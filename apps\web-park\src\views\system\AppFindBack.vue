<script name="AppFindBack" setup lang="ts">
import type { ComponentInternalInstance } from 'vue';

import { getCurrentInstance, onMounted, reactive, ref } from 'vue';

import {
  ElButton,
  ElCol,
  ElIcon,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElPagination,
  ElRow,
  ElTable,
  ElTableColumn,
} from 'element-plus';

import { AppApi, ParkParkApi } from '#/api';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const filterText = ref('');
const tableData = ref<any>([]);
const loading = ref(false);
const total = ref<number>(0);
const orignalData = ref<any>([]);
const multipleSelection = ref<any>([]);
const data = reactive<any>({
  queryParams: {
    page: 1,
    limit: 30,
    name: '',
  },
});

const loadSiteList = (appId: string) => {
  AppApi.AppApi.appParkListApi(appId).then((response) => {
    orignalData.value = response;
    // 初始化右侧列表的值
    multipleSelection.value = response;
  });
};
const loadCanSiteList = (params: {
  limit: number;
  name: string;
  page: number;
}) => {
  loading.value = true;
  params.page === undefined
    ? (params.page = 1)
    : (params.page = data.queryParams.page);
  params.limit === undefined
    ? (params.limit = 30)
    : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  ParkParkApi.getParkListApi(params).then((response) => {
    // if (response.success === true) {
    tableData.value = response.rows;
    total.value = Number(response.total);
    loading.value = false;
    // } else {
    //   ElMessage({
    //     message:
    //       response.detail_message === ''
    //         ? response.message
    //         : response.detail_message,
    //     type: 'error',
    //   });
    //   loading.value = false;
    // }
  });
};
const multipleTable = ref<any>();
onMounted(() => {
  // 加载已关联车场列表
  // loadSiteList();
  // 加载可关联车场列表
  loadCanSiteList(data.queryParams);
  for (let i = 0; i < orignalData.value.length; i++) {
    for (let j = 0; j < tableData.value.length; j++) {
      if (orignalData.value[i].park_id === tableData.value[j].park_id) {
        const T = proxy?.$refs.multipleTable as any;
        T.toggleRowSelection(tableData.value[j], true);
      }
    }
  }
});

const changeText = (val: string | undefined) => {
  if (val !== undefined && val.trim() !== '') {
    data.queryParams.name = val;
    data.queryParams.page = 1;
  } else {
    delete data.queryParams.name;
  }
  loadCanSiteList(data.queryParams);
};

const tableRowClassName = (row: { id: any }) => {
  for (let i = 0; i < orignalData.value.length; i++) {
    if (orignalData.value[i].park_id === row.id) {
      return 'success-row';
    }
  }
  return '';
};
const resetChecked = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage({
      message: '当前暂无已选项，请先添加！',
      type: 'warning',
    });
  } else {
    ElMessageBox.confirm('确定要清空已选项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        multipleSelection.value = [];
        orignalData.value = [];
        const T = proxy?.$refs.multipleTable as any;
        T.clearSelection();
      })
      .catch(() => {});
  }
};
// 左侧点选
const handleCurrentChange = (row: { id: undefined; name: undefined }) => {
  // eslint-disable-next-line array-callback-return
  const res = orignalData.value.some((item: { park_id: undefined }) => {
    if (item.park_id === row.id) {
      return true;
    }
  });
  if (res) {
    // proxy.$refs.multipleTable.toggleRowSelection(row, false); // 点击选中
    const T = proxy?.$refs.multipleTable as any;
    T.toggleRowSelection(row, false);
    for (let i = 0; i < multipleSelection.value.length; i++) {
      if (multipleSelection.value[i] === row.id) {
        multipleSelection.value.splice(i, 1);
      }
    }
    for (let i = 0; i < orignalData.value.length; i++) {
      if (orignalData.value[i].park_id === row.id) {
        orignalData.value.splice(i, 1);
      }
    }
  } else {
    const arr = {
      park_id: undefined,
      park_name: undefined,
    };
    arr.park_id = row.id;
    arr.park_name = row.name;
    const T = proxy?.$refs.multipleTable as any;
    T.toggleRowSelection(row, true); // 点击选中
    multipleSelection.value.push(row.id);
    orignalData.value.push(arr);
  }
};
const handleCheckboxChange = (val: any) => {
  multipleSelection.value = val;
};
// 右侧删除
const removeItem = (id: any) => {
  for (let i = 0; i < orignalData.value.length; i++) {
    if (orignalData.value[i].park_id === id) {
      orignalData.value.splice(i, 1);
    }
  }
};
// 关闭弹框
const cancel = () => {
  proxy?.$emit('authCharge', 'false');
};
// 关闭弹框渲染input
const submitTableData = () => {
  if (orignalData.value.length < 0 || orignalData.value.length === 0) {
    ElMessage({
      message: '当前暂无已选项，请先添加！',
      type: 'warning',
    });
    return false;
  }
  proxy?.$emit('renderTableInput', orignalData.value);
  proxy?.$emit('authCharge', 'false');
};
const handleSizeChange = (val: any) => {
  data.queryParams.limit = val;
  loadCanSiteList(data.queryParams);
};
const handleCurrentPageChange = (val: any) => {
  data.queryParams.page = val;
  loadCanSiteList(data.queryParams);
};
defineExpose({
  loadSiteList,
  submitTableData,
  cancel,
});
</script>

<template>
  <div>
    <ElRow :gutter="10" style="overflow: hidden">
      <ElCol :span="16">
        <div>
          <p class="tips">选择</p>
          <div
            style="
              height: 550px;
              padding: 20px;
              margin-top: 10px;
              border: 1px solid #dcdfe6;
            "
          >
            <ElInput
              v-model="filterText"
              clearable
              placeholder="输入车场名称进行过滤"
              @input="changeText"
            />
            <div
              style="
                height: 440px;
                margin-top: 10px;
                margin-bottom: 10px;
                overflow: auto;
              "
            >
              <ElTable
                ref="multipleTable"
                :data="tableData"
                :header-cell-style="{ background: '#f9f9f9', color: '#606266' }"
                :row-class-name="tableRowClassName"
                border
                style="width: 100%"
                tooltip-effect="dark"
                v-loading="loading"
                @row-click="handleCurrentChange"
                @selection-change="handleCheckboxChange"
              >
                <ElTableColumn align="center" label="车场名称" prop="name" />
                <ElTableColumn
                  align="center"
                  label="车场状态"
                  prop="state_display"
                />
                <ElTableColumn
                  align="center"
                  label="所在城市"
                  prop="city_name"
                />
              </ElTable>
            </div>
            <ElPagination
              :current-page="data.queryParams.page"
              :page-size="data.queryParams.limit"
              :page-sizes="[10, 30, 50, 100]"
              :total="total"
              background
              class="table-pagination"
              layout="total, sizes, prev, pager, next, jumper"
              @current-change="handleCurrentPageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
      </ElCol>
      <ElCol :span="8">
        <div class="grid-content">
          <p class="tips">
            <span>已选</span>
            <ElButton
              class="reset-checked"
              link
              type="primary"
              @click="resetChecked"
            >
              清空
            </ElButton>
          </p>
          <div
            style="
              height: 550px;
              padding: 20px;
              margin-top: 10px;
              border: 1px solid #dcdfe6;
            "
          >
            <ul>
              <li
                v-for="(item, key) in orignalData"
                :key="key"
                :data-id="item.park_id"
              >
                <span>{{ item.park_name }}</span>
                <ElIcon
                  :size="16"
                  class="icon"
                  @click="removeItem(item.park_id)"
                >
                  <span class="icon-[ep--remove-filled]"></span>
                </ElIcon>
              </li>
            </ul>
          </div>
        </div>
      </ElCol>
      <!-- <ElCol :span="24">
        <div style="margin-top: 10px; text-align: right">
          <ElButton @click="cancel"> 取 消 </ElButton>
          <ElButton type="primary" @click="submitTableData"> 确 定 </ElButton>
        </div>
      </ElCol> -->
    </ElRow>
  </div>
</template>
<style scoped lang="scss">
.grid-conten {
  padding: 0 10px;
}

.tips {
  display: flex;
  justify-content: space-between;
  margin-top: 0;
  overflow: hidden;
}

.reset-checked {
  padding: 0;
  color: #1e9fff;
  cursor: pointer;
}

.reset-checked:hover {
  color: #ff6c65;
}

ul {
  height: 564px;
  padding: 0;
  margin: 0;
  overflow: auto;
  list-style: none;
}

.icon {
  margin-top: 5px;
  color: #999;
  cursor: pointer;
}

.icon:hover {
  color: #ff6c65;
  text-shadow: #ff6c65 0 0 2px;
}

ul li {
  display: flex;
  justify-content: space-between;
  padding-left: 10px;
  line-height: 28px;
}

ul li:hover {
  background-color: #f5f7fa;
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
