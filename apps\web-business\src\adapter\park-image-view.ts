import { createVNode, ref, render } from 'vue';

import { ElImageViewer } from 'element-plus';

const viewerState = ref(false);

/**
 * 图片预览器Hook
 * @description 直接使用Url打开图片
 * @demo
 *  // 引入HooK
 *  import useParkImageView from '#/adapter/park-image-view';
 *  // 导出查看方法 和 开启状态
 *  const [parkImageView, viewerState] = useParkImageView();
 *  // 使用查看方法
 *  parkImageView('https://xxx.com/xxx.png');
 */

export default function useParkImageView() {
  /**
   * 图片预览
   * @param {string} imageUrl 图片地址
   */
  const parkImageView = (imageUrl: string) => {
    if (viewerState.value) {
      return;
    }
    const container = document.createElement('div');
    document.body.append(container);
    const vnode = createVNode(ElImageViewer, {
      'url-list': [imageUrl],
      onClose: () => {
        render(null, container);
        container.remove();
        viewerState.value = false;
      },
    });
    render(vnode, container);
    viewerState.value = true;
  };

  return [parkImageView, viewerState] as const;
}
