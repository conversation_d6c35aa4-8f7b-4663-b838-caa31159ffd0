<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSFinance } from '#/api/park/finance';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { FinanceApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'ShiftReport', // 交接班报表列表
});

interface IFormValues {
  on_off_time?: string[];
  shift_name: string;
}

/**
 * 业务变量
 */
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      SRRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  SRRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  SRRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'shift_name',
      label: '当班人：',
      componentProps: {
        clearable: true,
        placeholder: '请输入当班人',
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [],
      fieldName: 'on_off_time',
      label: '班次时间：',
      componentProps: {
        clearable: true,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '班次时间开始日期',
        endPlaceholder: '班次时间结束日期',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description交接班报表管理列表
 */
const gridOptions: VxeGridProps<NSFinance.IPagingShiftHandoverRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    {
      title: '班次信息',
      children: [
        { field: 'park_name', title: '停车场名称', minWidth: 100 },
        { field: 'shift_name', title: '当班人', minWidth: 100 },
        {
          field: 'on_off_time',
          slots: { header: 'on_off_time_header', default: 'on_off_time' },
          sortable: true,
          minWidth: 200,
        },
        {
          field: 'should_pay_money',
          title: '应收',
          sortable: true,
          minWidth: 100,
        },
        { field: 'payed_money', title: '实收', sortable: true, minWidth: 100 },
      ],
    },
    {
      title: '电子支付',
      children: [
        {
          field: 'ali_money',
          title: '支付宝支付',
          sortable: true,
          minWidth: 100,
        },
        { field: 'wx_money', title: '微信支付', sortable: true, minWidth: 100 },
        { field: 'etc_money', title: 'ETC支付', sortable: true, minWidth: 100 },
      ],
    },
    {
      title: '现金收费',
      children: [
        {
          field: 'cash_money',
          title: '现金支付',
          sortable: true,
          minWidth: 100,
        },
        {
          field: 'special_money',
          title: '特殊处理',
          sortable: true,
          minWidth: 100,
        },
      ],
    },
    {
      title: '异常处理损失',
      children: [
        {
          field: 'special_loss_money',
          title: '特殊处理损失',
          sortable: true,
          minWidth: 100,
        },
        {
          field: 'flush_loss_money',
          title: '被冲车辆损失',
          sortable: true,
          minWidth: 100,
        },
      ],
    },
    { field: 'debate_money', title: '优免抵扣', sortable: true, minWidth: 100 },
    { field: 'manual_money', title: '手动抬杆', sortable: true, minWidth: 100 },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPagingShiftHandoverRecords,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [ShiftReportTable, SRRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取交接班报表列表数据
 */
async function getPagingShiftHandoverRecords(
  { page }: any,
  formValues: IFormValues,
) {
  let params;
  if (isReset.value) {
    params = {
      park_id: '',
      park_name: '',
      shift_name: '',
      on_time: '',
      off_time: '',
    };
    isReset.value = false;
  } else {
    params = {
      ...formValues,
      park_id: selectParkCheck.value?.park_id || '',
      park_name: selectParkCheck.value?.park_name || '',
      on_time: formValues!.on_off_time?.[0]
        ? dayjs(formValues!.on_off_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
        : '',
      off_time: formValues!.on_off_time?.[1]
        ? dayjs(formValues!.on_off_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
        : '',
    };
    delete params.on_off_time;
  }
  try {
    const res = await FinanceApi.getPagingShiftHandoverRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

const loadData = () => {
  SRRef.setLoading(true);
  setTimeout(() => {
    SRRef.setLoading(false);
    SRRef.query();
  }, 200);
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    SRRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--交接班报表管理表格 -->
    <ShiftReportTable table-title="交接班报表列表">
      <!-- 自定义-班次开始结束时间 -->
      <template #on_off_time_header>
        <span>班次开始时间<br />班次结束时间</span>
      </template>
      <template #on_off_time="{ row }">
        <span>{{ row.on_time }}<br />{{ row.off_time }}</span>
      </template>
      <!-- 自定义-长租有效期列 -->
      <template #valid_time="{ row }">
        <span>{{ row.valid_start_time }}~{{ row.valid_end_time }}</span>
      </template>
    </ShiftReportTable>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
