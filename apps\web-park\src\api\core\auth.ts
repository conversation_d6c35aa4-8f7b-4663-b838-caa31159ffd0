import type { UserEntity } from '@vben/types';

import { baseRequestClient, requestClient } from '#/api/request';

export namespace NSCoreAuth {
  /** 登录接口参数 */
  export interface ILoginParams {
    username?: string;
    password?: string;
    captcha?: string;
    key?: string;
  }

  /** 登录接口返回值 */
  export interface ILoginResult {
    accessToken: string;
  }

  export interface IRefreshTokenResult {
    data: string;
    status: number;
  }

  /** 获取验证码接口返回值 */
  export interface IGetCaptchaResult {
    captcha: string;
    key: string;
  }
}

/**
 * 账号密码+图形验证码
 * @description 一般用于非万达用户登录
 */
export async function loginApi(data: NSCoreAuth.ILoginParams) {
  return requestClient.post<any>('/console/auth/login', data);
}

/**
 * 获取验证码
 */
export async function getCaptchaApi() {
  return requestClient.get<NSCoreAuth.IGetCaptchaResult>(
    `/console/auth/getCaptcha?t=${Date.now()}`,
  );
}

/**
 * 验证CAS过来的Token 获取用户信息进行渐进式登录
 * 需要携带CAS的Token
 */
export async function validateCASTokenApi(token: string) {
  return requestClient.post<any>(
    `/console/auth/loginByIAM?t=${Date.now()}`,
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );
}

/**
 * 获取用户信息
 */
export async function getUserInfoByTokenApi() {
  return requestClient.post<UserEntity>('/console/auth/getUserByToken');
}

/**
 * 刷新accessToken 未实现
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<NSCoreAuth.IRefreshTokenResult>(
    '/auth/refresh',
    {
      withCredentials: true,
    },
  );
}

/**
 * 退出登录 未实现
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码 未实现
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}
