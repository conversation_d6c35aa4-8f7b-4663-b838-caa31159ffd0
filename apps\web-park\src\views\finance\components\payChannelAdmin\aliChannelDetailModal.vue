<script lang="ts" setup>
import type { NSPay } from '#/api';

import { ref, toRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm, z } from '#/adapter/form';

defineOptions({
  name: 'AliChannelDetailModal', // 支付宝支付渠道详情弹窗
});

interface option {
  key: string;
  label: string;
  value: number;
}

const formState = ref<NSPay.IPayChannelDetailResult>({});
const stateOptions = ref<option[]>([]);
const editSchema = [
  {
    component: 'Input',
    fieldName: 'channel_name',
    componentProps: {
      placeholder: '请输入支付渠道名称',
      clearable: true,
    },
    label: '支付渠道名称',
    rules: z.string().refine((val) => val, {
      message: '请输入支付渠道名称',
    }),
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: stateOptions,
    },
    fieldName: 'pay_channel_state',
    label: '支付渠道状态',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'ali_app_id',
    componentProps: {
      placeholder: '请输入阿里应用ID',
      clearable: true,
    },
    label: '阿里应用ID',
    rules: z.string().refine((val) => val, {
      message: '请输入阿里应用ID',
    }),
  },
  {
    component: 'Input',
    fieldName: 'ali_private_key',
    componentProps: {
      placeholder: '请输入私钥',
      clearable: true,
    },
    label: '私钥',
    rules: z.string().refine((val) => val, {
      message: '请输入私钥',
    }),
  },
  {
    component: 'Input',
    fieldName: 'ali_public_key',
    componentProps: {
      placeholder: '请输入公钥',
      clearable: true,
    },
    label: '公钥',
    rules: z.string().refine((val) => val, {
      message: '请输入公钥',
    }),
  },
];
const viewSchema = [
  {
    component: 'Text',
    fieldName: 'pay_channel_detail',
    label: '支付渠道',
  },
  {
    component: 'Text',
    fieldName: 'channel_name_detail',
    label: '支付渠道名称',
  },
  {
    component: 'Text',
    fieldName: 'pay_channel_state_display_detail',
    label: '支付渠道状态',
  },
  {
    component: 'Text',
    fieldName: 'ali_app_id_detail',
    label: '阿里应用ID',
  },
  {
    component: 'Text',
    fieldName: 'ali_private_key_detail',
    label: '私钥',
  },
  {
    component: 'Text',
    fieldName: 'ali_public_key_detail',
    label: '公钥',
  },
];

const [AliChannelDetailForm, ACDFRef] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    colon: true,
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  showDefaultActions: false,
  schema: [],
  wrapperClass: 'grid-cols-1',
});

/**
 * 退款详情弹窗配置
 */
const [Modal, ModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const shareData = ModalApi.getData<Record<string, any>>();
      const { type, formData, channelData } = shareData;
      const { states } = channelData;
      stateOptions.value = states;
      formState.value = formData;
      if (type === 'view') {
        ACDFRef.setState({ schema: viewSchema });
      } else {
        ACDFRef.setState({ schema: editSchema });
        ACDFRef.setValues({
          ...formData,
        });
      }
    } else {
      ACDFRef.resetForm();
    }
  },
  onConfirm: async () => {
    const res = await ACDFRef.validate();
    if (!res.valid) return;
    const shareData = ModalApi.getData<Record<string, any>>();
    const data = await ACDFRef.getValues();
    if (shareData.confirmFn) {
      shareData.confirmFn(toRaw(data));
    }
  },
});
</script>

<template>
  <!-- 支付宝支付渠道详情弹窗 -->
  <Modal :close-on-click-modal="false" class="w-[1200px]">
    <AliChannelDetailForm>
      <template #pay_channel_detail>
        <span>支付宝</span>
      </template>
      <template #channel_name_detail>
        {{ formState.channel_name }}
      </template>
      <template #pay_channel_state_display_detail>
        {{ formState.pay_channel_state_display }}
      </template>
      <template #ali_app_id_detail>
        {{ formState.ali_app_id }}
      </template>
      <template #ali_private_key_detail>
        <span class="break-all">
          {{ formState.ali_private_key }}
        </span>
      </template>
      <template #ali_public_key_detail>
        <span class="break-all">
          {{ formState.ali_public_key }}
        </span>
      </template>
    </AliChannelDetailForm>
  </Modal>
</template>
