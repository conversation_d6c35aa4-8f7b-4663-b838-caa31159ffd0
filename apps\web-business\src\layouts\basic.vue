<script lang="ts" setup>
import type { Component } from 'vue';

import type { NotificationItem } from '@vben/layouts';

import { computed, ref, watch } from 'vue';

import { AuthenticationLoginExpiredModal, useVbenModal } from '@vben/common-ui';
import { useWatermark } from '@vben/hooks';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';

import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';

import ChangePawModal from './changePawModal.vue';

const notifications = ref<NotificationItem[]>([]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() => notifications.value.length > 0);

const avatar = computed(() => {
  return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: ChangePawModal,
});

async function handleLogout() {
  await authStore.logout(false);
}

const openChangePasswordModal = (): void => {
  modalApi.open();
};

const menus = computed(() => [
  {
    handler: (): void => {
      openChangePasswordModal();
    },
    icon: 'ep:key' as unknown as Component,
    text: '修改密码',
  },
]);

watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout @clear-preferences-and-logout="handleLogout">
    <template #preferences>
      <div></div>
    </template>
    <template #user-dropdown>
      <Modal />
      <UserDropdown
        :avatar
        :description="`${userStore.userInfo?.userEntity.park_name}-${userStore.userInfo?.userEntity.name}-${userStore.userInfo?.userEntity.address}`"
        :menus
        :tag-text="userStore.userInfo?.userEntity.merchant_type_desc"
        :text="`${userStore.userInfo?.realName}(${userStore.userInfo?.userEntity.username})`"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <Notification :dot="showDot" :notifications="notifications" />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>
</template>
