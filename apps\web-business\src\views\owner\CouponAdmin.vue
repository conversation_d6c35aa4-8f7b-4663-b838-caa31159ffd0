<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton, ElTag } from 'element-plus';
import QRCode from 'qrcode';

import useParkImageView from '#/adapter/park-image-view';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PlatformApi } from '#/api';
import { CommonApi, CommonModule } from '#/api/common';

import CouponAdminAdd from './CouponAdminComponents/CouponAdminAdd.vue';

const VITE_DOMAIN = import.meta.env.VITE_DOMAIN;

// 查询参数类型
interface IFormValues {
  name: string;
  coupon_meta_name: string;
  types: number[];
  coupon_states: number[];
  draw_states: number[];
  audit_modes: number[];
}

// 优免卷列表行数据类型
interface IRowType {
  id: string;
  name: string;
  coupon_meta_name: string;
  type_desc: string;
  coupon_data: string;
  coupon_valid: string;
  coupon_state_desc: string;
  valid: string;
  draw_state_desc: string;
  audit_mode_desc: string;
  total_count: string;
  used_count: string;
  serial_no: string;
}
const [parkImageView] = useParkImageView();
// 优免卷类型 Options
const searchTypesOptions = ref<{ label: string; value: number }[]>([]);
// 优免卷类状态 Options
const searchCouponStatesOptions = ref<{ label: string; value: number }[]>([]);
// 审核类型 Options
const searchAuditModesOptions = ref<{ label: string; value: number }[]>([]);
// 领取状态 Options
const searchDrawStatesOptions = ref<{ label: string; value: number }[]>([]);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'name',
      label: '领卷用途：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'coupon_meta_name',
      label: '优免卷名称：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'types',
      label: '优免卷类型：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchTypesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'coupon_states',
      label: '优免卷状态：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchCouponStatesOptions,
      },
    },

    {
      component: 'Select',
      fieldName: 'draw_states',
      label: '领取状态：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchDrawStatesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'audit_modes',
      label: '审核类型：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchAuditModesOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    { title: '序号', type: 'seq', minWidth: 60, width: 60 },
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'name',
      title: '领卷用途',
      minWidth: 100,
    },
    {
      field: 'coupon_meta_name',
      title: '优免卷名称',
      minWidth: 100,
    },
    {
      field: 'type_desc',
      title: '优免卷类型',
      minWidth: 100,
    },
    {
      field: 'coupon_data',
      title: '优惠内容',
      slots: { default: 'coupon_data' },
      minWidth: 100,
    },
    {
      field: 'coupon_valid',
      title: '有效期',
      slots: { default: 'valid_time' },
      minWidth: 180,
    },
    {
      field: 'coupon_state_desc',
      title: '优免卷状态',
      slots: { default: 'coupon_state_desc' },
      minWidth: 100,
    },
    {
      field: 'valid',
      title: '领取时间',
      slots: { default: 'valid' },
      minWidth: 280,
    },
    {
      field: 'draw_state_desc',
      title: '领取状态',
      minWidth: 100,
    },
    {
      field: 'audit_mode_desc',
      title: '审核类型',
      minWidth: 100,
    },
    {
      field: 'total_count',
      title: '限制领卷人数',
      minWidth: 100,
    },
    {
      field: 'used_count',
      title: '已领取人数',
      minWidth: 100,
    },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 150,
      width: 150,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: getCouponList,
    },
  },
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 优免卷类型 优免卷状态 审核类型 领取状态
 */
const initSearchData = () => {
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.COUPON, [
    { enum_key: 'types', enum_value: 'EnumCouponMetaType' },
    { enum_key: 'coupon_states', enum_value: 'EnumMerchantCouponState' },
  ]).then((res) => {
    searchTypesOptions.value = res.types;
    searchCouponStatesOptions.value = res.coupon_states;
  });

  CommonApi.findEnumsApi(CommonModule.EnumModuleType.PLATFORM, [
    { enum_key: 'audit_modes', enum_value: 'EnumAuditMode' },
    { enum_key: 'draw_states', enum_value: 'EnumDrawQrcodeDrawState' },
  ]).then((res) => {
    searchAuditModesOptions.value = res.audit_modes;
    searchDrawStatesOptions.value = res.draw_states;
  });
};

/**
 * 获取优免卷列表数据
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 * @returns {Promise<{items: IRowType[], total: number}>} 优免卷列表和总数
 */
async function getCouponList({ page }: any, formValues: IFormValues) {
  // 查询参数格式化
  const params = {
    name: formValues.name,
    coupon_meta_name: formValues.coupon_meta_name,
    types: formValues.types,
    coupon_states: formValues.coupon_states,
    draw_states: formValues.draw_states,
    audit_modes: formValues.audit_modes,
  };
  try {
    const res = await PlatformApi.getPagingDrawCodesApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 装填颜色
 * @param {number} state 状态
 * @returns {string} 颜色
 */
const getStateColor = (state: number, status: number) => {
  if (status === 1) {
    return 'danger';
  }
  switch (state) {
    case 0: {
      return 'info';
    }
    case 1: {
      return 'primary';
    }
    case 2: {
      return 'danger';
    }
  }
};

/**
 * 注册添加领卷码弹窗
 * @description 配置connectedComponent
 */
const [CouponAdminAddModal, CAddModalRef] = useVbenModal({
  connectedComponent: CouponAdminAdd,
});

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};

/**
 * 注册优免卷列表表格
 * @description 启用表格 配置formOptions与gridOptions
 */
const [DrawCodesTable, DTRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const addDrawCodeAction = () => {
  CAddModalRef.setData({
    query: () => {
      DTRef.query();
    },
  });
  CAddModalRef.open();
};

const viewQrcodeAction = (row: IRowType) => {
  const h5Url1 = `${VITE_DOMAIN}/h5/getCoupon.html?serial_no=${row.serial_no}`;
  QRCode.toDataURL(h5Url1, { width: 600 }).then((url) => {
    parkImageView(url);
  });
};

const stopAction = (row: IRowType) => {
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '领卷码停用',
    description: '确定要停用该领卷码吗？',
    onConfirm: async () => {
      await PlatformApi.setDisableDrawCodeApi(Number(row.id));
      DTRef.query();
    },
  });
};

const enableAction = (row: IRowType) => {
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '领卷码启用',
    description: '确定要启用该领卷码吗？',
    onConfirm: async () => {
      await PlatformApi.setEnableDrawCodeApi(Number(row.id));
      DTRef.query();
    },
  });
};
// 显示优免券内容
const showCouponData = (row: any) => {
  if (row.coupon_meta_param.coupon_type === 1) {
    return `${row.coupon_meta_param.derate_hour}小时`;
  }
  if (row.coupon_meta_param.coupon_type === 2) {
    return `${row.coupon_meta_param.derate_money}元`;
  }
  if (row.coupon_meta_param.coupon_type === 3) {
    return `${row.coupon_meta_param.discount_ratio}折`;
  }
  if (row.coupon_meta_param.coupon_type === 4) {
    return `全免券`;
  }
  if (row.coupon_meta_param.coupon_type === 5) {
    return `${
      row.coupon_meta_param.start_time
    }-${row.coupon_meta_param.end_time}`;
  }
};
onMounted(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <CouponAdminAddModal />
    <!-- 二次确认弹窗 -->
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <!-- 优免卷列表表格 -->
    <DrawCodesTable>
      <template #toolbar-actions>
        <ElButton type="primary" @click="addDrawCodeAction">
          添加领卷码
        </ElButton>
      </template>
      <template #coupon_data="{ row }">
        {{ showCouponData(row) }}
      </template>
      <template #valid_time="{ row }">
        {{ row.coupon_valid_start_time }} - {{ row.coupon_valid_end_time }}
      </template>
      <template #coupon_state_desc="{ row }">
        <ElTag :type="getStateColor(row.coupon_state, row?.audit_state)">
          {{
            row.audit_state
              ? row.audit_state_desc === '已通过'
                ? '进行中'
                : '审核中'
              : row.coupon_state_desc
          }}
        </ElTag>
      </template>
      <template #valid="{ row }">
        {{ row.valid_start_time }} - {{ row.valid_end_time }}
      </template>
      <template #actions="{ row }">
        <div v-if="row.audit_state !== 1">
          <div
            class="flex items-center justify-evenly"
            v-if="row.coupon_state === 1"
          >
            <ElButton link type="primary" @click="viewQrcodeAction(row)">
              查看二维码
            </ElButton>
            <ElButton
              link
              type="danger"
              @click="stopAction(row)"
              v-if="row.state === 1"
            >
              停用
            </ElButton>
            <ElButton link type="primary" @click="enableAction(row)" v-else>
              启用
            </ElButton>
          </div>
        </div>
      </template>
    </DrawCodesTable>
  </Page>
</template>
