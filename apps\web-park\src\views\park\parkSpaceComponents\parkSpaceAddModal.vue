<script lang="ts" setup>
import type { NSEmployee } from '#/api';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  type FormInstance,
} from 'element-plus';

import ParkSelectModalComponent from '#/components/park-select-modal.vue';

const baseDto = {
  park_id: '',
  park_name: '',
  park_region_id: '',
  code: '',
  type: '',
  property: '',
  picture: '',
  picUrl: '',
};
// 表单实例
const formRef = ref<FormInstance>();
// 表单数据
const formState = ref(baseDto);

// 表单验证规则
const rules = ref({
  park_name: [{ required: true, message: '请选择停车场', trigger: 'blur' }],
  park_region_id: [{ required: true, message: '请选择子场', trigger: 'blur' }],
  code: [{ required: true, message: '请填写车位编号', trigger: 'blur' }],
  type: [{ required: true, message: '请选择车位类型', trigger: 'blur' }],
  property: [{ required: true, message: '请选择车位属性', trigger: 'blur' }],
});

const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});
const openParkSelectModal = () => {
  // PSModalRef.setState({ title: '关停车场' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: [
      {
        park_id: formState.value.park_id,
        park_name: formState.value.park_name,
      },
    ],
    confirmFn: (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      // 更新员工的车场授权
      if (newSelectArray.length > 0) {
        const { park_id, park_name } = newSelectArray[0]!;
        formState.value.park_id = park_id;
        formState.value.park_name = park_name;
      }
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

/**
 * 注册弹窗
 */
const [Modal] = useVbenModal({
  draggable: true,
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      // 获取停车场列表
    }
  },
  onConfirm: () => {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        // 添加车位
      }
    });
  },
});
</script>
<template>
  <!-- 添加车位弹窗 -->
  <Modal
    :close-on-click-modal="false"
    :fullscreen-button="false"
    class="w-[650px]"
    title="添加车位"
  >
    <ElForm
      ref="formRef"
      :model="formState"
      :rules="rules"
      class="p-4"
      label-position="right"
      label-width="auto"
    >
      <ElFormItem label="关联停车场：" prop="park_name">
        <ElInput
          v-model="formState.park_name"
          placeholder="请选择车场"
          readonly
          @click="openParkSelectModal"
        />
      </ElFormItem>
      <ElFormItem label="关联子场：" prop="park_region_id">
        <ElSelect
          v-model="formState.park_region_id"
          placeholder="请选择子场"
          readonly
        />
      </ElFormItem>
      <ElFormItem label="车位编号：" prop="code">
        <ElInput
          v-model="formState.code"
          clearable
          placeholder="请填写车位编号"
        />
      </ElFormItem>
      <ElFormItem label="车位类型：" prop="type">
        <ElSelect v-model="formState.type" clearable placeholder="请选择" />
      </ElFormItem>
      <ElFormItem label="车位属性：" prop="property">
        <ElSelect v-model="formState.property" clearable placeholder="请选择" />
      </ElFormItem>
    </ElForm>
  </Modal>
  <!-- 停车场选择弹窗 -->
  <ParkSelectModal />
</template>
