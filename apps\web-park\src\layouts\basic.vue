<script lang="ts" setup>
import type { Component } from 'vue';

import type { NotificationItem } from '@vben/layouts';
import type { BasicDict } from '@vben/types';

import { computed, onMounted, ref, watch } from 'vue';

import { AuthenticationLoginExpiredModal, useVbenModal } from '@vben/common-ui';
import { useWatermark } from '@vben/hooks';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';

import { ParkAuditApi } from '#/api';
import { useAuthStore } from '#/store';
import LoginForm from '#/views/_core/authentication/login.vue';

import SelRoleModal from './selRoleModal.vue';

const notifications = ref<NotificationItem[]>([]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() => notifications.value.length > 0);

const avatar = computed(() => {
  return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: SelRoleModal,
});

// 当前角色名称
const currentRoleName = computed(() => {
  return userStore.userRoleList.find(
    (item: BasicDict) => item.id === userStore.userInfo?.userEntity.role_id,
  )?.name;
});

async function getWaitAuditCount() {
  const count = await ParkAuditApi.getWaitAuditCountApi();
  if (count > 0) {
    const list = await ParkAuditApi.getWaitAuditListApi({
      page: 1,
      limit: 30,
      audit_states: ['0'],
    });

    notifications.value = list.rows.map((item) => ({
      avatar: 'https://avatar.vercel.sh/rauchg.svg?text=待审批',
      date: item.updated_at,
      message: item.title,
      isRead: false,
      title: item.type_desc,
    }));
  }
}
onMounted(() => {
  getWaitAuditCount();
});

async function handleLogout() {
  await authStore.logout(false);
}

const openChangeRoleModal = (): void => {
  modalApi.open();
};

const openChangePasswordModal = (): void => {
  modalApi.open();
};

const menus = computed(() => [
  {
    handler: () => {
      openChangeRoleModal();
    },
    icon: 'ep:switch' as unknown as Component,
    text: '切换用户角色',
  },
  {
    handler: (): void => {
      openChangePasswordModal();
    },
    icon: 'ep:key' as unknown as Component,
    text: '修改密码',
  },
]);

watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout @clear-preferences-and-logout="handleLogout">
    <template #preferences>
      <div></div>
    </template>
    <template #user-dropdown>
      <Modal />
      <UserDropdown
        :avatar
        :description="currentRoleName"
        :menus
        :tag-text="userStore.userInfo?.username"
        :text="userStore.userInfo?.realName"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <Notification :dot="showDot" :notifications="notifications" />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>
</template>
