<script setup lang="ts">
/**
 * @description 常见问题组件
 * @component CommonProblem
 *
 * 功能：
 * 1. 展示常见问题
 * 2. 响应式布局适配
 */

// 定义数据
const pageData = [
  {
    title: '惠达停车系统用户使用手册V2.3',
    id: '2',
  },
  // {
  //   title: '关于惠达停车系统V2.3',
  //   id: '3',
  // },
];
</script>

<template>
  <div class="mt-3 h-full space-y-2 overflow-y-auto">
    <div v-for="(item, index) in pageData" :key="index">
      <a
        href="/user_manual/user_manual_V2.3.docx"
        :download="`${item.title}.docx`"
        class="block cursor-pointer truncate text-[#5570F1] hover:underline"
      >
        {{ item.title }}
      </a>
    </div>
  </div>
</template>
