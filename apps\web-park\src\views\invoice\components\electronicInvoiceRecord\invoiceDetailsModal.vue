<script lang="ts" setup>
import type { NSInvoice } from '#/api';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';

defineOptions({
  name: 'InvoiceDetailsModal', // 查看发票明细弹窗
});

/**
 * 业务变量
 */
const formState = ref<NSInvoice.IPagingInvoiceRecordRow>({});
const basicSchema = [
  {
    component: 'Text',
    fieldName: 'prk_park_name',
    label: '停车场名称',
  },
  {
    component: 'Text',
    fieldName: 'money',
    label: '开票金额',
  },
  {
    component: 'Text',
    fieldName: 'state_desc',
    label: '开票状态',
  },
  {
    component: 'Text',
    fieldName: 'company_name',
    label: '开票公司',
  },
  {
    component: 'Text',
    fieldName: 'name',
    label: '会员昵称',
  },
  {
    component: 'Text',
    fieldName: 'mbr_mobile',
    label: '会员手机号',
  },
  {
    component: 'Text',
    fieldName: 'fee_type_desc',
    label: '费用类型',
  },
  {
    component: 'Text',
    fieldName: 'invoice_type_desc',
    label: '发票类型',
  },
  {
    component: 'Text',
    fieldName: 'title',
    label: '发票抬头',
  },
  {
    component: 'Text',
    fieldName: 'created_at',
    label: '申请时间',
  },
  {
    component: 'Text',
    fieldName: 'email',
    label: '电子邮箱',
  },
];
const extraSchema = [
  {
    component: 'Text',
    fieldName: 'taxpayer_identify_no',
    label: '发票税号',
  },
  {
    component: 'Text',
    fieldName: 'company_address',
    label: '公司地址',
  },
  {
    component: 'Text',
    fieldName: 'contact_mobile',
    label: '公司电话',
  },
  {
    component: 'Text',
    fieldName: 'company_bank_account',
    label: '公司开户行',
  },
  {
    component: 'Text',
    fieldName: 'account_no',
    label: '开户行账号',
  },
];

const [InvoiceDetailForm, IDFRef] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    colon: true,
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  showDefaultActions: false,
  schema: [],
  wrapperClass: 'grid-cols-1',
});

/**
 * 退款详情弹窗配置
 */
const [InvoiceDetailsModal, invoiceDetailsModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const shareData = invoiceDetailsModalApi.getData<Record<string, any>>();
      const { data } = shareData;
      formState.value = data;
      const actualSchema = cloneDeep(basicSchema);
      if ([1, 2].includes(data.title_type)) {
        actualSchema.splice(8, 0, {
          component: 'Text',
          fieldName: 'title_type_desc',
          label: '抬头类型',
        });
      }
      if (data.title_type === 1 || data.invoice_type === 2) {
        actualSchema.splice(-2, 0, ...extraSchema);
      }
      IDFRef.setState({ schema: actualSchema });
    } else {
      IDFRef.resetForm();
    }
  },
});
</script>

<template>
  <!-- 查看发票明细详情弹窗 -->
  <InvoiceDetailsModal :footer="false" class="w-[650px]" title="查看明细">
    <InvoiceDetailForm>
      <template #prk_park_name>
        {{ formState.prk_park_name || '无' }}
      </template>
      <template #money>
        {{ formState.money || '无' }}
      </template>
      <template #state_desc>
        {{ formState.state_desc || '无' }}
      </template>
      <template #company_name>
        {{ formState.company_name || '无' }}
      </template>
      <template #name>
        {{ formState.name || '无' }}
      </template>
      <template #mbr_mobile>
        {{ formState.mbr_mobile || '无' }}
      </template>
      <template #fee_type_desc>
        {{ formState.fee_type_desc || '无' }}
      </template>
      <template #invoice_type_desc>
        {{ formState.invoice_type_desc || '无' }}
      </template>
      <template #title_type_desc>
        {{ formState.title_type_desc || '无' }}
      </template>
      <template #title>
        {{ formState.title || '无' }}
      </template>
      <template #taxpayer_identify_no>
        {{ formState.taxpayer_identify_no || '无' }}
      </template>
      <template #company_address>
        {{ formState.company_address || '无' }}
      </template>
      <template #contact_mobile>
        {{ formState.contact_mobile || '无' }}
      </template>
      <template #company_bank_account>
        {{ formState.company_bank_account || '无' }}
      </template>
      <template #account_no>
        {{ formState.account_no || '无' }}
      </template>
      <template #created_at>
        {{ formState.created_at || '无' }}
      </template>
      <template #email>
        {{ formState.email || '无' }}
      </template>
    </InvoiceDetailForm>
  </InvoiceDetailsModal>
</template>
