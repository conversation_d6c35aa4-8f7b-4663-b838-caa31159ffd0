import { requestClient } from '#/api/request';

export namespace NSParkAudit {
  export interface IWaitAuditListParams {
    page: number;
    limit: number;
    audit_states: string[];
  }

  export interface IWaitAuditListResult {
    current_page: number;
    page_count: number;
    rows: any[];
    total: number;
  }
}

/**
 * 获取待审核数量用于右上角的消息展示
 */
export async function getWaitAuditCountApi() {
  return requestClient.post<number>(
    '/console/park/audit/biz/getWaitAuditCount',
    {},
  );
}

/**
 * 获取待审核列表
 */
export async function getWaitAuditListApi(
  params: NSParkAudit.IWaitAuditListParams,
) {
  return requestClient.post<NSParkAudit.IWaitAuditListResult>(
    '/console/park/audit/biz/pagingBizAudits',
    params,
  );
}
