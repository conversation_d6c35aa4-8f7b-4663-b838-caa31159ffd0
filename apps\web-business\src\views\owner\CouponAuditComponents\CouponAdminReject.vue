<script lang="ts" setup>
import type { FormInstance } from 'element-plus';

import type { NSPlatform } from '#/api';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElForm, ElFormItem, ElInput } from 'element-plus';

import { PlatformApi } from '#/api';

// 表单实例
const formRef = ref<FormInstance>();

const baseDto = {
  memo: '',
  id: '',
};

// 表单数据
const formState = ref(baseDto);

// 表单验证规则
const rules = ref({
  memo: [{ required: true, message: '请填写驳回原因', trigger: 'blur' }],
});

let query: Function | undefined = () => {};

/**
 * 注册弹窗
 */
const [Modal, ModalApi] = useVbenModal({
  draggable: true,
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formState.value = { ...baseDto };
      const shareData = ModalApi.getData<Record<string, any>>();
      formState.value.id = shareData.id;
      query = shareData.query;
    }
  },
  onConfirm: () => {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        const params: NSPlatform.IDrawCodeAuditRejectParams = {
          id: formState.value.id,
          memo: formState.value.memo,
        };
        await PlatformApi.setDrawCodeAuditRejectApi(params);
        ModalApi.close();
        query?.();
      }
    });
  },
});
</script>
<template>
  <Modal
    :close-on-click-modal="false"
    :fullscreen-button="false"
    title="驳回领卷审批"
    class="w-[650px]"
  >
    <ElForm
      ref="formRef"
      :model="formState"
      :rules="rules"
      class="p-4"
      label-position="right"
      label-width="auto"
    >
      <ElFormItem label="驳回原因：" prop="memo">
        <ElInput
          v-model="formState.memo"
          clearable
          placeholder="请填写驳回原因"
        />
      </ElFormItem>
    </ElForm>
  </Modal>
</template>
