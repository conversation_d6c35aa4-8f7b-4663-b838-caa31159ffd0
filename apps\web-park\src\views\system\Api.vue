<script name="ApiTable" lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
  ElSwitch,
  ElTag,
  ElText,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  ApiModuleApi,
  CommonApi,
  CommonModule,
  PermissionGroupApi,
} from '#/api';

const addFormRef = ref();
const editFormRef = ref();
const apiIds = ref<any>([]);
const methods = ref<any>([]);
const types = ref<any>([]);
const premissionGroupList = ref<any>([]);
const rules = {
  permission_group_id: [
    {
      required: true,
      message: '请选择权限组',
      trigger: 'blur',
    },
  ],
  permission_name: [
    {
      required: true,
      message: '请输入权限名称',
      trigger: 'blur',
    },
  ],
  permission_code: [
    {
      required: true,
      message: '请输入权限代码',
      trigger: 'blur',
    },
  ],
  name: [
    {
      required: true,
      message: '请输入接口名称',
      trigger: 'blur',
    },
  ],
  path: [
    {
      required: true,
      message: '请输入包路径',
      trigger: 'blur',
    },
  ],
  http_method: [
    {
      required: true,
      message: '请选择请求方式',
      trigger: 'blur',
    },
  ],
  url: [
    {
      required: true,
      message: '请输入URL地址',
      trigger: 'blur',
    },
  ],
  anonymous: [
    {
      required: true,
      message: '请选择是否需要权限',
      trigger: 'blur',
    },
  ],
};

interface IdataFromValues {
  permission_group_id: string;
  permission_name: string;
  permission_code: string;
  name: string;
  path: string;
  http_method: string;
  url: string;
  anonymous: string;
  id?: string;
  permission_id?: string;
  permission_group_name?: string;
}
interface IdataValues {
  form: IdataFromValues;
  updateForm: IdataFromValues;
}
const data = reactive<IdataValues>({
  form: {
    permission_group_id: '',
    permission_name: '',
    permission_code: '',
    name: '',
    path: '',
    http_method: '',
    url: '',
    anonymous: '1',
  },
  updateForm: {
    permission_group_id: '',
    permission_name: '',
    permission_code: '',
    name: '',
    path: '',
    http_method: '',
    url: '',
    anonymous: '1',
  },
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '填写接口名称',
      },
      defaultValue: '',
      fieldName: 'name',
      label: '接口名称',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: methods,
        placeholder: '请选择',
        filterable: true,
      },
      fieldName: 'http_method',
      label: '请求方式',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: premissionGroupList,
        placeholder: '请选择',
        filterable: true,
      },
      fieldName: 'permission_group_id',
      label: '权限组名称',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  // submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};
const initSelect = () => {
  const param = [
    { enum_key: 'methods', enum_value: 'EnumHttpMethodType' },
    { enum_key: 'types', enum_value: 'EnumPermissionType' },
  ];
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.SYSTEM, param).then(
    (response) => {
      methods.value = response.methods.map((item: any) => {
        return {
          key: item.key,
          label: item.key,
          value: item.value,
        };
      });
      types.value = response.types;
    },
  );
};
const getPremissionGroupList = () => {
  PermissionGroupApi.permissionGroupListApi().then((response) => {
    if (response) {
      premissionGroupList.value = response.map((item: any) => {
        return {
          ...item,
          label: item.group_name,
          value: item.id,
        };
      });
    }
  });
};

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
onMounted(() => {
  initSelect();
  getPremissionGroupList();
});

interface RowType {
  category: string;
  color: string;
  id: string;
  price: string;
  productName: string;
  releaseDate: string;
}

const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: '',
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },

  columns: [
    { align: 'left', title: '', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 60 },
    {
      field: 'permission_group_name',
      title: '权限组名',
      minWidth: 150,
      width: 150,
    },
    { field: 'permission_name', title: '权限名称', minWidth: 150, width: 150 },
    { field: 'permission_code', title: '权限代码', minWidth: 150, width: 150 },
    {
      field: 'permission_type_display',
      title: '权限类型',
      minWidth: 120,
      width: 120,
    },
    { field: 'name', title: '接口名称', minWidth: 150, width: 150 },
    { field: 'path', title: '包路径', minWidth: 220, width: 220 },
    {
      field: 'http_method_desc',
      title: '请求方法',
      minWidth: 120,
      width: 120,
      slots: { default: 'http_method_desc' },
    },
    { field: 'url', title: 'URL地址', minWidth: 230 },
    {
      field: 'anonymous',
      title: '是否需要权限',
      slots: { default: 'anonymous' },
      minWidth: 120,
      width: 120,
    },
    {
      slots: { default: 'actions' },
      title: '操作',
      field: 'actions',
      fixed: 'right',
      minWidth: 120,
      width: 120,
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formOptions) => {
        const { rows, total } = await ApiModuleApi.pagingApisApi({
          page: page.currentPage,
          limit: page.pageSize,
          http_method: formOptions.http_method,
          name: formOptions.name,
          permission_group_id: formOptions.permission_group_id,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
};

const [Grid, ETRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
/**
 * 新建Api
 */
const [ModalApi, modalApiApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    ApiModuleApi.createApiApi(data.form)
      .then(() => {
        ETRef.query();
        addFormRef.value.resetFields();
        modalApiApi.close();
      })
      .catch(() => {
        ETRef.query();
      });
  },
  onCancel: () => {
    addFormRef.value.resetFields();
    modalApiApi.close();
  },
});
const handleAddCreate = () => {
  data.form = {
    permission_group_id: '',
    permission_name: '',
    permission_code: '',
    name: '',
    path: '',
    http_method: '',
    url: '',
    anonymous: '1',
  };
  modalApiApi.open();
};

const table = ref<any>(null);

/**
 * 修改api
 */
const [ModalApiUpdate, modalApiUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    editFormRef.value.validate().then(() => {
      ApiModuleApi.updateApiApi(data.updateForm)
        .then(() => {
          ETRef.query();
          editFormRef.value.resetFields();
          modalApiUpdateApi.close();
        })
        .catch(() => {
          ETRef.query();
        });
    });
  },
  onCancel: () => {
    editFormRef.value.resetFields();
    modalApiUpdateApi.close();
  },
});
const handlerEdit = (id: any) => {
  ApiModuleApi.getApiByIdApi(id).then((response) => {
    if (response) {
      data.updateForm = {
        id: response.id,
        name: response.name,
        path: response.path,
        http_method: response.http_method,
        url: response.url,
        permission_id: response.permission_id,
        permission_name: response.permission_name,
        permission_code: response.permission_code,
        permission_group_id: response.permission_group_id,
        permission_group_name: response.permission_group_name,
        anonymous: `${response.anonymous}`,
      };
      modalApiUpdateApi.open();
    }
  });
};

const batchDel = (type: string) => {
  if (type === '批量') {
    apiIds.value = ETRef.grid.getCheckboxRecords();
  }
  if (apiIds.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的Api接口',
      type: 'warning',
    });
  } else {
    showConfirmModal({
      title: 'Api接口删除',
      description: '确定要删除吗？',
      onConfirm: async () => {
        const pushIds = [];
        for (let i = 0; i < apiIds.value.length; i++) {
          pushIds.push(Number.parseInt(apiIds.value[i].id));
        }
        const param = {
          ids: pushIds,
        };
        ApiModuleApi.deleteApisApi(param)
          .then(() => {
            ETRef.query();
          })
          .catch(() => {
            ETRef.query();
          });
      },
    });
  }
};
const delApi = (val: any) => {
  apiIds.value[0] = val;
  batchDel('单删');
};
</script>

<template>
  <Page auto-content-height>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <Grid ref="table">
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElButton type="primary" @click="handleAddCreate()">新 增</ElButton>
        <ElButton type="danger" @click="batchDel('批量')">批量删除</ElButton>
      </template>
      <template #anonymous="{ row }">
        <ElTag :type="row.anonymous === 1 ? 'success' : 'danger'">
          {{ row.anonymous === 1 ? '启用' : '禁用' }}
        </ElTag>
      </template>
      <template #http_method_desc="{ row }">
        {{ row.http_method_desc }}
      </template>
      <template #actions="{ row }">
        <ElButton link type="primary" @click="handlerEdit(row.id)">
          修改
        </ElButton>
        <ElButton link type="danger" @click="delApi(row)"> 删除 </ElButton>
      </template>
    </Grid>

    <ModalApi :fullscreen-button="false" class="w-[650px]" title="添加Api接口">
      <ElForm
        ref="addFormRef"
        :model="data.form"
        :rules="rules"
        label-width="120px"
      >
        <ElFormItem label="权限组" prop="permission_group_id">
          <ElSelect v-model="data.form.permission_group_id" style="width: 100%">
            <ElOption
              v-for="item in premissionGroupList"
              :key="item.id"
              :label="item.group_name"
              :value="item.id"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="权限名称" prop="permission_name">
          <ElInput v-model="data.form.permission_name" />
        </ElFormItem>
        <ElFormItem label="权限代码" prop="permission_code">
          <ElInput v-model="data.form.permission_code" />
        </ElFormItem>
        <ElFormItem label="接口名称" prop="name">
          <ElInput v-model="data.form.name" />
        </ElFormItem>
        <ElFormItem label="包路径" prop="path">
          <ElInput v-model="data.form.path" />
        </ElFormItem>
        <ElFormItem label="请求方式" prop="http_method">
          <ElSelect v-model="data.form.http_method" style="width: 100%">
            <ElOption
              v-for="item in methods"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="URL地址" prop="url">
          <ElInput v-model="data.form.url" />
        </ElFormItem>
        <ElFormItem label="是否需要权限" prop="anonymous">
          <ElSwitch
            v-model="data.form.anonymous"
            active-value="1"
            inactive-value="0"
          />
        </ElFormItem>
      </ElForm>
    </ModalApi>
    <ModalApiUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="修改Api接口"
    >
      <ElForm
        ref="editFormRef"
        :model="data.updateForm"
        :rules="rules"
        label-width="120px"
      >
        <ElFormItem label="权限组" prop="http_method">
          <ElSelect
            v-model="data.updateForm.permission_group_id"
            style="width: 100%"
          >
            <ElOption
              v-for="item in premissionGroupList"
              :key="item.id"
              :label="item.group_name"
              :value="item.id"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="权限名称" prop="permissionName">
          <ElInput v-model="data.updateForm.permission_name" />
        </ElFormItem>
        <ElFormItem label="权限代码" prop="path">
          <ElInput v-model="data.updateForm.permission_code" />
        </ElFormItem>
        <ElFormItem label="接口名称" prop="name">
          <ElInput v-model="data.updateForm.name" />
        </ElFormItem>
        <ElFormItem label="包路径" prop="path">
          <ElInput v-model="data.updateForm.path" />
        </ElFormItem>
        <ElFormItem label="请求方式" prop="http_method">
          <ElSelect v-model="data.updateForm.http_method" style="width: 100%">
            <ElOption
              v-for="item in methods"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="URL地址" prop="url">
          <ElInput v-model="data.updateForm.url" />
        </ElFormItem>
        <ElFormItem label="是否需要权限" prop="anonymous">
          <ElSwitch
            v-model="data.updateForm.anonymous"
            active-value="1"
            inactive-value="0"
          />
        </ElFormItem>
      </ElForm>
    </ModalApiUpdate>
  </Page>
</template>
<style lang="scss" scoped>
.example-showcase .el-dropdown-link {
  display: flex;
  align-items: center;
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>
