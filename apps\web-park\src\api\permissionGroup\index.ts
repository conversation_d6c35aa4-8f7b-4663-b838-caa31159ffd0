import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};
export const PermissionGroupApi = {
  /**
   * 权限组列表
   */
  permissionGroupListApi() {
    return requestClient.get<any>(
      '/console/permissionGroup/permissionGroupList',
    );
  },

  /**
   * 分页查询权限组
   */
  pagingPermissionGroupApi(data: any) {
    return requestClient.post<any>(
      '/console/permissionGroup/pagingPermissionGroup',
      data,
    );
  },
  /**
   * 保存权限组
   */
  createPermissionGroupApi(data: any) {
    return requestClient.post<any>(
      '/console/permissionGroup/createPermissionGroup',
      data,
      config,
    );
  },

  /**
   * 根据权限组ID查询权限信息
   */
  getPermissionByIdApi(data: any) {
    return requestClient.post<any>(
      '/console/permissionGroup/getPermissionById',
      data,
    );
  },

  /**
   * 修改权限组
   */
  updatePermissionGroupApi(data: any) {
    return requestClient.post<any>(
      '/console/permissionGroup/updatePermissionGroup',
      data,
      config,
    );
  },

  /**
   * 删除权限组
   */
  deletePermissionGroupApi(id: string) {
    return requestClient.post<any>(
      `/console/permissionGroup/deletePermissionGroup/${id}`,
      {},
      config,
    );
  },
};
