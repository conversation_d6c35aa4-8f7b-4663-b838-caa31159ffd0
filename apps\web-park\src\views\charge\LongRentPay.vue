<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSParkFee } from '#/api/park/fee';

import { onBeforeMount, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElButton, ElMessage } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, ParkFeeApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

import RefundLongRentModal from './components/longRentPay/refundLongRentModal.vue';

defineOptions({
  name: 'LongRentPay', // 长租缴费列表
});

interface IFormValues {
  pay_time?: string[];
  park_id: string;
  park_name: string;
  pay_end_time: string;
  pay_start_time: string;
  plate_no: string;
  refund_states: number[];
  renew_states: number[];
  rent_states: number[];
}

/**
 * 业务变量
 */
const isReset = ref(false);
const renewStatesOptions = ref<CommonModule.IEnumItem[]>([]);
const rentStatesOptions = ref<CommonModule.IEnumItem[]>([]);
const refundStatesOptions = ref<CommonModule.IEnumItem[]>([]);
const payWayOptions = ref<CommonModule.IEnumItem[]>([]);
const userStore = useUserStore();

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);
const chooseDay = ref(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

const [RLRModal, rlrModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: RefundLongRentModal,
});

/**
 * 禁用日期方法
 */
const disabledDate = (
  date: Date | dayjs.Dayjs | null | number | string | undefined,
) => {
  if (!chooseDay.value) {
    return false;
  }
  // 超过31天后禁用
  const after31Days = dayjs(date).isAfter(
    dayjs(chooseDay.value).add(31, 'day'),
  );
  // 超过31天后禁用
  const before31Days = dayjs(date).isBefore(
    dayjs(chooseDay.value).subtract(31, 'day'),
  );
  return after31Days || before31Days;
};

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      LRPRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  LRPRef.formApi.resetForm();
  selectParkCheck.value = null;
  chooseDay.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  LRPRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入车牌号',
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'renew_states',
      label: '是否续费：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: renewStatesOptions,
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'rent_states',
      label: '长租状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: rentStatesOptions,
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'refund_states',
      label: '退款状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: refundStatesOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [
        `${dayjs().startOf('date').format('YYYY-MM-DD')} 00:00:00`,
        `${dayjs().endOf('date').format('YYYY-MM-DD')} 23:59:59`,
      ],
      fieldName: 'pay_time',
      label: '支付日期：',
      componentProps: {
        clearable: false,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '支付开始日期',
        endPlaceholder: '支付结束日期',
        disabledDate,
        onCalendarChange: (event: any) => {
          chooseDay.value = event[0];
        },
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description长租缴费管理列表
 */
const gridOptions: VxeGridProps<NSParkFee.IPagingRentSpaceRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    { field: 'order_no', title: '订单号', minWidth: 200 },
    { field: 'park_name', title: '停车场名称', minWidth: 100 },
    { field: 'park_code', title: '车位编号', minWidth: 100, width: 100 },
    { field: 'rent_rule_name', title: '规则名称', minWidth: 200 },
    { field: 'product_type_desc', title: '产品类型', minWidth: 100 },
    { field: 'payed_money', title: '支付金额', sortable: true, minWidth: 100 },
    { field: 'payed_time', title: '支付时间', sortable: true, minWidth: 200 },
    { field: 'prk_rent_rule_type_desc', title: '长租类型', minWidth: 100 },
    {
      field: 'valid_time',
      title: '长租有效期',
      slots: { default: 'valid_time' },
      minWidth: 300,
    },
    { field: 'pay_state_desc', title: '支付状态', minWidth: 100 },
    { field: 'rent_state_desc', title: '长租状态', minWidth: 100 },
    { field: 'plate_nos', title: '车牌号', minWidth: 100 },
    { field: 'member_name', title: '车主姓名', minWidth: 100 },
    { field: 'member_mobile', title: '手机号', minWidth: 100 },
    { field: 'renew_state_desc', title: '是否续费', minWidth: 100 },
    { field: 'invoice_state_desc', title: '发票状态', minWidth: 100 },
    { field: 'refund_state_desc', title: '退款状态', minWidth: 100 },
    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
      width: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: getPagingRentSpaceRecords,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [LongRentPayTable, LRPRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取长租缴费列表数据
 */
async function getPagingRentSpaceRecords(
  { page }: any,
  formValues: IFormValues,
) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    pay_start_time: formValues!.pay_time?.[0]
      ? dayjs(formValues!.pay_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    pay_end_time: formValues!.pay_time?.[1]
      ? dayjs(formValues!.pay_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.pay_time;
  try {
    const res = await ParkFeeApi.getPagingRentSpaceRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 是否展示申请退款按钮
 */
const showRefund = (row: NSParkFee.IPagingRentSpaceRecordsRow) => {
  return (
    row.pay_state === 2 &&
    [3, 4].includes(row.refund_state) &&
    row.rent_state === 1
  );
};

/**
 * 点击申请退款按钮
 */
const applyRefund = (row: NSParkFee.IPagingRentSpaceRecordsRow) => {
  rlrModalApi
    .setData({
      row,
      options: payWayOptions.value,
      confirmFn: () => {
        // eslint-disable-next-line no-use-before-define
        loadData();
      },
    })
    .open();
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSelectOptionsData = async () => {
  // 获取车辆类型,停车类型 + 获取订单状态,支付渠道,支付方式,退款状态
  const params1 = [
    { enum_key: 'openSignState', enum_value: 'EnumOpenSignState' },
    { enum_key: 'refundStateList', enum_value: 'EnumRefundState' },
    { enum_key: 'longRentList', enum_value: 'EnumRentSpaceApplyRentState' },
  ];
  // 获取退款渠道
  const params2 = [
    { enum_key: 'refundList', enum_value: 'EnumRefundChannelType' },
  ];
  try {
    const res = await Promise.all([
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, params1),
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.ORDER, params1),
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, params2),
    ]);
    renewStatesOptions.value = res[0]!.openSignState;
    rentStatesOptions.value = res[0]!.longRentList;
    payWayOptions.value = res[2]!.refundList;
    refundStatesOptions.value = res[1]!.refundStateList;
  } catch {
    renewStatesOptions.value = [];
    rentStatesOptions.value = [];
    payWayOptions.value = [];
    refundStatesOptions.value = [];
  }
};

const loadData = () => {
  LRPRef.setLoading(true);
  setTimeout(() => {
    LRPRef.setLoading(false);
    LRPRef.query();
  }, 200);
};

onBeforeMount(() => {
  initSelectOptionsData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    LRPRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--长租缴费管理表格 -->
    <LongRentPayTable table-title="长租缴费列表">
      <!-- 自定义-长租有效期列 -->
      <template #valid_time="{ row }">
        <span>{{ row.valid_start_time }}~{{ row.valid_end_time }}</span>
      </template>
      <!-- 自定义-操作项列 -->
      <template #actions="{ row }">
        <ElButton
          v-if="showRefund(row)"
          link
          type="danger"
          @click="applyRefund(row)"
        >
          申请退款
        </ElButton>
      </template>
    </LongRentPayTable>
    <RLRModal />
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
