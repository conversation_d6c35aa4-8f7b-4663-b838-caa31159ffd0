<script name="MessagePushTab" setup lang="ts">
// import { onMounted } from 'vue';
import {
  ElCard,
  ElCol,
  ElRadioButton,
  ElRadioGroup,
  ElRow,
} from 'element-plus';

const emit = defineEmits(['radioChange']);
const radio = defineModel<any>('radio');
const radioChange = () => {
  emit('radioChange', radio.value);
};
</script>
<template>
  <ElCard shadow="never">
    <ElRow :gutter="10">
      <ElCol :span="24">
        <ElRadioGroup v-model="radio" size="large" @change="radioChange">
          <ElRadioButton label="长租车到期提醒" :value="1" />
          <ElRadioButton label="车辆长期临停提醒" :value="2" />
          <ElRadioButton label="车道拥挤提醒" :value="3" />
          <ElRadioButton label="设备离线提醒" :value="4" />
          <ElRadioButton label="免费车入场提醒" :value="5" />
          <ElRadioButton label="访客车入场提醒" :value="6" />
        </ElRadioGroup>
      </ElCol>
    </ElRow>
  </ElCard>
</template>

<style lang="scss" scoped></style>
