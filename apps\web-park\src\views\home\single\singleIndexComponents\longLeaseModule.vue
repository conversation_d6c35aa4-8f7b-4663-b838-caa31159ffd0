<script lang="ts" setup>
import { ref } from 'vue';

/**
 * @description 长租交易模块组件
 * @component LongLeaseModule
 *
 * 功能：
 * 1. 展示长租交易数据
 * 2. 支持明暗主题切换
 * 3. 响应式布局适配
 */
import { VbenCountToAnimator } from '@vben/common-ui';

import { HomepageApi } from '#/api';
import { router } from '#/router';
// 定义数据
const statistics = ref<any>([
  {
    label: '本月现金收费(元)',
    value: '0',
    color: 'text-[#5570F1]',
    bg: 'bg-[#1570FF]/[0.08]',
  },
  {
    label: '本月电子支付(元)',
    value: '0',
    color: 'text-[#06C7B8]',
    bg: 'bg-[#3BDBCF]/[0.08]',
  },
  {
    label: '本月长租退费(元)',
    value: '0',
    color: 'text-[#E96466]',
    bg: 'bg-[#F93C00]/[0.05]',
  },
  {
    label: '用户长租待审核',
    value: '0',
    color: 'text-[#5570F1]',
    bg: 'bg-[#1570FF]/[0.08]',
  },
  {
    label: '本月新开通',
    value: '0',
    color: 'text-[#5570F1]',
    bg: 'bg-[#1570FF]/[0.08]',
  },
  {
    label: '本月过期未续费',
    value: '0',
    color: 'text-[#5570F1]',
    bg: 'bg-[#1570FF]/[0.08]',
  },
]);
const fetchData = async (params: any) => {
  HomepageApi.rentPayInfoApi(params).then((res) => {
    if (!res) {
      statistics.value[0].value = 0;
      statistics.value[1].value = 0;
      statistics.value[2].value = 0;
      statistics.value[3].value = 0;
      statistics.value[4].value = 0;
      statistics.value[5].value = 0;
      return;
    }
    statistics.value[0].value = res.cash_payed_money;
    statistics.value[1].value = res.electronic_payed_money;
    statistics.value[2].value = res.refund_rent_money;
    statistics.value[3].value = res.audit_rent_num;
    statistics.value[4].value = res.activate_rent_num;
    statistics.value[5].value = res.overdue_rent_num;
  });
};
const to = (item: any) => {
  if (item.label === '用户长租待审核' && item.value > 0) {
    router.push({
      path: '/bizAudit/BizAudit',
    });
  }
};

defineExpose({
  fetchData,
});
</script>

<template>
  <div
    class="mt-3 flex h-full items-center justify-around gap-2 overflow-y-auto"
  >
    <div
      v-for="(item, index) in statistics"
      :key="index"
      :class="[item.bg]"
      class="flex h-full flex-1 flex-col items-center justify-center rounded"
    >
      <div class="text-base font-medium">
        {{ item.label }}
      </div>
      <div :class="[item.color]" class="mt-1 text-xl font-bold">
        <VbenCountToAnimator
          :duration="2000"
          :end-val="Number(item.value)"
          :decimals="index < 3 ? 2 : 0"
          @click="to(item)"
          :style="{
            cursor:
              item.label === '用户长租待审核' && item.value > 0
                ? 'pointer'
                : 'unset',
          }"
          :start-val="1"
        />
      </div>
    </div>
  </div>
</template>
