<script lang="ts" setup>
import { onBeforeMount, ref, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { ElSegmented } from 'element-plus';

import { CommonApi, CommonModule } from '#/api';

import CarInRecord from './components/chargeAdmin/carInRecord.vue';
import DeleteRecord from './components/chargeAdmin/deleteRecord.vue';

defineOptions({
  name: 'ChargeAdmin', // 入场记录+已删除记录列表
});

const activeName = ref('');
const selectOptions = [
  { label: '入场记录', value: 'carInRecord' },
  { label: '已删除记录', value: 'deleteRecord' },
];
const carInRecordRef = useTemplateRef('carInRecordRef');
const deleteRecordRef = useTemplateRef('deleteRecordRef');

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = async () => {
  // 获取车辆类型及是否出场
  const params = [
    {
      enum_key: 'outParkStateList',
      enum_value: 'EnumOutParkState',
    },
    {
      enum_key: 'carTypeList',
      enum_value: 'EnumCarType',
    },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    carInRecordRef.value!.carTypeOptions = res.carTypeList;
    carInRecordRef.value!.outParkStateOptions = res.outParkStateList;
    deleteRecordRef.value!.carTypeOptions = res.carTypeList;
    activeName.value = 'carInRecord';
  } catch (error) {
    console.error(error);
  }
};

onBeforeMount(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <template #title>
      <ElSegmented
        v-model="activeName"
        :options="selectOptions"
        size="default"
      />
    </template>
    <CarInRecord v-show="activeName === 'carInRecord'" ref="carInRecordRef" />
    <DeleteRecord
      v-show="activeName === 'deleteRecord'"
      ref="deleteRecordRef"
    />
  </Page>
</template>
