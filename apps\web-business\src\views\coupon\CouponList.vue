<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PlatformApi } from '#/api';
import { CommonApi, CommonModule } from '#/api/common';

// 查询参数类型
interface IFormValues {
  coupon_meta_name: string;
  states: number[];
  types: number[];
}

// 优免卷列表行数据类型
interface IRowType {
  id: string;
  coupon_meta_name: string;
  type_desc: string;
  coupon_data: string;
  valid_time: string;
  state_desc: string;
  total_count: string;
  manual_draw_count: string;
  qrcode_draw_count: string;
  remainder_count: string;
  verified_count: string;
  created_at: string;
  valid_end_time: string;
  valid_start_time: string;
}

// 优免卷类型 Options
const searchTypesOptions = ref<{ label: string; value: number }[]>([]);
// 优免卷类状态 Options
const searchStatesOptions = ref<{ label: string; value: number }[]>([]);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'coupon_meta_name',
      label: '优免卷名称：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'types',
      label: '优免卷类型：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchTypesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'states',
      label: '优免卷状态：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchStatesOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    { title: '序号', type: 'seq', minWidth: 60, width: 60 },
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'park_name',
      title: '停车场名称',
      minWidth: 100,
    },
    {
      field: 'coupon_meta_name',
      title: '优免卷名称',
      minWidth: 100,
    },
    {
      field: 'type_desc',
      title: '优免卷类型',
      minWidth: 100,
    },
    {
      field: 'coupon_data',
      title: '优惠内容',
      slots: { default: 'coupon_data' },
      minWidth: 100,
    },
    {
      field: 'valid_time',
      title: '有效期',
      slots: { default: 'valid_time' },
      minWidth: 200,
    },
    {
      field: 'state_desc',
      title: '优免卷状态',
      slots: { default: 'state_desc' },
      minWidth: 100,
    },
    {
      field: 'total_count',
      title: '优免卷数量',
      minWidth: 100,
    },
    {
      field: 'manual_draw_count',
      title: '人工发放数量',
      minWidth: 100,
    },
    {
      field: 'qrcode_draw_count',
      title: '小程序领取数量',
      minWidth: 100,
    },
    {
      field: 'remainder_count',
      title: '优免卷剩余数量',
      minWidth: 120,
    },
    {
      field: 'verified_count',
      title: '已核销数量',
      minWidth: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      sortable: true,
      minWidth: 150,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: getCouponList,
    },
  },
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = () => {
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.COUPON, [
    { enum_key: 'types', enum_value: 'EnumCouponMetaType' },
    { enum_key: 'states', enum_value: 'EnumMerchantCouponState' },
  ]).then((res) => {
    searchTypesOptions.value = res.types;
    searchStatesOptions.value = res.states;
  });
};

/**
 * 获取优免卷列表数据
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 * @returns {Promise<{items: IRowType[], total: number}>} 优免卷列表和总数
 */
async function getCouponList({ page }: any, formValues: IFormValues) {
  // 查询参数格式化
  const params = {
    coupon_meta_name: formValues.coupon_meta_name,
    types: formValues.types,
    states: formValues.states,
  };
  try {
    const res = await PlatformApi.getPagingMerchantCouponsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 装填颜色
 * @param {number} state 状态
 * @returns {string} 颜色
 */
const getStateColor = (state: number) => {
  switch (state) {
    case 0: {
      return 'info';
    }
    case 1: {
      return 'primary';
    }
    case 2: {
      return 'danger';
    }
  }
};
// 显示优免券内容
const showCouponData = (row: any) => {
  if (row.coupon_meta_param.coupon_type === 1) {
    return `${row.coupon_meta_param.derate_hour}小时`;
  }
  if (row.coupon_meta_param.coupon_type === 2) {
    return `${row.coupon_meta_param.derate_money}元`;
  }
  if (row.coupon_meta_param.coupon_type === 3) {
    return `${row.coupon_meta_param.discount_ratio}折`;
  }
  if (row.coupon_meta_param.coupon_type === 4) {
    return `全免券`;
  }
  if (row.coupon_meta_param.coupon_type === 5) {
    return `${
      row.coupon_meta_param.start_time
    }-${row.coupon_meta_param.end_time}`;
  }
};
/**
 * 注册优免卷列表表格
 * @description 启用表格 配置formOptions与gridOptions
 */
const [CouponTable] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
onMounted(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <!-- 优免卷列表表格 -->
    <CouponTable>
      <template #valid_time="{ row }">
        {{ row.valid_start_time }} - {{ row.valid_end_time }}
      </template>
      <template #coupon_data="{ row }">
        {{ showCouponData(row) }}
      </template>
      <template #state_desc="{ row }">
        <ElTag :type="getStateColor(row.state)">
          {{ row.state_desc }}
        </ElTag>
      </template>
    </CouponTable>
  </Page>
</template>
