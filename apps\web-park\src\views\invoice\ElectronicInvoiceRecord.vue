<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee, NSInvoice } from '#/api';
import type { NSParkFee } from '#/api/park/fee';

import { onActivated, onBeforeMount, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElText, ElUpload } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, InvoiceApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

import InvoiceDetailsModal from './components/electronicInvoiceRecord/invoiceDetailsModal.vue';
import RelativeOrdersModal from './components/electronicInvoiceRecord/relativeOrdersModal.vue';

defineOptions({
  name: 'ElectronicInvoiceRecord', // 电子发票记录列表
});

interface IFormValues {
  park_id: string;
  park_name: string;
  time?: string[];
  end_time?: string;
  start_time?: string;
  company_name?: string;
  name_or_mobile?: string;
  invoice_states?: number[];
  invoice_types?: number[];
  title_types?: number[];
  fee_types?: number[];
}

/**
 * 业务变量
 */
const route = useRoute();
const uploadUrl = '/console/invoice/uploadInvoiceData';
// const uploadUrl = `${import.meta.env.VITE_WZT_ORIGIN_URL}/console/invoice/uploadInvoiceData`;
const isReset = ref(false);
const invoiceStatesOptions = ref<CommonModule.IEnumItem[]>([]);
const invoiceTypesOptions = ref<CommonModule.IEnumItem[]>([]);
const titleTypesOptions = ref<CommonModule.IEnumItem[]>([]);
const feeTypesOptions = ref<CommonModule.IEnumItem[]>([]);
const userStore = useUserStore();
const headers = reactive({
  Authorization: userStore.userInfo?.token,
});

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 查看发票明细弹窗
 */
const [ROModal, roModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: RelativeOrdersModal,
});

/**
 * 查看发票明细弹窗
 */
const [IDModal, idModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: InvoiceDetailsModal,
});

/**
 * 打开车场选择弹窗
 */
const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      EIRRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  EIRRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  EIRRef.reload();
};

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [ConfirmModal, ConfirmModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ConfirmModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ConfirmModalRef.close();
  };
  ConfirmModalRef.open();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'company_name',
      label: '开票公司：',
      componentProps: {
        clearable: true,
        placeholder: '请输入开票公司',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'name_or_mobile',
      label: '昵称/手机：',
      componentProps: {
        clearable: true,
        placeholder: '请输入会员昵称/手机号',
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'invoice_states',
      label: '开票状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: invoiceStatesOptions,
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'invoice_types',
      label: '发票类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: invoiceTypesOptions,
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'title_types',
      label: '抬头类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: titleTypesOptions,
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'fee_types',
      label: '费用类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: feeTypesOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [],
      fieldName: 'time',
      label: '申请日期：',
      componentProps: {
        clearable: false,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description电子发票记录管理列表
 */
const gridOptions: VxeGridProps<NSParkFee.IPagingRentSpaceRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    {
      field: 'relative_orders',
      title: '关联订单',
      sortable: true,
      slots: { default: 'relative_orders' },
      minWidth: 100,
    },
    { field: 'prk_park_name', title: '停车场名称', minWidth: 150 },
    { field: 'money', title: '开票金额', sortable: true, minWidth: 100 },
    { field: 'state_desc', title: '开票状态', minWidth: 150 },
    { field: 'company_name', title: '开票公司', minWidth: 250 },
    { field: 'name', title: '会员昵称', minWidth: 150 },
    { field: 'mbr_mobile', title: '会员手机号', minWidth: 150 },
    { field: 'contact_mobile', title: '公司电话', minWidth: 150 },
    { field: 'title_type_desc', title: '抬头类型', minWidth: 100 },
    { field: 'fee_type_desc', title: '费用类型', minWidth: 150 },
    { field: 'invoice_type_desc', title: '发票类型', minWidth: 200 },
    { field: 'title', title: '发票抬头', minWidth: 200 },
    {
      field: 'attachment_name',
      title: '附件',
      slots: { default: 'attachment_name' },
      minWidth: 150,
    },
    { field: 'created_at', title: '申请时间', sortable: true, minWidth: 200 },
    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 350,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: getPagingInvoiceRecord,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [ElectronicInvoiceRecordTable, EIRRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取电子发票记录列表数据
 */
async function getPagingInvoiceRecord({ page }: any, formValues: IFormValues) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.time;
  try {
    const res = await InvoiceApi.getPagingInvoiceRecordApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSelectOptionsData = async () => {
  // 获取开票状态,发票类型
  const params = [
    { enum_key: 'invoiceStatesList', enum_value: 'EnumInvoiceState' },
    { enum_key: 'invoiceTypeList', enum_value: 'EnumInvoiceType' },
    { enum_key: 'titleTypeList', enum_value: 'EnumTitleType' },
    { enum_key: 'feeTypeList', enum_value: 'EnumFeeType' },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    invoiceStatesOptions.value = res.invoiceStatesList;
    invoiceTypesOptions.value = res.invoiceTypeList;
    feeTypesOptions.value = res.feeTypeList;
    titleTypesOptions.value = res.titleTypeList;
  } catch {
    invoiceStatesOptions.value = [];
    invoiceTypesOptions.value = [];
    feeTypesOptions.value = [];
    titleTypesOptions.value = [];
  }
};

/**
 * 查询发票结果
 */
const queryInvoice = async (row: NSInvoice.IPagingInvoiceRecordRow) => {
  if (![1, 4].includes(row.state))
    return ElMessage.error('发票成功无需查询结果');
  row.queryLoading = true;
  try {
    const res = await InvoiceApi.queryInvoiceApi(row.id);
    if (res.success) {
      ElMessage.success(res.message);
    } else {
      ElMessage.error(res.detail_message || res.message);
    }
  } catch (error) {
    console.error(error);
  } finally {
    row.queryLoading = false;
  }
};

/**
 * 发送发票邮件
 */
const sendingInvoiceMail = async (row: NSInvoice.IPagingInvoiceRecordRow) => {
  if (row.state !== 3) return ElMessage.error('发票未成功开具,不能发送邮件');
  row.sendLoading = true;
  try {
    const res = await InvoiceApi.sendingInvoiceMailApi(row.id);
    if (res.success) {
      if (res.data.code === 0) {
        ElMessage.success(res.data.send_email_result_msg);
      } else {
        ElMessage.error(res.data.message);
      }
    } else {
      ElMessage.error(res.detail_message || res.message);
    }
  } catch (error) {
    console.error(error);
  } finally {
    row.sendLoading = false;
  }
};

/**
 * 红冲-作废发票
 */
const cancelInvoice = async (row: NSInvoice.IPagingInvoiceRecordRow) => {
  if (row.state !== 3) return ElMessage.error('发票未成功开具,不能红冲作废');
  row.cancelLoading = true;
  try {
    if (row.invoice_type === 2) {
      showConfirmModal({
        title: '提示',
        description: '电子专票红冲需要否进行红字信息表申请，是否申请？',
        onConfirm: async () => {
          const res = await InvoiceApi.cancelInvoiceApi(row.id);
          if (res.success) {
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.detail_message || res.message);
          }
        },
      });
    } else {
      const res = await InvoiceApi.cancelInvoiceApi(row.id);
      if (res.success) {
        if (res.data.code === 0) {
          ElMessage.success(res.data.message);
        } else {
          ElMessage.error(res.data.message);
        }
      } else {
        ElMessage.error(res.detail_message || res.message);
      }
    }
  } catch (error) {
    console.error(error);
  } finally {
    row.cancelLoading = false;
  }
};

/**
 * 上传文件大小校验
 */
const beforeUpload = (file: { size: number }) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage.warning('上传文件大小不能超过 25MB!');
  }
};

const saveToFile = async (params: NSInvoice.ISaveFileParams) => {
  const res = await InvoiceApi.saveFileApi(params);
  if (res.success) {
    ElMessage.success(res.message);
    EIRRef.query();
  } else {
    ElMessage.error(res.detail_message || res.message);
  }
};

/**
 * 发票上传成功
 */
const onSuccessUpload = (
  response: {
    data: { attachment_name: string; attachment_path: string };
    detail_message: any;
    message: string;
    success: boolean;
  },
  id: string,
  email: string,
) => {
  if (response.success) {
    const { attachment_path, attachment_name } = response.data;
    // 保存文件到电子发票记录表
    const params = {
      id,
      email,
      attachment_path,
      attachment_name,
    };
    ElMessage.success(response.message);
    saveToFile(params);
    // eslint-disable-next-line no-use-before-define
    loadData();
  } else {
    ElMessage.error(response.message);
  }
};

/**
 * 查看关联订单
 */
const viewRelativeOrders = (row: NSInvoice.IPagingInvoiceRecordRow) => {
  roModalApi
    .setData({
      data: row,
    })
    .open();
};

/**
 * 查看发票明细
 */
const viewDetail = (row: NSInvoice.IPagingInvoiceRecordRow) => {
  idModalApi
    .setData({
      data: row,
    })
    .open();
};

/**
 * 下载附件内容
 */
const downloadAttachment = (path: string) => {
  if (path) {
    window.open(path, '_blank');
  } else {
    ElMessage.error('发票为空！');
  }
};

const loadData = () => {
  EIRRef.setLoading(true);
  setTimeout(() => {
    EIRRef.setLoading(false);
    EIRRef.query();
  }, 200);
};

onBeforeMount(() => {
  initSelectOptionsData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    EIRRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});

onActivated(() => {
  if (Object.prototype.toString.call(route.query) === '[object Object]') return;
  const { memberId, memberName } = route.query;
  EIRRef.formApi.setFieldValue('memberId', memberId);
  EIRRef.formApi.setFieldValue('name_or_mobile', memberName);
  loadData();
});
</script>
<template>
  <Page auto-content-height>
    <!--电子发票记录管理表格 -->
    <ElectronicInvoiceRecordTable table-title="电子发票记录列表">
      <!-- 自定义-关联订单 -->
      <template #relative_orders="{ row }">
        <ElButton link type="primary" @click="viewRelativeOrders(row)">
          {{ row.relative_orders }}
        </ElButton>
      </template>
      <!-- 自定义-附件信息 -->
      <template #attachment_name="{ row }">
        <ElButton
          v-if="row.attachment_name && row.attachment_path"
          link
          type="primary"
          @click="downloadAttachment(row.attachment_path)"
        >
          {{ row.attachment_name }}
        </ElButton>
        <ElText v-else type="danger">暂无附件</ElText>
      </template>
      <!-- 自定义-操作项列 -->
      <template #actions="{ row }">
        <div class="flex items-center">
          <ElButton link type="primary" @click="viewDetail(row)">
            查看明细
          </ElButton>
          <ElUpload
            v-if="row.state !== 1"
            class="mx-3"
            :limit="1"
            :action="uploadUrl"
            :data="{ id: row.id }"
            :headers="headers"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="
              (response) => onSuccessUpload(response, row.id, row.email)
            "
          >
            <ElButton link type="primary"> 人工开票 </ElButton>
          </ElUpload>
          <ElButton
            link
            type="primary"
            :loading="row.queryLoading"
            @click="queryInvoice(row)"
          >
            查询结果
          </ElButton>
          <ElButton
            link
            type="primary"
            :loading="row.sendLoading"
            @click="sendingInvoiceMail(row)"
          >
            发送邮件
          </ElButton>
          <ElButton
            link
            type="danger"
            :loading="row.cancelLoading"
            @click="cancelInvoice(row)"
          >
            红冲
          </ElButton>
        </div>
      </template>
    </ElectronicInvoiceRecordTable>
    <!-- 关联订单列表 -->
    <ROModal />
    <!-- 查看发票明细弹窗 -->
    <IDModal />
    <!-- 红冲作废发票二次确认弹窗 -->
    <ConfirmModal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </ConfirmModal>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
