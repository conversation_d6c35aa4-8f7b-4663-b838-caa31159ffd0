<script name="DictTypeTable" lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { nextTick, onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton, ElForm, ElFormItem, ElInput, ElText } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { DictApi } from '#/api';
import { router } from '#/router';

const addFormRef = ref();
const editFormRef = ref();
const status = ref(false);

const rules = {
  name: [
    {
      required: true,
      message: '请输入字典名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入字典编码',
      trigger: 'blur',
    },
  ],
};
interface IFormValues {
  name: string;
  code: string;
  memo: string;
}
interface IDataValues {
  form: IFormValues;
  updateForm: any;
}
const data = reactive<IDataValues>({
  form: {
    name: '',
    code: '',
    memo: '',
  },
  updateForm: {},
});
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '填写字典名称',
      },
      defaultValue: '',
      fieldName: 'name',
      label: '字典名称',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '填写字典编码',
      },
      defaultValue: '',
      fieldName: 'code',
      label: '字典编码',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  // submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
interface RowType {
  category: string;
  color: string;
  id: string;
  price: string;
  productName: string;
  releaseDate: string;
}

const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: '',
  },
  columns: [
    { field: 'id', title: 'ID', width: 60 },
    { field: 'name', title: '字典名称' },
    { field: 'code', title: '字典编码' },
    { field: 'updated_at', title: '修改时间', sortable: true },
    { field: 'memo', title: '备注' },
    {
      slots: { default: 'action' },
      title: '操作',
      field: 'action',
      fixed: 'right',
    },
  ],
  height: 'auto',
  keepSource: true,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } = await DictApi.pagingDictTypeApi({
          page: page.currentPage,
          limit: page.pageSize,
          ...formValues,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
// 分页查询字典列表数
onMounted(() => {
  status.value = true;
});
// 查看字典数据
const showDictDetail = (id: any) => {
  router.push({
    path: '/system/dict',
    query: {
      dictTypeId: id,
    },
  });
};

/**
 * 新建字典
 */
const [ModalDictType, modalDictTypeApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    addFormRef.value.validate().then(() => {
      DictApi.createDictTypeApi(data.form)
        .then(() => {
          addFormRef.value.resetFields();
          gridApi.query();
          modalDictTypeApi.close();
        })
        .catch(() => {
          gridApi.query();
        });
    });
  },
  onCancel: () => {
    addFormRef.value.resetFields();
    modalDictTypeApi.close();
  },
});
// 新建字典类型
const handleCreate = (addFormRef: { clearValidate: () => void }) => {
  if (status.value === false) {
    nextTick(() => {
      addFormRef.clearValidate();
    });
  }
  data.form = {
    name: '',
    code: '',
    memo: '',
  };
  modalDictTypeApi.open();
  status.value = false;
};

// 删除字典类型
const batchDelete = (id: any) => {
  showConfirmModal({
    title: '字典类型删除',
    description: '确定要删除吗？',
    onConfirm: async () => {
      DictApi.deleteDictTypeApi(id)
        .then(() => {
          gridApi.query();
        })
        .catch(() => {
          gridApi.query();
        });
    },
  });
};
const handleDel = (val: any) => {
  batchDelete(val);
};

/**
 * 新建字典
 */
const [ModalDictTypeUpdate, modalDictTypeUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    editFormRef.value.validate().then(() => {
      DictApi.updateDictTypeApi(data.updateForm)
        .then(() => {
          gridApi.query();
          editFormRef.value.resetFields();
          modalDictTypeUpdateApi.close();
        })
        .catch(() => {
          gridApi.query();
        });
    });
  },
  onCancel: () => {
    editFormRef.value.resetFields();
    modalDictTypeUpdateApi.close();
  },
});
// 修改字典类型
const handleEdit = (row: { code: any; id: any; memo: any; name: any }) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    memo: row.memo,
  };
  modalDictTypeUpdateApi.open();
};
</script>

<template>
  <Page auto-content-height>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <Grid>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElButton type="primary" @click="handleCreate(addFormRef)">
          新 增
        </ElButton>
      </template>
      <template #action="{ row }">
        <ElButton link type="primary" @click="showDictDetail(row.id)">
          字典数据
        </ElButton>
        <ElButton link type="primary" @click="handleEdit(row)"> 修改 </ElButton>
        <ElButton link type="danger" @click="handleDel(row.id)">
          删除
        </ElButton>
      </template>
    </Grid>
    <ModalDictType
      :fullscreen-button="false"
      class="w-[650px]"
      title="新增字典类型"
    >
      <ElForm
        ref="addFormRef"
        :model="data.form"
        :rules="rules"
        label-width="80px"
      >
        <ElFormItem label="字典名称" prop="name">
          <ElInput v-model="data.form.name" />
        </ElFormItem>
        <ElFormItem label="字典编码" prop="code">
          <ElInput v-model="data.form.code" />
        </ElFormItem>
        <ElFormItem label="备注" prop="memo">
          <ElInput v-model="data.form.memo" :rows="6" type="textarea" />
        </ElFormItem>
      </ElForm>
    </ModalDictType>

    <ModalDictTypeUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="修改字典类型"
    >
      <ElForm
        ref="editFormRef"
        :model="data.updateForm"
        :rules="rules"
        label-width="80px"
      >
        <ElFormItem label="字典名称" prop="name">
          <ElInput v-model="data.updateForm.name" />
        </ElFormItem>
        <ElFormItem label="字典编码" prop="code">
          <ElInput v-model="data.updateForm.code" />
        </ElFormItem>
        <ElFormItem label="备注" prop="memo">
          <ElInput v-model="data.updateForm.memo" :rows="6" type="textarea" />
        </ElFormItem>
      </ElForm>
    </ModalDictTypeUpdate>
  </Page>
</template>
<style lang="scss" scoped></style>
