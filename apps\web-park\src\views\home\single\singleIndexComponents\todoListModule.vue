<script lang="ts" setup>
import { ref } from 'vue';

/**
 * @description 待办事项组件
 * @component TodoListModule
 *
 * 功能：
 * 1. 展示待办事项
 * 2. 响应式布局适配
 */
import { HomepageApi } from '#/api';
import { router } from '#/router';
// 定义数据
const todoList = ref<any>([
  {
    label: '滞留车辆数量',
    value: '0',
  },
  {
    label: '流程待审批',
    value: '0',
  },
  {
    label: '长租即将到期',
    value: '0',
  },
  {
    label: '待处理退款',
    value: '0',
  },
]);
const fetchData = async (params: any) => {
  HomepageApi.rentTodoApi(params).then((res) => {
    todoList.value[2].value = res.overdue_rent_num || 0;
    todoList.value[3].value = res.refund_rent_num || 0;
    todoList.value[1].value = res.other_rent_num || 0;
    todoList.value[0].value = res.stranded_num || 0;
  });
};
const to = (i: number) => {
  if (i === 0) {
    router.push({
      path: '/charge/chargeAdmin',
    });
  }
  if (i === 1) {
    router.push({
      path: '/bizAudit/BizAudit',
    });
  }
  if (i === 2) {
    router.push({
      path: '/car/spaceRentApply',
    });
  }
  if (i === 3) {
    router.push({
      path: '/finance/refund',
    });
  }
};
defineExpose({
  fetchData,
});
</script>

<template>
  <div
    class="mt-3 flex h-full items-center justify-around gap-2 overflow-hidden"
  >
    <div
      v-for="(item, index) in todoList"
      :key="index"
      class="flex h-full flex-1 flex-col items-center justify-center rounded"
    >
      <div class="text-base font-medium">
        {{ item.label }}
      </div>
      <div class="mt-2 text-3xl font-semibold text-[#1570FF]">
        {{ item.value }}
      </div>
      <div
        @click="to(index)"
        class="mt-1 flex cursor-pointer items-center gap-1 text-sm text-[#2B6CDE] hover:underline"
      >
        <span>查看详情</span>
        <span class="icon-[weui--arrow-filled]"></span>
      </div>
    </div>
  </div>
</template>
