<script lang="ts" setup>
import { nextTick, onMounted, ref, watch } from 'vue';

import { VbenCountToAnimator } from '@vben/common-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { ElRadioButton, ElRadioGroup, ElTabPane, ElTabs } from 'element-plus';

import { HomepageApi } from '#/api';

const tabPosition = ref<number>(0);
const activeName = ref<string>('临停数据');
const chartRef0 = ref<any>();
const chartRef1 = ref<any>();
const { renderEcharts: renderEcharts0, getChartInstance: getChartInstance0 } =
  useEcharts(chartRef0);
const { renderEcharts: renderEcharts1, getChartInstance: getChartInstance1 } =
  useEcharts(chartRef1);
const turnoverRate = ref<any>(0);
const truncateDecimal = (num: null | undefined, precision = 3) => {
  if (num === null || num === undefined) {
    return num;
  }
  // 将数字转换为字符串
  const numStr = String(num);
  // 使用正则表达式查找小数点后是否有超过precision位的数字
  const regex = new RegExp(`(\\.\\d{${precision + 1},})$`);
  // 如果匹配，则截取到小数点后precision位
  if (regex.test(numStr)) {
    return Number(numStr).toFixed(precision);
  }
  // 如果没有匹配，则返回原数字
  return num;
};
const data00 = ref<any>([]);
const data01 = ref<any>([]);
const xData0 = ref<any>([]);

const data10 = ref<any>([]);
const data11 = ref<any>([]);
const xData1 = ref<any>([]);
const init0 = () => {
  renderEcharts0({
    grid: {
      top: '20%',
      left: '3%',
      right: '8%',
      bottom: '3%',
      containLabel: true,
    },
    legend: {
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      show: true,
      top: 0,
      left: '3%',
      itemStyle: {
        borderWidth: 0,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        let result = `${params[0].axisValueLabel}<br/>`; // 时间/类别
        params.forEach((item: any) => {
          result += `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:8px;height:8px;background-color:${item.color}"></span>`;
          result += `${item.seriesName}: ${item.value}<br/>`;
        });
        return result;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      textStyle: {
        color: '#666666',
      },
      borderWidth: 0,
      padding: [5, 10],
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData0.value,
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisLabel: {
        color: '#333333',
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 1,
      interval: 0.2,
      axisLabel: {
        formatter: (value: number) => `${value}`,
        color: '#333333',
      },
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        name: '周转率',
        type: 'line',
        smooth: true,
        data: data00.value,
        itemStyle: {
          color: '#5570F1',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(85, 112, 241, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(85, 112, 241, 0.1)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
      },
      {
        name: '车位利用率',
        type: 'line',
        smooth: true,
        data: data01.value,
        itemStyle: {
          color: '#3BDBCF',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 219, 241, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(0, 219, 241, 0.1)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
      },
    ],
  });
};

const init1 = () => {
  renderEcharts1({
    grid: {
      top: '20%',
      left: '3%',
      right: '8%',
      bottom: '3%',
      containLabel: true,
    },
    legend: {
      icon: 'circle',
      itemWidth: 12,
      itemHeight: 12,
      show: true,
      top: 0,
      left: '3%',
      itemStyle: {
        borderWidth: 0,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        let result = `${params[0].axisValueLabel}<br/>`; // 时间/类别
        params.forEach((item: any) => {
          result += `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:8px;height:8px;background-color:${item.color}"></span>`;
          result += `${item.seriesName}: ${item.value}<br/>`;
        });
        return result;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      textStyle: {
        color: '#666666',
      },
      borderWidth: 0,
      padding: [5, 10],
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xData1.value,
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      axisLabel: {
        color: '#333333',
      },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 1,
      interval: 0.2,
      axisLabel: {
        formatter: (value: number) => `${value}`,
        color: '#333333',
      },
      axisLine: {
        lineStyle: {
          color: '#E5E6EB',
        },
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        name: '临停平均时长（小时）',
        type: 'line',
        smooth: true,
        data: data10.value,
        itemStyle: {
          color: '#5570F1',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(85, 112, 241, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(85, 112, 241, 0.1)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
      },
      {
        name: '长租平均时长（小时）',
        type: 'line',
        smooth: true,
        data: data11.value,
        itemStyle: {
          color: '#3BDBCF',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 219, 241, 0.6)',
              },
              {
                offset: 1,
                color: 'rgba(0, 219, 241, 0.1)',
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
        },
      },
    ],
  });
};
const list0 = ref<any>([
  { name: '临停车次', value: '0' },
  { name: '临停通行时长', value: '0' },
  { name: '临停平均通行效率', value: '0' },
]);
const list1 = ref<any>([
  { name: '长租车次', value: '0' },
  { name: '长租通行时长', value: '0' },
  { name: '长租平均通行效率', value: '0' },
]);
const opacity = ref<number>(1);
const paramsParent = ref<any>(null);
onMounted(() => {
  init0();
});
const fetchData = async (params: any) => {
  paramsParent.value = params;
  if (tabPosition.value === 0) {
    HomepageApi.parkTurnoverAndSpaceUsageApi(params).then((res) => {
      if (res) {
        res = res.reverse();
        data00.value = res.map(
          (item: { average_turnround_rate: any }) =>
            item.average_turnround_rate || 0,
        );
        data01.value = res.map(
          (item: { average_use_ratio: any }) => item.average_use_ratio || 0,
        );
        xData0.value = res.map(
          (item: { statistics_date: any }) => item.statistics_date,
        );
      }
      setTimeout(() => {
        const chartInstance = getChartInstance0();
        chartInstance?.setOption({
          xAxis: {
            data: xData0.value,
          },
          series: [
            {
              data: data00.value,
            },
            {
              data: data01.value,
            },
          ],
        });
      }, 1000);
      // xData = useData.map((item) =>
      //   dayjs(item.statistics_date).format(timeFormatter[params.time_unit]),
      // );
    });
  }
  if (tabPosition.value === 1) {
    HomepageApi.statParkTrafficFlowsByIntervalApi(params).then((res) => {
      if (res) {
        res = res.reverse();
        data10.value = res.map(
          (item: { parking_time: any }) => item.parking_time || 0,
        );
        data11.value = res.map(
          (item: { rent_time: any }) => item.rent_time || 0,
        );
        xData1.value = res.map((item: { time: any }) => item.time);
      }
      setTimeout(() => {
        const chartInstance = getChartInstance1();
        chartInstance?.setOption({
          xAxis: {
            data: xData1.value,
          },
          series: [
            {
              data: data10.value,
            },
            {
              data: data11.value,
            },
          ],
        });
      }, 1000);
      // xData = useData.map((item) =>
      //   dayjs(item.statistics_date).format(timeFormatter[params.time_unit]),
      // );
    });
  }
  if (tabPosition.value === 2) {
    HomepageApi.parkTrafficEfficiencyApi(params).then((res) => {
      if (res) {
        list0.value[0].value = res.parking_pass_amount;
        list0.value[1].value = res.parking_pass_time;
        list0.value[2].value = res.parking_ratio;

        list1.value[0].value = res.rent_pass_amount;
        list1.value[1].value = res.rent_pass_time;
        list1.value[2].value = res.rent_ratio;

        turnoverRate.value = truncateDecimal(res.park_turnover_rate || 0, 2);
      }
    });
  }
};
watch(
  () => tabPosition.value,
  (value) => {
    opacity.value = 0;
    if (value === 0) {
      nextTick(() => {
        init0();
        fetchData(paramsParent.value);
        setTimeout(() => {
          opacity.value = 1;
        }, 1000);
      });
    }
    if (value === 1) {
      nextTick(() => {
        init1();
        fetchData(paramsParent.value);
        setTimeout(() => {
          opacity.value = 1;
        }, 1000);
      });
    }
    if (value === 2) {
      fetchData(paramsParent.value);
    }
  },
);

defineExpose({
  fetchData,
});
</script>

<template>
  <div class="relative mt-2 h-full">
    <ElRadioGroup
      class="tabs1 absolute right-[0px] top-[-36px]"
      v-model="tabPosition"
      style="margin-bottom: 30px"
    >
      <ElRadioButton :value="0">周转率及车位利用趋势</ElRadioButton>
      <ElRadioButton :value="1">停车时长数据</ElRadioButton>
      <ElRadioButton :value="2">通行效率数据</ElRadioButton>
    </ElRadioGroup>
    <ElTabs v-if="tabPosition === 2" v-model="activeName" class="demo-tabs">
      <ElTabPane label="临停数据" name="临停数据" />
      <ElTabPane label="长租数据" name="长租数据" />
    </ElTabs>
    <div v-show="tabPosition === 0" :style="{ height: '100%', opacity }">
      <EchartsUI ref="chartRef0" :style="{ height: '100%' }" class="w-full" />
    </div>
    <div v-show="tabPosition === 1" :style="{ height: '100%', opacity }">
      <EchartsUI ref="chartRef1" :style="{ height: '100%' }" class="w-full" />
    </div>
    <div
      v-show="tabPosition === 2"
      class="mt-[-8px] flex items-center justify-center"
      :style="{ height: '80%' }"
    >
      <div
        class="relative box-border flex items-center justify-center bg-[#1570FF]/[0.08] p-[60px]"
        style="border-radius: 50%"
      >
        <div
          class="cus-border absolute box-border flex h-[90%] w-[90%] items-center justify-center"
          style="border-radius: 50%"
        >
          <div
            class="custom-sbg absolute box-border flex-col items-center justify-center p-[40px]"
            style="border-radius: 50%"
          >
            <div
              class="absolute left-0 right-0 top-[22px] text-center font-bold text-[#fff]"
            >
              {{ turnoverRate }}
            </div>
            <div
              class="absolute left-0 right-0 text-center text-[12px] text-[#fff]"
            >
              平均周转率
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="activeName === '临停数据'"
        class="ml-4 flex h-full w-[70%] flex-wrap items-center gap-1"
      >
        <div
          v-for="(item, i) in list0"
          :key="i"
          class="flex h-[calc(50%-0.25rem)] flex-1 flex-col justify-center rounded-lg bg-[#1570FF]/[0.08] text-center"
        >
          <div class="text-sm">{{ item.name }}</div>
          <div class="text-base font-bold text-[#5570F1]">
            <VbenCountToAnimator
              :duration="2000"
              :end-val="Number(item.value)"
              :start-val="0"
              :decimals="i !== 0 ? 2 : 0"
            />
          </div>
        </div>
      </div>

      <div
        v-if="activeName === '长租数据'"
        class="ml-4 flex h-full w-[70%] flex-wrap items-center gap-1"
      >
        <div
          v-for="(item, i) in list1"
          :key="i"
          class="flex h-[calc(50%-0.25rem)] flex-1 flex-col justify-center rounded-lg bg-[#1570FF]/[0.08] text-center"
        >
          <div class="text-sm">{{ item.name }}</div>
          <div class="text-base font-bold text-[#5570F1]">
            <VbenCountToAnimator
              :duration="2000"
              :end-val="Number(item.value)"
              :start-val="0"
              :decimals="i !== 0 ? 2 : 0"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style>
.el-tabs__active-bar {
  display: none;
}

.el-tabs__nav-wrap::after {
  display: none;
}

.tabs1 .is-active .el-radio-button__inner {
  background-color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
}

.el-tabs__item.is-active,
.el-tabs__item:hover {
  color: hsl(var(--primary)) !important;
}

.cus-border {
  border: 4px solid hsl(var(--primary) / 90%);
}

.custom-sbg {
  background-image: linear-gradient(
    to bottom,
    hsl(var(--primary) / 90%),
    hsl(var(--primary) / 50%)
  );
}
</style>
