<script lang="ts" setup>
/**
 * 当班记录
 */
import { ref } from 'vue';

import { ElEmpty } from 'element-plus';
import { vue3ScrollSeamless } from 'vue3-scroll-seamless';

import { HomepageApi } from '#/api';
import { router } from '#/router';

const list = ref<any>([]);
const classOptions = {
  singleHeight: 35,
  waitTime: 2500,
};
const fetchData = async (params: any) => {
  HomepageApi.shiftHandoverRecordsApi(params).then((res) => {
    list.value = res;
  });
};
const to = () => {
  router.push({
    path: '/statisticalReport/abnormalRecord',
  });
};
const to2 = () => {
  router.push({
    path: '/finance/shiftReport',
  });
};
defineExpose({
  fetchData,
});
</script>

<template>
  <div class="mt-2 flex h-full w-full flex-1 flex-col overflow-hidden">
    <div
      class="flex h-[35px] items-center justify-between bg-[#F4F7FD] dark:bg-[#F4F7FD]/[0.08]"
    >
      <span class="w-[18%] truncate text-center text-base">岗亭名称</span>
      <span class="w-[18%] truncate text-center text-base">当班人员</span>
      <span class="w-[24%] truncate text-center text-base">上班时间</span>
      <span class="w-[24%] truncate text-center text-base">下班时间</span>
      <span class="w-[16%] truncate text-center text-base">异常数据</span>
    </div>
    <ElEmpty
      :image-size="60"
      v-if="list.length === 0"
      description="暂无交班记录"
    />
    <vue3ScrollSeamless
      :class-options="classOptions"
      :data-list="list"
      class="flex-1 overflow-hidden"
    >
      <div
        v-for="(item, i) of list"
        :key="i"
        class="flex h-[35px] items-center justify-between border-b border-gray-100 hover:cursor-pointer hover:bg-gray-100 hover:text-[#5570F1]"
        title="点击查看当班详情"
      >
        <span class="w-[18%] truncate text-center text-xs" @click="to2">{{
          item.park_name
        }}</span>
        <span class="w-[18%] truncate text-center text-xs" @click="to2">{{
          item.shift_name
        }}</span>
        <span class="w-[24%] truncate text-center text-xs" @click="to2">{{
          item.on_time
        }}</span>
        <span class="w-[24%] truncate text-center text-xs" @click="to2">{{
          item.off_time
        }}</span>
        <span
          v-if="item.loss_money > 0"
          class="w-[16%] truncate text-center text-xs"
          @click="to()"
          >点击查看
        </span>
        <span @click="to2" v-else class="w-[16%] truncate text-center text-xs">
          --
        </span>
      </div>
    </vue3ScrollSeamless>
  </div>
</template>
