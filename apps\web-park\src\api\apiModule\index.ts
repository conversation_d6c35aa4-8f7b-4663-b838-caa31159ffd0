import { requestClient } from '#/api/request';

export const ApiModuleApi = {
  /**
   * api接口管理
   */
  // 分页查找
  pagingApisApi(data: any) {
    return requestClient.post<any>('/console/api/pagingApis', data);
  },

  // api接口保存
  createApiApi(data: any) {
    return requestClient.post<any>('/console/api/createApi', data);
  },

  // api接口修改
  updateApiApi(data: any) {
    return requestClient.post<any>('/console/api/updateApi', data);
  },

  // api接口删除
  deleteApisApi(data: any) {
    return requestClient.post<any>('/console/api/deleteApis', data);
  },

  // 查询单条api接口
  getApiByIdApi(data: any) {
    return requestClient.post<any>(`/console/api/getApiById/${data}`);
  },

  // api接口列表
  apiListApi() {
    return requestClient.get<any>('/console/api/apiList');
  },

  // 查询权限分组下的api树
  getApiPermissionByGroupIdApi(data: any) {
    return requestClient.post<any>(
      `/console/api/getApiPermissionByGroupId/${data}`,
    );
  },

  // 查询角色权限分组下的api树
  getRoleApiPermissionTreeApi(data: any) {
    return requestClient.post<any>(
      `/console/api/getRoleApiPermissionTree/${data}`,
    );
  },
};
