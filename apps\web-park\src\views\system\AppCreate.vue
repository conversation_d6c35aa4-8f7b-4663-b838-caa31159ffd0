<script setup name="AppCreate" lang="ts">
import { onActivated, onMounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

// import commonService from '@/service/common/CommonService';
// import { closeCurrentTab } from '@/utils/tabKit';
import {
  ElButton,
  ElCard,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElRow,
  ElSelect,
} from 'element-plus';

// import appAdminService from '@/service/system/AppAdminService';
import { AppApi, CommonApi, CommonModule } from '#/api';
import { router } from '#/router';

const appType = ref<any>([]);
const addForm = ref<any>(null);
const data = reactive({
  active: 1,
  appForm: {
    name: undefined,
    app_key: undefined,
    features: undefined,
    memo: undefined,
    app_type: undefined,
    app_code: undefined,
    app_public_key: undefined,
    app_private_key: undefined,
    app_sign_key: undefined,
    app_token_expiry: undefined,
    plat_public_key: undefined,
    plat_private_key: undefined,
    plat_sign_key: undefined,
    plat_token_expiry: undefined,
    app_params: undefined,
  },
  rules: {
    name: [
      {
        required: true,
        message: '请输入应用名称',
        trigger: 'blur',
      },
    ],
    app_key: [
      {
        required: true,
        message: '请输入App Key',
        trigger: 'blur',
      },
    ],
    features: [
      {
        required: true,
        message: '请输入应用介绍',
        trigger: 'blur',
      },
    ],
  },
});

const initSelects = () => {
  const param = [{ enum_key: 'appType', enum_value: 'EnumAppType' }];
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, param).then(
    (response) => {
      appType.value = response.appType;
    },
  );
};
onMounted(() => {
  // 数据初始化
  initSelects();
});
onActivated(() => {
  addForm.value.resetFields();
});

const closeTab = (addForm: {
  resetFields?: any;
  validate?: () => Promise<any>;
}) => {
  addForm.resetFields();
  // closeCurrentTab({
  //   path: '/system/appAdmin',
  // });
  router.push({
    path: '/system/appAdmin',
  });
};

const save = (addForm: { validate: () => Promise<any> }) => {
  addForm.validate().then(() => {
    AppApi.AppApi.createAppApi(data.appForm).then(() => {
      // if (response.success === true) {
      //   ElMessage({
      //     message: '创建应用成功',
      //     type: 'success',
      //   });
      //   closeTab(addForm);
      // } else {
      //   ElMessage({
      //     message: response.message,
      //     type: 'error',
      //   });
      // }
      closeTab(addForm);
    });
  });
};
</script>
<template>
  <Page>
    <ElCard class="card">
      <ElForm
        ref="addForm"
        label-width="200px"
        :rules="data.rules"
        :model="data.appForm"
      >
        <template #header>
          <div style="display: inline-block; line-height: 32px">创建应用</div>
        </template>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="应用名称" class="required" prop="name">
              <ElInput v-model="data.appForm.name" maxlength="30" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="app_key" class="required" prop="app_key">
              <ElInput v-model="data.appForm.app_key" maxlength="200" />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="应用类型" class="required" prop="app_type">
              <ElSelect
                v-model="data.appForm.app_type"
                placeholder="应用类型"
                clearable
              >
                <ElOption
                  v-for="item in appType"
                  :key="item.value"
                  :label="item.key"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="应用标识" class="required" prop="app_code">
              <ElInput v-model="data.appForm.app_code" maxlength="200" />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="应用公钥" prop="app_public_key">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.app_public_key"
                maxlength="1000"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="应用私钥" prop="app_private_key">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.app_private_key"
                maxlength="1000"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="应用签名秘钥" prop="app_sign_key">
              <ElInput v-model="data.appForm.app_sign_key" maxlength="30" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="应用Token有效期(分钟)" prop="app_token_expiry">
              <ElInput
                v-model="data.appForm.app_token_expiry"
                maxlength="200"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="平台公钥" prop="plat_public_key">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.plat_public_key"
                maxlength="1000"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="平台私钥" prop="plat_private_key">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.plat_private_key"
                maxlength="1000"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="平台签名秘钥" prop="plat_sign_key">
              <ElInput v-model="data.appForm.plat_sign_key" maxlength="30" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="平台Token有效期(分钟)" prop="plat_token_expiry">
              <ElInput
                v-model="data.appForm.plat_token_expiry"
                maxlength="200"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="24">
            <ElFormItem label="其他参数（json）" prop="app_params">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.app_params"
                maxlength="1000"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5">
          <ElCol :span="12">
            <ElFormItem label="应用介绍" class="required" prop="features">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.features"
                maxlength="500"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="备注" prop="memo">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="data.appForm.memo"
                maxlength="500"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5" class="btn-group">
          <ElCol :span="24">
            <ElFormItem style="text-align: center">
              <ElButton style="margin-top: 36px" @click="closeTab(addForm)">
                取消
              </ElButton>
              <ElButton
                type="primary"
                style="margin-top: 36px"
                @click="save(addForm)"
              >
                保存
              </ElButton>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </ElCard>
  </Page>
</template>

<style lang="scss" scoped>
.card {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

.content {
  //width: 1000px;
  // margin: 50px auto;
}

.form {
  // width: 600px;
  margin: 50px auto;
}

.desc {
  width: 100%;
  padding: 0;
  color: rgb(0 0 0 / 45%);
}

.desc h3 {
  margin: 0 0 12px;
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
  color: rgb(0 0 0 / 45%);
}

.desc h4 {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: rgb(0 0 0 / 45%);
}

.desc p {
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 22px;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  color: red;
  content: '* ';
}

.el-upload-dragger {
  width: 500px;
}

.btn-group :deep(.el-form-item__content) {
  justify-content: center;
  margin-left: 0;
}
</style>
