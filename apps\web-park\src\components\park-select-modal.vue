<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeGridProps } from '@vben/plugins/vxe-table';

import type { NSEmployee } from '#/api';

import { ref, toRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { ElButton, ElMessage, ElText } from 'element-plus';

import { ParkParkApi } from '#/api';

// 查询参数类型
interface IFormValues {
  name: string;
}
interface IRowType {
  city_name: string;
  name: string;
  state_desc: string;
}

const selectParkList = ref<NSEmployee.IEmployeeParkListResult[]>([]);
const isMultiple = ref(false); // 车场是否支持多选
const isNoEmpty = ref(false); // 车场选择是否不能为空
const needAllParameter = ref(false); // 选择后的车场是否需要返回整个item对象(默认只返回id和name)
const [Modal, ModalApi] = useVbenModal({
  title: '车场选择',
  description:
    '点击表格的取消或选择按钮，或在已选择中点击删除图标，即可取消或选择车场',
  onOpenChange(isOpen) {
    if (isOpen) {
      const shareData = ModalApi.getData<Record<string, any>>();
      isMultiple.value = shareData.isMultiple;
      isNoEmpty.value = shareData.isNoEmpty || false;
      needAllParameter.value = shareData.needAllParameter || false;
      // 多选时，直接赋值
      if (isMultiple.value) {
        selectParkList.value = [...shareData.selectArray];
      } else {
        // 单选时，赋值第一个元素
        selectParkList.value =
          shareData.selectArray.length > 0 ? [shareData.selectArray[0]] : [];
      }
    }
  },
  onConfirm() {
    const shareData = ModalApi.getData<Record<string, any>>();
    if (isNoEmpty.value && selectParkList.value.length === 0) {
      return ElMessage.warning('请至少选择一个车场');
    } else if (shareData.confirmFn) {
      shareData.confirmFn(toRaw(selectParkList.value));
    }
  },
});

const formOptions: VbenFormProps = {
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '按停车场名称搜索',
      },
      defaultValue: '',
      fieldName: 'name',
      label: '停车场名称：',
    },
  ],
  submitButtonOptions: {
    show: false,
  },
  resetButtonOptions: {
    show: false,
  },
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  wrapperClass: 'flex-1',
  actionWrapperClass: 'hidden',
};

const gridOptions: VxeGridProps<IRowType> = {
  columns: [
    { field: 'city_name', title: '所属地区', minWidth: 120, width: 120 },
    { field: 'name', title: '停车场名称' },
    {
      field: 'state_desc',
      title: '车场状态',
      slots: { default: 'state' },
      minWidth: 120,
      width: 120,
    },
    {
      field: 'actions',
      title: '操作',
      slots: { default: 'actions' },
      minWidth: 80,
      width: 80,
    },
  ],
  rowConfig: {
    isHover: true,
  },
  keepSource: true,
  height: 'auto',
  pagerConfig: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10],
  },
  proxyConfig: {
    ajax: {
      query: getParkList,
    },
  },
};

/**
 * 获取停车场列表
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 * @returns {Promise<{items: IRowType[], total: number}>} 停车场列表和总数
 */
async function getParkList({ page }: any, formValues: IFormValues) {
  // 查询参数格式化
  const params = {
    name: formValues.name,
  };
  try {
    const res = await ParkParkApi.getParkListApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridClass: 'h-full',
});

// 一键清空
const clearSelectedPark = () => {
  selectParkList.value = [];
};

// 选择停车场
const selectPark = (park: NSEmployee.IEmployeeParkListResult) => {
  const selParkData = needAllParameter.value
    ? park
    : {
        park_id: park.park_id,
        park_name: park.park_name,
      };
  if (isMultiple.value) {
    selectParkList.value.push(selParkData);
  } else {
    selectParkList.value = [selParkData];
  }
};
</script>
<template>
  <Modal
    :draggable="true"
    :fullscreen-button="false"
    class="w-[1000px]"
    content-class="p-0 bg-background-deep"
  >
    <div class="flex flex-row">
      <!-- 左侧 -->
      <div class="m-4 h-[568px] w-full rounded-md">
        <Grid>
          <template #actions="{ row }">
            <ElButton
              v-if="!selectParkList.some((item) => item.park_id === row.id)"
              link
              type="primary"
              @click="
                selectPark({
                  ...row,
                  park_id: row.id,
                  park_name: row.name,
                })
              "
            >
              选择
            </ElButton>
            <ElButton
              v-else
              link
              type="danger"
              @click="
                selectParkList = selectParkList.filter(
                  (item) => item.park_id !== row.id,
                )
              "
            >
              取消
            </ElButton>
          </template>
          <template #state="{ row }">
            {{ row.state_desc }}
          </template>
        </Grid>
      </div>
      <!-- 右侧 -->
      <div class="bg-background m-4 ml-0 w-[450px] rounded-md px-2 pt-4">
        <div class="flex justify-between border-b pb-3">
          <ElText>已选择 ({{ selectParkList.length }})</ElText>
          <ElButton link type="danger" @click="clearSelectedPark">
            全部清空
          </ElButton>
        </div>
        <div class="h-[518px] overflow-y-auto px-2 pt-3">
          <div
            v-for="selectItem in selectParkList"
            :key="selectItem.park_id"
            class="flex items-center justify-between py-2"
          >
            <ElText :title="selectItem.park_name" line-clamp="1">
              {{ selectItem.park_name }}
            </ElText>
            <span
              class="icon-[ep--remove-filled] cursor-pointer text-gray-400 hover:text-red-500"
              @click="
                selectParkList = selectParkList.filter(
                  (item) => item.park_id !== selectItem.park_id,
                )
              "
            ></span>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
