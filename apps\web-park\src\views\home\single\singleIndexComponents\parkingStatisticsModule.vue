<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue';

import { VbenCountToAnimator } from '@vben/common-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { HomepageApi } from '#/api';

const chartRef = ref<any>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);

const bgImg = new URL(`/public/pie-bg.svg`, import.meta.url).href;

const pieData = ref<any>([
  { name: '空闲车位', value: 0 },
  { name: '长租在场', value: 0 },
  { name: '临停在场', value: 0 },
]);
const tableData = ref<any>([
  {
    name: '总车位',
    value: 0,
    bg: 'rgba(21, 112, 255, 0.08)',
    color: '#5570F1',
  },
  {
    name: '临停在场车俩',
    value: 0,
    bg: 'rgba(254, 206, 0, 0.08)',
    color: '#FFA53E',
  },
  {
    name: '长租在场车俩',
    value: 0,
    bg: 'rgba(194, 165, 249, 0.08)',
    color: '#A575FF',
  },
  {
    name: '空闲车位',
    value: 0,
    bg: 'rgba(21, 112, 255, 0.08)',
    color: '#5570F1',
  },
  {
    name: '平均停车时长',
    value: 0,
    bg: 'rgba(21, 112, 255, 0.08)',
    color: '#5570F1',
  },
  {
    name: '单车位价值',
    value: 0,
    bg: 'rgba(21, 112, 255, 0.08)',
    color: '#5570F1',
  },
]);

onMounted(() => {
  renderEcharts({
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: '0',
      icon: 'circle',
      itemWidth: 10,
      textStyle: {
        fontSize: 14,
      },
      itemGap: 20,
      itemHeight: 20,
    },
    graphic: [
      {
        type: 'image',
        id: 'logo',
        left: 'center',
        top: 'center',
        bounding: 'raw',
        style: {
          image: bgImg,
          opacity: 1,
        },
      },
    ],
    series: [
      {
        animationDelay() {
          return Math.random() * 100;
        },
        animationEasing: 'exponentialInOut',
        animationType: 'scale',
        avoidLabelOverlap: false,
        color: ['#5570F1', '#C2A5F9', '#FFCC91'],
        data: pieData.value,
        emphasis: {
          label: {
            fontSize: '12',
            fontWeight: 'bold',
            show: true,
            formatter: '{c|{c}辆}\n{b|{b}}',
            rich: {
              b: {
                color: '#666666',
                fontSize: 12,
                lineHeight: 20,
              },
              c: {
                color: '#333333',
                fontSize: 18,
              },
            },
          },
        },
        itemStyle: {
          borderRadius: 0,
          borderWidth: 2,
        },
        label: {
          position: 'center',
          show: false,
        },
        labelLine: {
          show: false,
        },
        name: '停车统计',
        radius: ['45%', '70%'],
        center: ['50%', '50%'],
        type: 'pie',
        roseType: 'area',
      },
    ],
  });
});
const fetchData = async (params: any) => {
  try {
    HomepageApi.countParkInfoApi(params).then((res) => {
      tableData.value[0].value = res.space_cnt || 0;
      tableData.value[1].value = res.parking_plate_cnt || 0;
      tableData.value[2].value = res.rent_plate_cnt || 0;
      tableData.value[3].value =
        tableData.value[0].value -
        (tableData.value[1].value + tableData.value[2].value);
      tableData.value[4].value = res.park_duration || 0;
      tableData.value[5].value = res.space_value || 0;

      pieData.value[0].value = tableData.value[3].value;
      pieData.value[1].value = tableData.value[2].value;
      pieData.value[2].value = tableData.value[1].value;
    });

    setTimeout(() => {
      const chartInstance = getChartInstance();
      chartInstance?.setOption({
        series: [
          {
            data: pieData.value,
          },
        ],
      });
    }, 1000);
  } finally {
    nextTick(() => {});
  }
};

defineExpose({
  fetchData,
});
</script>

<template>
  <div class="mt-3 flex h-full items-center justify-between">
    <EchartsUI ref="chartRef" width="40%" />
    <div class="flex flex-1 flex-wrap gap-2">
      <div
        v-for="(item, i) in tableData"
        :key="i"
        :style="{ backgroundColor: item.bg }"
        class="min-w-[calc(33.333%-0.5rem)] rounded-lg px-2 py-4 text-center"
      >
        <div class="text-base">{{ item.name }}</div>
        <div :style="{ color: item.color }" class="mt-1 text-xl font-bold">
          <VbenCountToAnimator
            :duration="2000"
            :end-val="Number(item.value)"
            :start-val="0"
            :decimals="i > 3 ? 2 : 0"
          />
        </div>
      </div>
    </div>
  </div>
</template>
