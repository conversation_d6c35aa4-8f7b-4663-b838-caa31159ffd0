<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSFinance } from '#/api/park/finance';

import { onBeforeMount, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElButton, ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, FinanceApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

import RefundDetailModal from './components/refund/refundDetailModal.vue';

defineOptions({
  name: 'LongRentPay', // 退款管理列表
});

interface IFormValues {
  time?: string[];
  park_id: string;
  park_name: string;
  refund_types?: number[];
  refund_states?: number[];
}

/**
 * 业务变量
 */
const refundTypeOptions = ref<CommonModule.IEnumItem[]>([]);
const refundStateOptions = ref<CommonModule.IEnumItem[]>([]);
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

const [RDModal, rdModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: RefundDetailModal,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      RefundRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  RefundRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  RefundRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'refund_types',
      label: '退款类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: refundTypeOptions,
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'refund_states',
      label: '退款状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: refundStateOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [],
      fieldName: 'time',
      label: '申请日期：',
      componentProps: {
        clearable: false,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '申请开始日期',
        endPlaceholder: '申请结束日期',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description退款管理管理列表
 */
const gridOptions: VxeGridProps<NSFinance.IPagingRefundOrderRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    {
      field: 'review_title',
      title: '审核标题',
      align: 'left',
      slots: { default: 'reviewTitle' },
      minWidth: 500,
    },
    { field: 'refund_type_desc', title: '退款类型', minWidth: 150 },
    {
      field: 'refund_state_desc',
      title: '退款状态',
      slots: { default: 'refundStateDesc' },
      minWidth: 150,
    },
    { field: 'apply_operator', title: '申请人', minWidth: 150 },
    { field: 'created_at', title: '申请时间', sortable: true, minWidth: 150 },
    {
      field: 'audit_reason',
      title: '取消退款原因',
      align: 'left',
      minWidth: 400,
    },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPagingRefundOrder,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [RefundTable, RefundRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取退款管理列表数据
 */
async function getPagingRefundOrder({ page }: any, formValues: IFormValues) {
  let params;
  if (isReset.value) {
    params = {
      park_id: '',
      park_name: '',
      start_time: '',
      end_time: '',
      refund_states: [],
      refund_types: [],
    };
    isReset.value = false;
  } else {
    params = {
      ...formValues,
      park_id: selectParkCheck.value?.park_id || '',
      park_name: selectParkCheck.value?.park_name || '',
      start_time: formValues!.time?.[0]
        ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
        : '',
      end_time: formValues!.time?.[1]
        ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
        : '',
    };
  }
  delete params.time;
  try {
    const res = await FinanceApi.getPagingRefundOrderApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 点击申请退款按钮
 */
const clickHandle = (row: NSFinance.IPagingRefundOrderRow, type: string) => {
  const { id, refund_type } = row;
  rdModalApi
    .setData({
      id,
      refundType: refund_type,
      type,
    })
    .open();
};

/**
 * 获取审核标题文案
 */
const getTitle = (row: NSFinance.IPagingRefundOrderRow) => {
  let str = `退款操作 - ${row.park_name}`;
  if (row.refund_money) {
    str += ` - 金额${row.refund_money}元`;
  }
  if (row.refund_time) {
    str += ` - ${row.refund_time}`;
  }
  return str;
};

const getRefundStateType = (state: number) => {
  if (state === 1) {
    return 'danger';
  } else if (state === 2) {
    return 'primary';
  } else {
    return 'success';
  }
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSelectOptionsData = async () => {
  // 获取退款类型,退款状态
  const params = [
    { enum_key: 'refundTypeList', enum_value: 'EnumRefundType' },
    { enum_key: 'refundStateList', enum_value: 'EnumRefundState' },
  ];
  try {
    const res = await Promise.all([
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, params),
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.ORDER, params),
    ]);
    refundTypeOptions.value = res[0]!.refundTypeList;
    refundStateOptions.value = res[1]!.refundStateList;
  } catch {
    refundTypeOptions.value = [];
    refundStateOptions.value = [];
  }
};

const loadData = () => {
  RefundRef.setLoading(true);
  setTimeout(() => {
    RefundRef.setLoading(false);
    RefundRef.query();
  }, 200);
};

onBeforeMount(() => {
  initSelectOptionsData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    RefundRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--退款管理表格 -->
    <RefundTable table-title="退款管理列表">
      <!-- 自定义-操作项列 -->
      <template #actions="{ row }">
        <ElButton
          v-if="row.refund_state === 1"
          link
          type="danger"
          @click="clickHandle(row, 'apply')"
        >
          退款
        </ElButton>
      </template>
      <!-- 自定义-审核标题列 -->
      <template #reviewTitle="{ row }">
        <ElButton link type="primary" @click="clickHandle(row, 'review')">
          <span class="w-[470px] truncate text-left">{{ getTitle(row) }}</span>
        </ElButton>
      </template>
      <!-- 自定义-退款状态列 -->
      <template #refundStateDesc="{ row }">
        <ElTag :type="getRefundStateType(row.refund_state)">
          {{ row.refund_state_desc }}
        </ElTag>
      </template>
    </RefundTable>
    <RDModal @update="loadData" />
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
