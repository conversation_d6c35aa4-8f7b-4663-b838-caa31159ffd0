<script setup name="AppDetail" lang="ts">
import type { LocationQueryValue } from 'vue-router';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { onActivated, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { ElCard, ElCol, ElRow } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AppApi } from '#/api';

const route = useRoute();

interface IParams {
  app_id: LocationQueryValue | LocationQueryValue[] | undefined;
  name: LocationQueryValue | LocationQueryValue[] | undefined;
  currentVersion: LocationQueryValue | LocationQueryValue[] | undefined;
  app_key: LocationQueryValue | LocationQueryValue[] | undefined;
  features: LocationQueryValue | LocationQueryValue[] | undefined;
  memo: LocationQueryValue | LocationQueryValue[] | undefined;
}
const paramP = ref<IParams>({
  app_id: '',
  name: '',
  currentVersion: '',
  app_key: '',
  features: '',
  memo: '',
});
onActivated(() => {
  // if ({} !== route.query) {
  if (!route.query) {
    return;
  }
  const param = route.query;
  paramP.value = {
    app_id: param.id,
    name: param.name,
    currentVersion: param.currentVersion,
    app_key: param.app_key,
    features: param.features,
    memo: param.memo,
  };
  // }
  // getList(data.appForm);
});

interface IRowType {
  version: string;
  login_name: string;
  memo: string;
  updator: string;
  updated_at: null | string;
}
/**
 * 表格配置
 * @description 员工管理列表
 */
const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: false,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    // 序号列 建议默认都配置
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    { field: 'version', title: '版本', minWidth: 100, width: 100 },
    { field: 'features', title: '版本更新说明', minWidth: 150 },
    { field: 'memo', title: '备注', minWidth: 150 },
    { field: 'updator', title: '更新人', minWidth: 150, width: 150 },
    { field: 'updated_at', title: '更新时间', minWidth: 200 },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const { rows, total } = await AppApi.AppApi.pagingHistoryAppApi({
          page: page.currentPage,
          limit: page.pageSize,
          ...paramP.value,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
};
const [DeatilTable] = useVbenVxeGrid({
  gridOptions,
});
</script>
<template>
  <Page>
    <ElCard class="card">
      <div class="clearfix mb-[10px]">
        <span>最新版本</span>
      </div>
      <ElRow :gutter="10">
        <ElCol :span="24">
          <ElRow :gutter="10">
            <ElCol :span="6">
              <span>应用名称：{{ paramP.name }}</span>
            </ElCol>
            <ElCol :span="6">
              <span>当前版本：{{ paramP.currentVersion }}</span>
            </ElCol>
            <ElCol :span="6">
              <span>App Key：{{ paramP.app_key }}</span>
            </ElCol>
            <ElCol :span="6">
              <span>更新说明：{{ paramP.features }}</span>
            </ElCol>
          </ElRow>
        </ElCol>
      </ElRow>
    </ElCard>
    <ElCard auto-content-height class="card" style="margin-top: 10px">
      <div style="height: calc(100vh - 270px)">
        <DeatilTable>
          <template #toolbar-actions>
            <div>版本信息</div>
          </template>
        </DeatilTable>
      </div>
    </ElCard>
  </Page>
</template>

<style lang="scss" scoped>
.card {
  height: 100%;
  vertical-align: middle;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  color: red;
  content: '* ';
}

.el-upload-dragger {
  width: 500px;
}
</style>
