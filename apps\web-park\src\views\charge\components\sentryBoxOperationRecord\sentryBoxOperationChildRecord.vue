<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSParkFee } from '#/api/park/fee';

import { onMounted, ref, toRaw, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElText } from 'element-plus'; // ElImage,

import useParkImageView from '#/adapter/park-image-view';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ParkFeeApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'SentryBoxOperationChildRecord', // 岗亭操作记录子列表
});

const props = defineProps({
  activeName: {
    type: String,
    default: 'specialRelease',
  },
  selectOptions: {
    // eslint-disable-next-line no-use-before-define
    type: Array<ISelectOptions>,
    default: () => [],
  },
});

interface ISelectOptions {
  label: string;
  value: string;
}

type IApiMap = {
  [key: string]: any;
};

interface IFormValues {
  park_id: string;
  park_name: string;
  plate_no?: string;
  operator_name?: string;
  time?: string[];
  in_start_time?: string;
  in_end_time?: string;
}

/**
 * 业务变量
 */
const [parkImageView] = useParkImageView();
const userStore = useUserStore();
const photoUrl = ref('');
const isReset = ref(false);
// const title = ref('');

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */
const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      SBOCRRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  SBOCRRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  SBOCRRef.reload();
};

/**
 * 注册图片预览弹窗
 */
// const [ViewPictureModal, VPModalRef] = useVbenModal();

/**
 * 预览图片公共方法
 */
const viewPicture = async (
  row: NSParkFee.ISentryBoxOperationRecordsRow,
  type?: string,
) => {
  const eventContext = JSON.parse(row.event_context!);
  if (
    ['entranceRangePole', 'plateNoCorrect', 'repeatEntrance'].includes(
      props.activeName,
    )
  ) {
    // 预览入场图片
    if (!eventContext.car_in_biz_no) return ElMessage.warning('暂无图片可查看');
    const res = await ParkFeeApi.getByCarInRecordIdApi(
      `${selectParkCheck.value?.park_id}/${eventContext.car_in_biz_no}`,
    );
    if (!res || !res.car_photo_url) return ElMessage.warning('暂无图片可查看');
    photoUrl.value = res.car_photo_url;
    // title.value = '入场图片';
  } else if (type === 'last') {
    // 预览上次入场图片
    if (!eventContext.last_car_in_biz_no)
      return ElMessage.warning('暂无图片可查看');
    const res = await ParkFeeApi.getByCarInRecordIdApi(
      `${selectParkCheck.value?.park_id}/${eventContext.last_car_in_biz_no}`,
    );
    if (!res || !res.car_photo_url) return ElMessage.warning('暂无图片可查看');
    photoUrl.value = res.car_photo_url;
    // title.value = '上次入场图片';
  } else {
    // 预览出场图片
    if (!eventContext.car_out_biz_no)
      return ElMessage.warning('暂无图片可查看');
    const res = await ParkFeeApi.getByCarOutRecordIdApi(
      `${selectParkCheck.value?.park_id}/${eventContext.car_out_biz_no}`,
    );
    if (!res || !res.out_car_photo_url)
      return ElMessage.warning('暂无图片可查看');
    photoUrl.value = res.out_car_photo_url;
    // title.value = '出场图片';
  }
  parkImageView(photoUrl.value);
  // VPModalRef.open();
};

const extraFormOptions = [
  {
    component: 'Input',
    defaultValue: '',
    fieldName: 'operator_name',
    label: '操作人',
    componentProps: {
      clearable: true,
      placeholder: '请输入操作人',
    },
  },
  {
    component: 'DatePicker',
    defaultValue: [],
    fieldName: 'time',
    label: '操作日期',
    componentProps: {
      clearable: true,
      type: 'datetimerange',
      style: {
        width: 'auto',
      },
      startPlaceholder: '操作开始时间',
      endPlaceholder: '操作结束时间',
    },
  },
];

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入车牌号',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const basicColumns = [
  { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
  { field: 'park_name', title: '停车场名称', minWidth: 100 },
  { field: 'plate_no', title: '车牌号', minWidth: 100 },
];

let extraColumns = [
  {
    field: 'event_context',
    title: '特殊放行详情',
    slots: { default: 'event_context' },
    minWidth: 200,
    width: 200,
  },
  {
    field: 'photo_url',
    title: '出场图片',
    slots: { default: 'photo_url' },
    minWidth: 100,
  },
  { field: 'charge_name', title: '操作人', minWidth: 200 },
  { field: 'updated_at', title: '操作时间', sortable: true, minWidth: 200 },
];

/**
 * 表格配置
 * @description 岗亭操作记录子管理列表
 */
const gridOptions: VxeGridProps<NSParkFee.ISentryBoxOperationRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    enabled: false,
    autoLoad: false,
    ajax: {
      query: getPagingInRecords,
    },
  },
};

/**
 * 获取岗亭操作记录子列表数据
 */
async function getPagingInRecords({ page }: any, formValues?: IFormValues) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  const params: IFormValues = {
    ...formValues,
    in_start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
  };
  delete params.time;
  if (props.activeName === 'repeatEntrance') {
    delete params.operator_name;
    delete params.in_start_time;
    delete params.in_end_time;
    // eslint-disable-next-line no-use-before-define
    SBOCRRef.formApi.setFieldValue('item', []);
  }
  const apiMap: IApiMap = {
    specialRelease: ParkFeeApi.getPagingSpecialReleaseRecordsApi,
    cancelRelease: ParkFeeApi.getPagingCancelReleaseRecordsApi,
    plateNoCorrect: ParkFeeApi.getPagingCorrectRecordsApi,
    entranceRangePole: ParkFeeApi.getPagingEntranceRecordsApi,
    exitRangePole: ParkFeeApi.getPagingExitRecordsApi,
    manualMatchingExit: ParkFeeApi.getPagingManualRecordsApi,
    repeatEntrance: ParkFeeApi.getPagingRepeatRecordsApi,
    rushedCar: ParkFeeApi.getPagingLossRecordsApi,
    changeRate: ParkFeeApi.getPagingRateRecordsApi,
  };
  try {
    const res = await apiMap[props.activeName]({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [SBOCRTable, SBOCRRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 *  重新加载列表数据
 */
const loadData = () => {
  SBOCRRef.setLoading(true);
  setTimeout(() => {
    SBOCRRef.setLoading(false);
    SBOCRRef.query();
  }, 200);
};

/**
 * 动态更新数据列表标题
 */
const getTableTitle = () => {
  const curTitle = props.selectOptions.find(
    (item) => item.value === props.activeName,
  )!.label;
  return `${curTitle}列表`;
};

/**
 * 动态更新搜索表单设置项内容
 */
const updateFormSchema = (val: string) => {
  SBOCRRef.formApi.setState((prev) => {
    const currentSchema = toRaw(prev?.schema) ?? [];
    // 如果是从其他tab切换到重复入场的tab下
    if (val === 'repeatEntrance') {
      return { schema: currentSchema.slice(0, -2) };
      // 如果是从重复入场的tab切换到其他tab下
    } else if (
      val !== 'repeatEntrance' &&
      !currentSchema.some((item) => item.fieldName === 'time')
    ) {
      return {
        schema: [...currentSchema, ...extraFormOptions],
      };
    }
    return {
      schema: currentSchema,
    };
  });
  const curType = val === 'rushedCar' ? '创建' : '操作';
  SBOCRRef.formApi.updateSchema([
    {
      fieldName: 'time',
      label: `${curType}日期`,
      componentProps: {
        startPlaceholder: `${curType}开始时间`,
        endPlaceholder: `${curType}结束时间`,
      },
    },
  ]);
};

/**
 * 动态更新数据列表设置项内容
 */
const updateTableGrid = (val: string) => {
  const curDetailTitle = props.selectOptions.find(
    (item) => item.value === val,
  )!.label;
  const curPhotoTitle = [
    'entranceRangePole',
    'plateNoCorrect',
    'repeatEntrance',
  ].includes(val)
    ? '入'
    : '出';
  let curField = ['charge_name', 'updated_at'];
  if (val !== 'specialRelease') {
    curField = ['cancelRelease', 'repeatEntrance', 'rushedCar'].includes(val)
      ? ['operator', 'created_at']
      : ['updator_name', 'updated_at'];
  }
  extraColumns = [
    {
      field: 'event_context',
      title: `${curDetailTitle}详情`,
      slots: { default: 'event_context' },
      minWidth: 200,
      width: 200,
    },
    {
      field: 'photo_url',
      title: `${curPhotoTitle}场图片`,
      slots: { default: 'photo_url' },
      minWidth: 100,
    },
    { field: curField[0]!, title: '操作人', minWidth: 200 },
    { field: curField[1]!, title: '操作时间', sortable: true, minWidth: 200 },
  ];
  if (val === 'repeatEntrance') {
    extraColumns.splice(1, 0, {
      field: 'last_photo_url',
      title: '上次入场图片',
      slots: { default: 'last_photo_url' },
      minWidth: 100,
    });
  }
  const res: any = [...basicColumns, ...extraColumns];
  SBOCRRef.setGridOptions({ columns: res });
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    SBOCRRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
  SBOCRRef.formApi.setState((prev) => {
    const currentSchema = toRaw(prev?.schema) ?? [];
    return {
      schema: [...currentSchema, ...extraFormOptions],
    };
  });
  const res: any = [...basicColumns, ...extraColumns];
  SBOCRRef.setGridOptions({ columns: res });
});

watch(
  () => props.activeName,
  (newVal) => {
    updateFormSchema(newVal);
    updateTableGrid(newVal);
  },
);
</script>
<template>
  <div class="h-full">
    <!-- 岗亭操作记录子管理表格 -->
    <SBOCRTable :table-title="getTableTitle()">
      <!-- 自定义-详情内容列 -->
      <template #event_context="{ row }">
        <ElText v-if="props.activeName === 'entranceRangePole'">
          {{ JSON.parse(row.event_context).in_reason }}
        </ElText>
        <ElText v-if="props.activeName === 'rushedCar'">
          {{ JSON.parse(row.event_context).flush_reason }}
        </ElText>
        <ElText v-else>
          {{ JSON.parse(row.event_context || row.event_content).out_reason }}
        </ElText>
      </template>
      <!-- 自定义-上次入场图片列(仅重复入场数据列有) -->
      <template #last_photo_url="{ row }">
        <ElButton link type="primary" @click="viewPicture(row, 'last')">
          查看图片
        </ElButton>
      </template>
      <!-- 自定义-入/出场图片列 -->
      <template #photo_url="{ row }">
        <ElButton link type="primary" @click="viewPicture(row)">
          查看图片
        </ElButton>
      </template>
    </SBOCRTable>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
    <!-- 图片预览弹窗 -->
    <!-- <ViewPictureModal :footer="false" :title="title" class="w-[650px]">
      <ElImage :src="photoUrl" fit="fill" />
    </ViewPictureModal> -->
  </div>
</template>
