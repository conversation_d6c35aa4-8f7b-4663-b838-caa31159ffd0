<script lang="ts" setup>
import type { FormInstance } from 'element-plus';

import type { NSPlatform } from '#/api';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { Msaterialsymbolshelp } from '@vben/icons';

import dayjs from 'dayjs';
import {
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElSelect,
  ElTooltip,
} from 'element-plus';

import { PlatformApi } from '#/api';

const isMustAudit = ref<number>(0);
// 表单实例
const formRef = ref<FormInstance>();
const defaultTime2: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59),
];
const baseDto = {
  name: '',
  merchant_coupon_id: '',
  valid: [],
  audit_mode: 1,
  total_count: 0,
  remainder_count: 0,
  use_time: 1,
};

// 表单数据
const formState = ref(baseDto);

// 表单验证规则
const rules = ref({
  name: [{ required: true, message: '请填写领卷码用途', trigger: 'blur' }],
  merchant_coupon_id: [
    { required: true, message: '请选择优免卷', trigger: 'blur' },
  ],
  valid: [
    { required: true, message: '请选择优免卷的可领取时间', trigger: 'blur' },
  ],
  audit_mode: [{ required: true, message: '请选择审核模式', trigger: 'blur' }],
  total_count: [
    { required: true, message: '请设置限制领卷人数', trigger: 'blur' },
  ],
  use_time: [
    { required: true, message: '请设置优免卷使用时间', trigger: 'blur' },
  ],
});

let query: Function | undefined = () => {};

const couponOptionsList = ref<NSPlatform.ICouponOptions[]>([]);

/**
 * 获取角色列表
 */
const initData = async () => {
  try {
    const res: NSPlatform.ICouponOptions[] | undefined =
      await PlatformApi.getListCouponMerchantsApi();
    couponOptionsList.value = res || [];
  } catch {
    couponOptionsList.value = [];
  }
};

/**
 * 注册弹窗
 */
const [Modal, ModalApi] = useVbenModal({
  draggable: true,
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      initData();
      formState.value = { ...baseDto };
      const shareData = ModalApi.getData<Record<string, any>>();
      query = shareData.query;
    }
  },
  onConfirm: () => {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        const params: NSPlatform.ICreateDrawCodeParams = {
          name: formState.value.name,
          merchant_coupon_id: formState.value.merchant_coupon_id,
          valid_start_time:
            dayjs(formState.value.valid[0]).format('YYYY-MM-DD HH:mm:ss') || '',
          valid_end_time:
            dayjs(formState.value.valid[1]).format('YYYY-MM-DD HH:mm:ss') || '',
          audit_mode: formState.value.audit_mode,
          total_count: formState.value.total_count,
          validDays: formState.value.use_time,
          plaza_audit: isMustAudit.value,
        };
        // return;
        await PlatformApi.createDrawCodeApi(params);
        ModalApi.close();
        query?.();
      }
    });
  },
});

const changeCouponAction = (id: string) => {
  const selectedCoupon = couponOptionsList.value.find((item) => item.id === id);
  const couponParamsData = JSON.parse(
    (selectedCoupon as NSPlatform.ICouponOptions).coupon_params,
  );
  if (
    couponParamsData.couponType === 4 ||
    (couponParamsData.couponType === 3 &&
      couponParamsData.discountRatio > 0 &&
      couponParamsData.discountRatio <= 6)
  ) {
    isMustAudit.value = 1;
    formState.value.audit_mode = 2;
  } else {
    isMustAudit.value = 0;
    formState.value.audit_mode = 1;
  }
  formState.value.remainder_count = selectedCoupon
    ? selectedCoupon.remainder_count
    : 0;
};

onMounted(() => {
  initData();
});
</script>
<template>
  <Modal
    :close-on-click-modal="false"
    :fullscreen-button="false"
    title="添加领卷码"
    class="w-[650px]"
  >
    <ElForm
      ref="formRef"
      :model="formState"
      :rules="rules"
      class="p-4"
      label-position="right"
      label-width="auto"
    >
      <ElFormItem label="领卷码用途：" prop="name">
        <ElInput v-model="formState.name" clearable placeholder="领卷码用途" />
      </ElFormItem>
      <ElFormItem label="选择优免卷：" prop="merchant_coupon_id">
        <ElSelect
          v-model="formState.merchant_coupon_id"
          clearable
          placeholder="请选择优免卷"
          @change="changeCouponAction"
        >
          <ElOption
            v-for="item in couponOptionsList"
            :key="item.id"
            :value="item.id"
            :label="item.coupon_meta_name"
          >
            {{ item.coupon_meta_name }}
          </ElOption>
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="可领取时间：" prop="valid">
        <ElDatePicker
          v-model="formState.valid"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :clearable="true"
          :default-time="defaultTime2"
        />
      </ElFormItem>
      <div class="mb-4 flex flex-col items-start justify-center">
        <ElFormItem label="审核模式：" prop="audit_mode" class="!mb-0">
          <ElRadioGroup
            v-model="formState.audit_mode"
            :disabled="Boolean(isMustAudit)"
          >
            <ElRadio :value="1">免审核</ElRadio>
            <ElRadio :value="2">
              <div class="flex items-center">
                需审核
                <ElTooltip effect="light" placement="top-end">
                  <template #content>
                    当前优免券折扣幅度较大，为保障使用合规性，该<br />券在发放与领取后需经广场管理人员审核通过方可<br />生效。
                  </template>
                  <Msaterialsymbolshelp class="size-5" />
                </ElTooltip>
              </div>
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <div
          v-if="formState.audit_mode === 2"
          class="ml-4 w-full pl-[105px] text-sm text-red-500"
        >
          {{
            !isMustAudit
              ? '此模式下车主扫码领卷后需要您在“领卷审核”模块中审核确认其才能核销'
              : '此模式下车主扫码领卷后需要广场人员在“业务审核”模块中审核确认其才能使用'
          }}
        </div>
      </div>
      <div class="flex flex-row items-center justify-start">
        <ElFormItem label="限制领卷人数：" prop="total_count" class="!mb-0">
          <ElInputNumber
            v-model="formState.total_count"
            :min="0"
            :max="formState.remainder_count || 0"
          />
        </ElFormItem>
        <div v-if="formState.remainder_count" class="ml-4 text-red-500">
          优惠卷还剩：{{ formState.remainder_count }}张
        </div>
      </div>
      <!-- <div class="mt-[15px] flex flex-row items-center justify-start">
        <ElFormItem label="优免卷使用时间：" prop="total_count" class="!mb-0">
          <div>
            <ElTooltip effect="light" placement="top-end">
              <template #content>
                自领取之日起N天内有效，示例：用户领取后7天<br />内可使用。
              </template>
              <Msaterialsymbolshelp class="size-5" />
            </ElTooltip>
          </div>
          <ElInputNumber v-model="formState.use_time" :min="1" />
        </ElFormItem>
      </div> -->
    </ElForm>
  </Modal>
</template>
