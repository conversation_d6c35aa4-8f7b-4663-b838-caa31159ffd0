<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee, NSStatisticalReport } from '#/api';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { StatisticalReportApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'ParkSpaceAvailability', // 车位利用率报表列表
});

interface IFormValues {
  rangeTime?: string[];
  time_type: number;
}

/**
 * 业务变量
 */
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      PARef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  PARef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  loadData();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'organization_ids',
      label: '组织架构：',
      componentProps: {
        clearable: true,
        placeholder: '请输入组织架构',
      },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'park_types',
    //   label: '查询范围',
    //   componentProps: {
    //     multiple: true,
    //     clearable: true,
    //     options: timeRangeTypeOptions,
    //   },
    // },
    // {
    //   component: 'TimeRange',
    //   defaultValue: [],
    //   fieldName: 'rangeTime',
    //   label: '',
    //   componentProps: {
    //     clearable: true,
    //     type: 'datetimerange',
    //     style: {
    //       width: 'auto',
    //     },
    //     startPlaceholder: '时间开始日期',
    //     endPlaceholder: '时间结束日期',
    //   },
    // },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description车位利用率报表管理列表
 */
const gridOptions: VxeGridProps<NSStatisticalReport.IListSpaceUseRatiosRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    enabled: false,
  },
  // 正常配置列
  columns: [
    {
      field: 'statistics_date',
      title: '统计日期',
      sortable: true,
      minWidth: 100,
    },
    { field: 'park_name', title: '车场名称', minWidth: 100 },
    { field: 'region_name', title: '大区', minWidth: 100 },
    { field: 'organizational_structure', title: '城市分公司', minWidth: 100 },
    { field: 'province_name', title: '所在省份', minWidth: 100 },
    { field: 'city_name', title: '所在城市', minWidth: 100 },
    { field: 'district_name', title: '所在区域', minWidth: 100 },
    {
      field: 'space_number',
      title: '临停车位数',
      sortable: true,
      minWidth: 120,
    },
    {
      field: 'parking_total_hours',
      title: '临停总时长（小时）',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'average_use_ratio',
      title: '平均日利用率',
      sortable: true,
      minWidth: 120,
    },
    {
      field: 'use_ratio_plate_no',
      title: '临停小于24小时有牌车车位利用率',
      sortable: true,
      showHeaderOverflow: false,
      width: 160,
    },
    {
      field: 'use_ratio_no_plate_no',
      title: '临停小于24小时无牌车车位利用率',
      sortable: true,
      showHeaderOverflow: false,
      width: 160,
    },
    {
      field: 'midday_fastigium_use_ratio',
      title: '午间高峰利用率 (12:00~15:00)',
      sortable: true,
      showHeaderOverflow: false,
      width: 126,
    },
    {
      field: 'night_fastigium_use_ratio',
      title: '晚间高峰利用率 (18:00~21:00)',
      sortable: true,
      showHeaderOverflow: false,
      width: 126,
    },
    {
      field: 'business_hours_use_ratio',
      title: '营业时间利用率 (10:30~22:30)',
      sortable: true,
      showHeaderOverflow: false,
      width: 126,
    },
    {
      field: 'morning_no_business_hours_use_ratio',
      title: '非营业时间利用率（早）(00:00~10:29)',
      sortable: true,
      showHeaderOverflow: false,
      width: 170,
    },
    {
      field: 'night_no_business_hours_use_ratio',
      title: '非营业时间利用率（晚）(22:31~23:59)',
      sortable: true,
      showHeaderOverflow: false,
      width: 170,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getListSpaceUseRatiosApi,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [ParkSpaceAvailabilityTable, PARef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取车位利用率报表列表数据
 */
async function getListSpaceUseRatiosApi(_: any, formValues: IFormValues) {
  let params;
  if (isReset.value) {
    params = {
      park_id: '',
      park_name: '',
      organization_ids: '',
      department_name: '',
      start_time: '',
      end_time: '',
      time_type: 3,
    };
    isReset.value = false;
  } else {
    params = {
      ...formValues,
      park_id: selectParkCheck.value?.park_id || '',
      park_name: selectParkCheck.value?.park_name || '',
      start_time:
        formValues?.rangeTime?.[0] ||
        dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      end_time: formValues?.rangeTime?.[1] || dayjs().format('YYYY-MM-DD'),
      time_type: 3,
    };
    delete params.rangeTime;
  }
  try {
    const res = await StatisticalReportApi.getListSpaceUseRatiosApi({
      // page: page.currentPage,
      // limit: page.pageSize,
      organization_ids: '',
      department_name: '',
      ...params,
    });
    return {
      items: res,
    };
  } catch {
    return {
      items: [],
    };
  }
}

const loadData = () => {
  PARef.setLoading(true);
  setTimeout(() => {
    PARef.setLoading(false);
    PARef.query();
  }, 200);
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--车位利用率报表管理表格 -->
    <ParkSpaceAvailabilityTable table-title="" />
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
