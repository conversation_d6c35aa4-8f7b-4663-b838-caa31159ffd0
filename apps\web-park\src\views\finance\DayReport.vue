<script lang="ts" setup>
import { onBeforeMount, ref, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { ElSegmented } from 'element-plus';

import { CommonApi, CommonModule } from '#/api';

import DayReportByMoney from './components/dayReport/dayReportByMoney.vue';
import DayReportByTime from './components/dayReport/dayReportByTime.vue';

defineOptions({
  name: 'DayReport', // 金额日报表+按次日报表
});

const activeName = ref('');
const selectOptions = [
  { label: '金额', value: 'money' },
  { label: '按次', value: 'time' },
];
const dayReportByMoneyRef = useTemplateRef('dayReportByMoneyRef');
const dayReportByTimeRef = useTemplateRef('dayReportByTimeRef');

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = async () => {
  // 获取车辆属性
  const params = [
    {
      enum_key: 'types',
      enum_value: 'EnumParkType',
    },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    dayReportByMoneyRef.value!.parkTypeOptions = res.types;
    dayReportByTimeRef.value!.parkTypeOptions = res.types;
    activeName.value = 'money';
  } catch (error) {
    console.error(error);
  }
};

onBeforeMount(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <template #title>
      <ElSegmented
        v-model="activeName"
        :options="selectOptions"
        size="default"
      />
    </template>
    <DayReportByMoney
      v-show="activeName === 'money'"
      ref="dayReportByMoneyRef"
    />
    <DayReportByTime v-show="activeName === 'time'" ref="dayReportByTimeRef" />
  </Page>
</template>
