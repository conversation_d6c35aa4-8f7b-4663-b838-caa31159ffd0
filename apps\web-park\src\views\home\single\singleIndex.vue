<script lang="ts" setup>
import { onMounted, onUnmounted, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElCol, ElRow } from 'element-plus';

import AssessmentIndexModule from './singleIndexComponents/assessmentIndexModule.vue';
import BulletinBoardModule from './singleIndexComponents/bulletinBoardModule.vue';
import CommonCard from './singleIndexComponents/commonCard.vue';
import CommonProblem from './singleIndexComponents/commonProblem.vue';
import CustomerServicePhone from './singleIndexComponents/customerServicePhone.vue';
import LongLeaseModule from './singleIndexComponents/longLeaseModule.vue';
import ParkingTransactionModule from './singleIndexComponents/parkingStatisticsModule.vue';
import TempStopModule from './singleIndexComponents/TempStopModule.vue';
import TimeRangerModule from './singleIndexComponents/timeRangerModule.vue';
import TodoListModule from './singleIndexComponents/todoListModule.vue';
import TurnoverRecordModule from './singleIndexComponents/turnoverRecordModule.vue';

// 每秒更新时间

const emits = defineEmits(['filter']);
const days = [
  '星期日',
  '星期一',
  '星期二',
  '星期三',
  '星期四',
  '星期五',
  '星期六',
];
const date = new Date();
const dayIndex = date.getDay();
const timeStr = ref<any>(null);
timeStr.value = `${dayjs().format('YYYY-MM-DD')} ${days[dayIndex]}`;
const currentTime = ref('');

function updateTime() {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString();
}

const intervalId = ref<any>(null); // 筛选框
onMounted(() => {
  updateTime(); // 初始获取时间
  intervalId.value = setInterval(updateTime, 1000);
});

const queryParam = reactive<any>({
  time_unit: '3',
  // date: getDefaultDate(3)
  date: [
    dayjs(Date.now() - 86_400_000).format('YYYY-MM-DD'),
    dayjs(Date.now() - 86_400_000).format('YYYY-MM-DD'),
  ],
});
const dealEmitData = () => {
  const useQueryParam = { ...queryParam };
  if (useQueryParam.date) {
    useQueryParam.start_date = useQueryParam.date[0];
    useQueryParam.end_date = useQueryParam.date[1];
    delete useQueryParam.date;
  }
  return useQueryParam;
};
const parkingTransactionModuleRef = ref<any>(null);
const longLeaseModuleRef = ref<any>(null);
const todoListModuleRef = ref<any>(null);
const tempStopModuleRef = ref<any>(null);
const turnoverRecordModuleRef = ref<any>(null);
const assessmentIndexModuleRef = ref<any>(null);
const handleFilter = () => {
  emits('filter', dealEmitData());
  parkingTransactionModuleRef.value.fetchData(dealEmitData());
  longLeaseModuleRef.value.fetchData(dealEmitData());
  todoListModuleRef.value.fetchData(dealEmitData());
  tempStopModuleRef.value.fetchData(dealEmitData());
  turnoverRecordModuleRef.value.fetchData(dealEmitData());
  assessmentIndexModuleRef.value.fetchData(dealEmitData());
};
onMounted(() => {
  handleFilter();
});
onUnmounted(() => clearInterval(intervalId.value));
</script>

<template>
  <Page
    auto-content-height
    class="flex h-full flex-col"
    header-class="p-0 h-[32px] flex items-center"
  >
    <template #title>
      <div class="flex min-w-[1670px] items-center justify-between px-4">
        <div class="text-primary flex-1 text-sm">
          {{ timeStr }} {{ currentTime }}
        </div>
        <TimeRangerModule
          v-model:date="queryParam.date"
          v-model:unit="queryParam.time_unit"
          v-model:park_id="queryParam.park_id"
          :teleported="false"
          popper-class="single-index-popper"
          size="small"
          style="width: 505px; margin-right: 10px"
          @change="handleFilter"
        />
      </div>
    </template>
    <!-- <div class="min-h-[340px]"> -->
    <!-- 第一行 43% -->
    <ElRow :gutter="12" class="h-[43%] min-h-[301px] min-w-[1670px]">
      <ElCol :span="9" class="h-full">
        <CommonCard title="停车统计">
          <ParkingTransactionModule ref="parkingTransactionModuleRef" />
        </CommonCard>
      </ElCol>
      <ElCol :span="15" class="h-full">
        <ElRow :gutter="12" class="h-1/2">
          <ElCol :span="24" class="h-full pb-[12px]">
            <CommonCard title="长租交易数据">
              <LongLeaseModule ref="longLeaseModuleRef" />
            </CommonCard>
          </ElCol>
        </ElRow>
        <ElRow :gutter="12" class="h-1/2">
          <ElCol :span="15" class="h-full !max-w-[60%] !flex-[0_0_60%]">
            <CommonCard :is-show-border="false" title="待办事项">
              <TodoListModule ref="todoListModuleRef" />
            </CommonCard>
          </ElCol>
          <ElCol :span="9" class="h-full !max-w-[40%] !flex-[0_0_40%]">
            <CommonCard title="全国车场客服电话">
              <CustomerServicePhone />
            </CommonCard>
          </ElCol>
        </ElRow>
      </ElCol>
    </ElRow>
    <!-- 第二行 24% -->
    <ElRow :gutter="12" class="box-border h-[24%] min-h-[176px] min-w-[1670px]">
      <ElCol :span="18" class="h-full py-[12px]">
        <CommonCard title="临停交易数据">
          <TempStopModule ref="tempStopModuleRef" />
        </CommonCard>
      </ElCol>
      <ElCol :span="6" class="h-full py-[12px]">
        <CommonCard title="常见问题">
          <CommonProblem />
        </CommonCard>
      </ElCol>
    </ElRow>
    <!-- 第三行 33% -->
    <ElRow :gutter="12" class="h-[33%] min-h-[220px] min-w-[1670px]">
      <ElCol :span="9" class="h-full">
        <CommonCard title="交班记录">
          <TurnoverRecordModule ref="turnoverRecordModuleRef" />
        </CommonCard>
      </ElCol>
      <ElCol :span="9" class="h-full">
        <CommonCard title="考核指标">
          <AssessmentIndexModule ref="assessmentIndexModuleRef" />
        </CommonCard>
      </ElCol>
      <ElCol :span="6" class="h-full">
        <CommonCard title="全国公告栏">
          <BulletinBoardModule />
        </CommonCard>
      </ElCol>
    </ElRow>
    <!-- </div> -->
  </Page>
</template>
