<script name="PermissionConfigModule" lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onActivated, reactive, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
  ElText,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  CommonApi,
  CommonModule,
  PermissionApi,
  PermissionGroupApi,
} from '#/api';

const props = defineProps({
  menu: {
    type: Object,
    required: true,
    default: () => ({
      id: undefined,
      name: undefined,
    }),
  },
});

const createForm = ref();
const updateForm = ref();
const total = ref(0);
const tableData = ref<any>([]);
const permissionGroupList = ref<any>([]);
const typeList = ref<any>([]);
const data = reactive<any>({
  permissionCreateForm: {
    page_id: undefined,
    name: '',
    code: '',
    permission_group_id: '',
  },
  permissionUpdateForm: {
    page_id: undefined,
    id: undefined,
    name: '',
    code: '',
    permission_group_id: '',
    page_name: '',
  },
  queryParams: {
    page_id: undefined,
    page: 1,
    limit: 30,
  },
  permissionRules: {
    name: [
      {
        required: true,
        message: '请输入权限名称',
        trigger: 'blur',
      },
    ],
    code: [
      {
        required: true,
        message: '请输入权限编码',
        trigger: 'blur',
      },
    ],
    permission_group_id: [
      {
        required: true,
        message: '请选择权限分组',
        trigger: 'blur',
      },
    ],
  },
});
interface RowType {
  name: string;
  code: string;
  permission_group_id: string;
  type: string;
}
const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: '',
  },
  columns: [
    // { align: 'left', title: '', type: 'checkbox', width: 40 },
    { field: 'name', title: '权限名称' },
    { field: 'code', title: '权限代码' },
    {
      field: 'permission_group_id',
      title: '权限分组',
      slots: { default: 'permission_group_id' },
    },
    { field: '', title: '权限类型' },
    { slots: { default: 'action' }, title: '操作' },
  ],
  exportConfig: {},
  height: 'auto', // 如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素，否则会出现高度闪动问题
  keepSource: true,

  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const { rows, total } = await PermissionApi.pagingPermissionsApi({
          limit: page.pageSize,
          page: page.currentPage,
          page_id: data.queryParams.page_id,
        });
        tableData.value = rows;
        const getData = {
          items: rows,
          total,
        };
        return getData;
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
// 页面初始化获取权限组列表和枚举
const initSelects = () => {
  PermissionGroupApi.permissionGroupListApi().then((response) => {
    if (response) {
      permissionGroupList.value = response;
    }
  });
  const param = [
    {
      enum_key: 'typeList',
      enum_value: 'EnumPermissionType',
    },
  ];
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.SYSTEM, param).then(
    (response) => {
      typeList.value = response.typeList;
    },
  );
};
onActivated(() => {
  initSelects();
});

watch(
  () => props.menu,
  (newVal) => {
    if (newVal.id === undefined) {
      tableData.value = [];
      total.value = 0;
    } else {
      data.queryParams.page_id = newVal.id;
      gridApi.query();
    }
  },
);
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
/**
 * 新建权限
 */
const [ModalPermission, modalPermissionApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    createForm.value.validate().then(() => {
      PermissionApi.createPermissionApi(data.permissionCreateForm).then(() => {
        modalPermissionApi.close();
        createForm.value.resetFields();
        gridApi.query();
      });
    });
  },
  onCancel: () => {
    createForm.value.resetFields();
    modalPermissionApi.close();
  },
});
// 新建权限
const addPermission = () => {
  if (props.menu.type === 'menu' || props.menu.id === undefined) {
    ElMessage({
      message: '请先选择一条页面数据',
      type: 'warning',
    });
    return;
  }
  data.permissionCreateForm.page_id = props.menu.id;
  data.permissionCreateForm.page_name = props.menu.name;
  modalPermissionApi.open();
};
const [ModalPermissionUpdate, modalPermissionUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    updateForm.value.validate().then(() => {
      PermissionApi.updatePermissionApi(data.permissionUpdateForm).then(() => {
        modalPermissionUpdateApi.close();
        updateForm.value.resetFields();
        gridApi.query();
      });
    });
  },
  onCancel: () => {
    updateForm.value.resetFields();
    modalPermissionUpdateApi.close();
  },
});
// 修改权限
const upPermission = (row: {
  code: any;
  id: any;
  name: any;
  permission_group_id: any;
}) => {
  data.permissionUpdateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    permission_group_id: row.permission_group_id,
    page_name: props.menu.name,
  };
  modalPermissionUpdateApi.open();
};
// 删除权限
const delPermission = (row: { id: any }) => {
  const ids = [row.id];
  showConfirmModal({
    title: '权限删除',
    description: '确定删除此权限吗?',
    onConfirm: async () => {
      PermissionApi.deletePermissionApi(ids).then(() => {
        gridApi.query();
      });
    },
  });
};
</script>
<template>
  <Modal :fullscreen-button="false" content-class="min-h-[80px]">
    <div class="flex h-[80px] items-center px-4">
      <ElText size="large">{{ modalState.description }}</ElText>
    </div>
  </Modal>
  <ElCard
    class="mt-3 overflow-hidden"
    permission-cardshadow="never"
    style="height: calc(100vh - 374px)"
  >
    <template #header>
      <div class="card-header">
        <span>权限配置</span>
        <div>
          <ElButton type="primary" @click="addPermission"> 添加权限 </ElButton>
        </div>
      </div>
    </template>
    <div auto-content-height style="height: calc(100vh - 480px)">
      <Grid>
        <template #permission_group_id="{ row }">
          <span>
            {{
              permissionGroupList.find((item: any) => {
                return item.id === row.permission_group_id;
              }).group_name
            }}
          </span>
        </template>

        <template #action="{ row }">
          <ElButton type="primary" @click="upPermission(row)"> 修改 </ElButton>
          <ElButton type="danger" @click="delPermission(row)">删除</ElButton>
        </template>
      </Grid>
    </div>
  </ElCard>
  <!-- 添加权限 -->
  <ModalPermission
    :fullscreen-button="false"
    class="w-[650px]"
    title="添加权限"
  >
    <ElForm
      ref="createForm"
      :model="data.permissionCreateForm"
      :rules="data.permissionRules"
      label-width="100px"
    >
      <ElFormItem label="页面名称" prop="page_name">
        <ElInput
          v-model="data.permissionCreateForm.page_name"
          :disabled="true"
        />
      </ElFormItem>
      <ElFormItem label="权限分组" prop="permission_group_id">
        <ElSelect
          v-model="data.permissionCreateForm.permission_group_id"
          filterable
          style="width: 100%"
        >
          <ElOption
            v-for="item in permissionGroupList"
            :key="item.id"
            :label="item.group_name"
            :value="item.id"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="权限名称" prop="name">
        <ElInput
          v-model="data.permissionCreateForm.name"
          placeholder="请输入"
        />
      </ElFormItem>
      <ElFormItem label="权限编码" prop="code">
        <ElInput
          v-model="data.permissionCreateForm.code"
          placeholder="请输入"
        />
      </ElFormItem>
    </ElForm>
  </ModalPermission>
  <!-- 修改权限 -->
  <ModalPermissionUpdate
    :fullscreen-button="false"
    class="w-[650px]"
    title="修改权限"
  >
    <ElForm
      ref="updateForm"
      :model="data.permissionUpdateForm"
      :rules="data.permissionRules"
      label-width="100px"
    >
      <ElFormItem label="页面名称" prop="page_name">
        <ElInput
          v-model="data.permissionUpdateForm.page_name"
          :disabled="true"
        />
      </ElFormItem>
      <ElFormItem label="权限分组" prop="permission_group_id">
        <ElSelect
          v-model="data.permissionUpdateForm.permission_group_id"
          style="width: 100%"
        >
          <ElOption
            v-for="item in permissionGroupList"
            :key="item.id"
            :label="item.group_name"
            :value="item.id"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="权限名称" prop="name">
        <ElInput
          v-model="data.permissionUpdateForm.name"
          placeholder="请输入"
        />
      </ElFormItem>
      <ElFormItem label="权限编码" prop="code">
        <ElInput
          v-model="data.permissionUpdateForm.code"
          placeholder="请输入"
        />
      </ElFormItem>
    </ElForm>
  </ModalPermissionUpdate>
</template>

<style lang="scss" scoped>
:deep(.ElCard__header) {
  padding: 6px 10px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.permission-card {
  margin-top: 10px;
}
</style>
