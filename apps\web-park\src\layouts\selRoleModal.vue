<script lang="ts" setup>
import type { BasicDict } from '@vben/types';

import { onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';
import { useAccessStore, useUserStore } from '@vben/stores';

import {
  ElForm,
  ElFormItem,
  ElMessage,
  ElOption,
  ElSelect,
} from 'element-plus';

import { RoleApi } from '#/api';

const router = useRouter();

const pageState = reactive<{
  curRoleId: number;
  options: BasicDict[];
}>({
  options: [],
  curRoleId: 0,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  onConfirm() {
    changeRoleAction();
  },
});

function changeRoleAction() {
  if (pageState.curRoleId === useUserStore().userInfo?.userEntity.role_id) {
    ElMessage.info('您已处于当前角色，无需切换');
    return;
  }
  modalApi.setState({ loading: true });
  RoleApi.switchRoleApi(pageState.curRoleId).then((res) => {
    const accessStore = useAccessStore();
    accessStore.setAccessToken(res);
    modalApi.setState({ loading: false });
    ElMessage.success('切换角色成功');
    // 刷新页面
    router.go(0);
  });
}

onMounted(() => {
  pageState.options = useUserStore().userRoleList;
  pageState.curRoleId = useUserStore().userInfo?.userEntity.role_id || 0;
});
</script>
<template>
  <Modal
    :fullscreen-button="false"
    title="切换角色"
    title-tooltip="切换角色会重新获取权限和菜单，如果您有未保存的工作，请先保存！"
  >
    <ElForm
      :model="pageState"
      class="mt-8 px-8"
      label-width="auto"
      size="large"
    >
      <ElFormItem label="选择角色：">
        <ElSelect v-model="pageState.curRoleId" placeholder="请选择角色">
          <ElOption
            v-for="item in pageState.options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </ElSelect>
      </ElFormItem>
    </ElForm>
  </Modal>
</template>
