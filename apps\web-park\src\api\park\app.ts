import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};
export const AppApi = {
  /**
   * 分页查询APP应用
   */
  pagingAppApi(data: any) {
    return requestClient.post<any>('/console/park/app/pagingApp', data);
  },

  /**
   * 创建APP信息
   */
  createAppApi(data: any) {
    return requestClient.post<any>('/console/park/app/createApp', data, config);
  },

  /**
   * 编辑APP信息
   */
  updateAppApi(data: any) {
    return requestClient.post<any>('/console/park/app/updateApp', data, config);
  },

  /**
   * 分页查询版本历史信息
   */
  pagingHistoryAppApi(data: any) {
    return requestClient.post<any>('/console/park/app/pagingHistoryApp', data);
  },

  /**
   * 查询单个APP对象
   */
  getAppApi(appId: string) {
    return requestClient.get<any>(`/console/park/app/getApp?appId=${appId}`);
  },

  /**
   * 提交新版本信息
   */
  releaseAppApi(data: any) {
    return requestClient.post<any>(
      '/console/park/app/releaseApp',
      data,
      config,
    );
  },
  /**
   * 分管车场授权
   */
  parkingAuthorityApi(data: any) {
    return requestClient.post<any>(
      '/console/park/app/parkingAuthority',
      data,
      config,
    );
  },

  /**
   * 查询员工授权充车场信息
   */
  appParkListApi(data: any) {
    return requestClient.post<any>(`/console/park/app/appParkList/${data}`);
  },
};
