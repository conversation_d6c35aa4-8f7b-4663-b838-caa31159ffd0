<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton, ElForm, ElFormItem, ElInput, ElText } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PermissionGroupApi } from '#/api';

import Detail from './permissionComponents/PermissionGroupDetail.vue';

const addFormRef = ref<any>(null);
const editFormRef = ref<any>(null);
const detailRef = ref<any>(null);
const rules = {
  group_name: [
    {
      required: true,
      message: '请输入权限组名称',
      trigger: 'blur',
    },
  ],
};
interface IdataFromValues {
  group_name: string;
  id?: string;
}
interface IdetailFormValues {
  name: string;
  code: string;
  type_display: string;
  id?: string;
  group_name?: string;
}
interface IdataValues {
  form: IdataFromValues;
  updateForm: IdataFromValues;
  detailForm: IdetailFormValues;
}
const data = reactive<IdataValues>({
  form: {
    group_name: '',
  },
  updateForm: {
    id: '',
    group_name: '',
  },
  detailForm: {
    name: '',
    code: '',
    type_display: '',
  },
});
// 列表页
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '输入权限组名称',
      },
      defaultValue: '',
      fieldName: 'group_name',
      label: '权限组名称',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  // submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
interface RowType {
  name: string;
  code: string;
  permission_group_id: string;
  type: string;
}
const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: false,
    labelField: 'name',
  },
  columns: [
    { field: 'id', title: 'ID', width: 120 },
    { field: 'group_name', title: '权限组名称' },
    { slots: { default: 'action' }, title: '操作', width: 300 },
  ],
  exportConfig: {},
  height: 'auto', // 如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素，否则会出现高度闪动问题
  keepSource: true,

  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } =
          await PermissionGroupApi.pagingPermissionGroupApi({
            limit: page.pageSize,
            page: page.currentPage,
            ...formValues,
          });
        const items = {
          items: rows,
          total,
        };
        return items;
      },
    },
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
};
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const seeDetail = (params: {
  code: string;
  limit: number;
  name: string;
  page: number;
  type_display: string;
}) => {
  data.detailForm = params;
  detailRef.value.seeDetail(data.detailForm);
  setTimeout(() => {
    detailRef.value.gridApi.query();
  }, 80);
};

onMounted(() => {
  // gridApi.query()
});

/**
 * 新建权限
 */
const [ModalPermissionGroup, modalPermissionGroupApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    addFormRef.value.validate().then(() => {
      PermissionGroupApi.createPermissionGroupApi(data.form)
        .then(() => {
          addFormRef.value.resetFields();
          gridApi.query();
          modalPermissionGroupApi.close();
        })
        .catch(() => {
          gridApi.query();
        });
    });
  },
  onCancel: () => {
    modalPermissionGroupApi.close();
    addFormRef.value.resetFields();
  },
});
const handleAdd = () => {
  data.form = {
    group_name: '',
  };
  modalPermissionGroupApi.open();
};

/**
 * 修改权限
 */
const [ModalPermissionGroupUpdate, modalPermissionGroupUpdateApi] =
  useVbenModal({
    draggable: true,
    onConfirm: () => {
      editFormRef.value.validate().then(() => {
        PermissionGroupApi.updatePermissionGroupApi(data.updateForm)
          .then(() => {
            gridApi.query();
            modalPermissionGroupUpdateApi.close();
            editFormRef.value.resetFields();
          })
          .catch(() => {
            gridApi.query();
          });
      });
    },
    onCancel: () => {
      modalPermissionGroupUpdateApi.close();
      editFormRef.value.resetFields();
    },
  });
const upRow = (row: { group_name: any; id: any }) => {
  data.updateForm = {
    id: row.id,
    group_name: row.group_name,
  };
  modalPermissionGroupUpdateApi.open();
};

const delRow = (row: { id: string }) => {
  showConfirmModal({
    title: '权限组删除',
    description: '删除权限组，确定删除吗？',
    onConfirm: async () => {
      PermissionGroupApi.deletePermissionGroupApi(row.id).then(() => {
        gridApi.query();
      });
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <Grid>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElButton type="primary" @click="handleAdd()">新 增</ElButton>
      </template>
      <template #action="{ row }">
        <ElButton link type="primary" @click="seeDetail(row)"> 查看 </ElButton>
        <ElButton link type="primary" @click="upRow(row)"> 修改 </ElButton>
        <ElButton link type="danger" @click="delRow(row)"> 删除 </ElButton>
      </template>
    </Grid>
    <ModalPermissionGroup
      :fullscreen-button="false"
      class="w-[650px]"
      title="添加权限组"
    >
      <ElForm
        ref="addFormRef"
        :model="data.form"
        :rules="rules"
        label-width="100px"
      >
        <ElFormItem label="权限组名称" prop="group_name">
          <ElInput v-model="data.form.group_name" :autofocus="true" />
        </ElFormItem>
      </ElForm>
    </ModalPermissionGroup>
    <ModalPermissionGroupUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="添加权限组"
    >
      <ElForm
        ref="editFormRef"
        :model="data.updateForm"
        :rules="rules"
        label-width="100px"
      >
        <ElFormItem label="权限组名称" prop="group_name">
          <ElInput v-model="data.updateForm.group_name" :autofocus="true" />
        </ElFormItem>
      </ElForm>
    </ModalPermissionGroupUpdate>
    <Detail ref="detailRef" />
  </Page>
</template>
