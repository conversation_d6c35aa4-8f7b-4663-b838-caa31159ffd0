import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};

/**
 * 分页查询权限
 */
export async function pagingPermissionsApi(data: any) {
  return requestClient.post<any>('/console/permission/pagingPermissions', data);
}

/**
 * 创建权限
 */
export async function createPermissionApi(data: any) {
  return requestClient.post<any>(
    '/console/permission/createPermission',
    data,
    config,
  );
}

/**
 * 修改权限
 */
export async function updatePermissionApi(data: any) {
  return requestClient.post<any>(
    '/console/permission/updatePermission',
    data,
    config,
  );
}

/**
 * 删除权限
 */
export async function deletePermissionApi(data: any) {
  return requestClient.post<any>('/console/permission/deletePermission', data);
}
export const PermissionApi = {
  pagingPermissionsApi,
  createPermissionApi,
  updatePermissionApi,
  deletePermissionApi,
};
