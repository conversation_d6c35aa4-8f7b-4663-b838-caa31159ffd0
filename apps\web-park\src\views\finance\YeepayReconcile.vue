<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee, NSPay } from '#/api';

import { onMounted, reactive, ref, toRaw } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { ElButton, ElMessage } from 'element-plus';
import printJS from 'print-js';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PayApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';
import { saveToFile } from '#/utils';

defineOptions({
  name: 'YeepayReconcile', // 易宝对账列表
});

interface IFormValues {
  park_id?: string;
  park_name?: string;
  month?: string;
}

/**
 * 业务变量
 */
const userStore = useUserStore();
const summaryData = ref<Array<NSPay.IYeeReconciliationVosRow>>([]);
let totalInfoData = reactive<NSPay.IMonthYeeSettleResult>({
  pay_park_summary_vo: {
    count: 0,
    date: '',
    sum_should_pay_money: 0,
    sum_total_money: 0,
  },
  pay_rent_summary_vo: {
    count: 0,
    date: '',
    sum_should_pay_money: 0,
    sum_total_money: 0,
  },
  refund_summary_vo: {
    count: 0,
    date: '',
    sum_real_refund_amount: 0,
    sum_refund_amount: 0,
    sum_refund_merchant_fee: 0,
  },
  yee_reconciliation_vos: [],
});

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      queryFormApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onSubmit = (values: Record<string, any>) => {
  // eslint-disable-next-line no-use-before-define
  getPagingShiftHandoverRecords(cloneDeep(values));
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  queryFormApi.resetForm();
  selectParkCheck.value = null;
  // eslint-disable-next-line no-use-before-define
  YRSRef.setGridOptions({
    data: [],
  });
  // eslint-disable-next-line no-use-before-define
  YRSIRef.setGridOptions({
    data: [],
  });
  // eslint-disable-next-line no-use-before-define
  YRLIRef.setGridOptions({
    data: [],
  });
  // eslint-disable-next-line no-use-before-define
  YRRRef.setGridOptions({
    data: [],
  });
};

/**
 * 搜索表单配置
 */
const [QueryForm, queryFormApi] = useVbenForm({
  // 默认展开
  collapsed: false,
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    formItemClass: 'pb-2',
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 水平布局
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'DatePicker',
      defaultValue: '',
      fieldName: 'month',
      label: '月份：',
      componentProps: {
        clearable: true,
        type: 'month',
        'value-format': 'YYYY-MM',
        style: {
          width: 'auto',
        },
        placeholder: '请选择月份',
      },
    },
  ],
  // 是否可展开
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  submitButtonOptions: {
    content: '搜索',
  },
  actionWrapperClass: 'pb-2',
  wrapperClass:
    'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:flex flex-row items-center',
});

const commonGridOptions = {
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  pagerConfig: {
    enabled: false,
  },
};

/**
 * 表格配置
 * @description易宝对账管理列表
 */
const summaryGridOptions: VxeGridProps<
  NSPay.IMonthYeeSettleResult['yee_reconciliation_vos']
> = {
  ...commonGridOptions,
  // 表格高度 自动
  height: '100%',
  // 正常配置列
  columns: [
    { field: 'date', title: '账单日期', sortable: true, minWidth: 150 },
    { field: 'merchant_no', title: '商户编号', minWidth: 150 },
    {
      field: 'total_amount',
      title: '交易总金额',
      sortable: true,
      minWidth: 150,
    },
    { field: 'number', title: '交易总笔数', sortable: true, minWidth: 150 },
    {
      field: 'paid_in_handling_fees',
      title: '实收手续费',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'total_settlement_amount',
      title: '结算总金额',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'settlement_handling_fee',
      title: '结算手续费',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'total_amount_received',
      title: '到账金额',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
    },
  ],
};

const incomeGridOptions: VxeGridProps<
  NSPay.IMonthYeeSettleResult['pay_park_summary_vo']
> = {
  ...commonGridOptions,
  minHeight: 80,
  // 正常配置列
  columns: [
    { field: 'date', minWidth: 200, slots: { header: 'date_header' } },
    {
      field: 'sum_should_pay_money',
      title: '应收总金额(元)',
      sortable: true,
      minWidth: 100,
    },
    {
      field: 'sum_total_money',
      title: '实收总金额(元)',
      sortable: true,
      minWidth: 100,
    },
    { field: 'count', title: '交易成功笔数', sortable: true, minWidth: 100 },
  ],
};

const refundGridOptions: VxeGridProps<
  NSPay.IMonthYeeSettleResult['refund_summary_vo']
> = {
  ...commonGridOptions,
  minHeight: 80,
  // 正常配置列
  columns: [
    { field: 'date', minWidth: 200, slots: { header: 'date_header' } },
    {
      field: 'sum_refund_amount',
      title: '退款总金额（元）',
      sortable: true,
      minWidth: 100,
    },
    { field: 'count', title: '退款总笔数', sortable: true, minWidth: 100 },
  ],
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  async cellClick({ row }) {
    if (row.date === '月合计') return;
    // eslint-disable-next-line no-use-before-define
    YRSIRef.setLoading(true);
    // eslint-disable-next-line no-use-before-define
    YRLIRef.setLoading(true);
    // eslint-disable-next-line no-use-before-define
    YRRRef.setLoading(true);
    try {
      const res = await PayApi.getDayYeeSettleApi(
        selectParkCheck.value?.park_id!,
        row.date,
      );
      // eslint-disable-next-line no-use-before-define
      setRecordsData(res);
    } catch (error) {
      console.error(error);
    } finally {
      // eslint-disable-next-line no-use-before-define
      YRSIRef.setLoading(false);
      // eslint-disable-next-line no-use-before-define
      YRLIRef.setLoading(false);
      // eslint-disable-next-line no-use-before-define
      YRRRef.setLoading(false);
    }
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [YRSummarizeTable, YRSRef] = useVbenVxeGrid({
  gridOptions: summaryGridOptions,
  gridEvents,
});

const [YRStopIncomeTable, YRSIRef] = useVbenVxeGrid({
  gridOptions: incomeGridOptions,
});

const [YRLongIncomeTable, YRLIRef] = useVbenVxeGrid({
  gridOptions: incomeGridOptions,
});

const [YRRefundTable, YRRRef] = useVbenVxeGrid({
  gridOptions: refundGridOptions,
});

const setRecordsData = (originData?: NSPay.IMonthYeeSettleResult) => {
  const stopIncomeData = [
    {
      date: '月合计',
      count: totalInfoData.pay_park_summary_vo?.count || 0,
      sum_total_money: totalInfoData.pay_park_summary_vo?.sum_total_money || 0,
      sum_should_pay_money:
        totalInfoData.pay_park_summary_vo?.sum_should_pay_money || 0,
    },
  ];
  const longIncomeData = [
    {
      date: '月合计',
      count: totalInfoData.pay_rent_summary_vo?.count || 0,
      sum_total_money: totalInfoData.pay_rent_summary_vo?.sum_total_money || 0,
      sum_should_pay_money:
        totalInfoData.pay_rent_summary_vo?.sum_should_pay_money || 0,
    },
  ];
  const refundData = [
    {
      date: '月合计',
      count: totalInfoData.refund_summary_vo?.count || 0,
      sum_refund_amount:
        totalInfoData.refund_summary_vo?.sum_refund_amount || 0,
      sum_real_refund_amount:
        totalInfoData.refund_summary_vo?.sum_real_refund_amount || 0,
      sum_refund_merchant_fee:
        totalInfoData.refund_summary_vo?.sum_refund_merchant_fee || 0,
    },
  ];
  if (originData?.pay_park_summary_vo) {
    stopIncomeData.push(originData.pay_park_summary_vo!);
  }
  if (originData?.pay_rent_summary_vo) {
    longIncomeData.push(originData.pay_rent_summary_vo!);
  }
  if (originData?.refund_summary_vo) {
    refundData.push(originData?.refund_summary_vo!);
  }
  YRSIRef.setGridOptions({
    data: stopIncomeData,
  });
  YRLIRef.setGridOptions({
    data: longIncomeData,
  });
  YRRRef.setGridOptions({
    data: refundData,
  });
};

/**
 * 获取易宝对账列表数据
 */
const getPagingShiftHandoverRecords = async (formValues: IFormValues) => {
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  YRSRef.setLoading(true);
  try {
    const res = await PayApi.getMonthYeeSettleApi(
      selectParkCheck.value?.park_id,
      formValues.month!,
    );
    totalInfoData = res;
    if (!res.yee_reconciliation_vos || res.yee_reconciliation_vos.length === 0)
      return;
    const data = res.yee_reconciliation_vos;
    const firstData = [
      {
        date: '月合计',
        merchant_no: '-',
        total_amount: data[0]!.total_amount,
        number: data[0]!.number,
        paid_in_handling_fees: data[0]!.paid_in_handling_fees,
        total_settlement_amount: data[0]!.total_settlement_amount,
        settlement_handling_fee: data[0]!.settlement_handling_fee,
        total_amount_received: data[0]!.total_amount_received,
      },
    ];
    data.shift();
    summaryData.value = [...firstData, ...data];
    YRSRef.setGridOptions({
      data: summaryData.value,
    });
    setRecordsData();
  } catch (error) {
    console.error(error);
  } finally {
    YRSRef.setLoading(false);
  }
};

/**
 * 下载易宝数据对账单
 */
const downloadSettleFile = async (row: NSPay.IYeeReconciliationVosRow) => {
  row.downLoadLoading = true;
  try {
    const res = await PayApi.getSettleFileDownloadApi(
      selectParkCheck.value?.park_id!,
      row.date,
    );
    const fileName = res.disposition
      .split(';')[1]
      .split('filename=')[1]
      .replaceAll('"', '');
    saveToFile(res.data, decodeURIComponent(fileName));
  } catch (error) {
    console.error(error);
  } finally {
    row.downLoadLoading = false;
  }
};

/**
 * 打印易宝交易汇总数据
 */
const printData = () => {
  printJS({
    printable: cloneDeep(summaryData.value),
    properties: [
      { field: 'date', displayName: '账单日期' },
      { field: 'merchant_no', displayName: '商户编号' },
      { field: 'total_amount', displayName: '交易总金额' },
      { field: 'number', displayName: '交易总笔数' },
      { field: 'paid_in_handling_fees', displayName: '实收手续费' },
      { field: 'total_settlement_amount', displayName: '结算总金额' },
      { field: 'settlement_handling_fee', displayName: '结算手续费' },
      { field: 'total_amount_received', displayName: '到账金额' },
    ],
    type: 'json',
    header: '停车场交易汇总（易宝数据）',
    gridHeaderStyle:
      'padding: 10px 16px;font-size: 14px;font-weight: 400;border: 0.5px solid #333333;',
    gridStyle:
      'padding: 5px 8px;border: 0.5px solid #333333;text-align: center;font-size: 12px;',
  });
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    queryFormApi.setFieldValue('park_id', user.park_names[0]);
    queryFormApi.getValues().then(async (res) => {
      if (toRaw(res).month) {
        getPagingShiftHandoverRecords({ month: toRaw(res).month });
      }
    });
  }
});
</script>
<template>
  <!--易宝对账管理表格 -->
  <Page>
    <div>
      <QueryForm class="bg-white p-2 pt-5" />
    </div>
    <div class="flex h-[calc(100vh-14.5rem)] flex-row pt-3">
      <div class="h-full w-[60%] bg-white">
        <YRSummarizeTable>
          <!-- 表格顶部左侧按钮栏 -->
          <template #toolbar-actions>
            <span class="text-[1rem]">
              停车场交易汇总（易宝数据）
              <ElButton
                type="primary"
                :disabled="summaryData.length === 0"
                @click="printData"
              >
                打印当前页
              </ElButton>
            </span>
          </template>
          <!-- 自定义-操作项列 -->
          <template #actions="{ row }">
            <span v-if="row.date === '月合计'">-</span>
            <ElButton
              v-else
              link
              type="primary"
              :loading="row.downLoadLoading"
              @click.stop="downloadSettleFile(row)"
            >
              下载对账单
            </ElButton>
          </template>
        </YRSummarizeTable>
      </div>
      <div class="ml-3 h-full w-[40%] bg-white">
        <div>
          <YRStopIncomeTable table-title="临停收入汇总（惠达平台数据）">
            <template #date_header>
              <div class="first-col">
                <div class="first-col-top">类别</div>
                <div class="first-col-bottom">日期</div>
              </div>
            </template>
          </YRStopIncomeTable>
        </div>
        <div>
          <YRLongIncomeTable table-title="长租收入汇总（惠达平台数据）">
            <template #date_header>
              <div class="first-col">
                <div class="first-col-top">类别</div>
                <div class="first-col-bottom">日期</div>
              </div>
            </template>
          </YRLongIncomeTable>
        </div>
        <div>
          <YRRefundTable table-title="退款汇总（惠达平台数据）">
            <template #date_header>
              <div class="first-col">
                <div class="first-col-top">类别</div>
                <div class="first-col-bottom">日期</div>
              </div>
            </template>
          </YRRefundTable>
        </div>
      </div>
    </div>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>

<style lang="scss" scoped>
.first-col {
  position: relative;
  min-width: 200px;
  height: 40px;

  &::before {
    position: absolute;
    top: 20px;
    left: 50px;
    width: 100px;
    height: 1px;
    content: '';
    background: rgb(195 195 195 / 50%);
    transform: rotate(25deg);
  }

  .first-col-top {
    position: absolute;
    top: 2px;
    right: 20px;
  }

  .first-col-bottom {
    position: absolute;
    bottom: 2px;
    left: 20px;
  }
}
</style>
