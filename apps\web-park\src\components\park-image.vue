<script lang="ts" setup>
import type { ImageProps } from 'element-plus';

import { ElImage, ElText } from 'element-plus';

interface Props extends Partial<ImageProps> {
  src?: string;
  width?: number | string;
  height?: number | string;
  noImageText?: string;
}

// 组件属性定义
const props = withDefaults(defineProps<Props>(), {
  width: '70px',
  height: '40px',
  fit: 'cover',
  noImageText: '未采集',
  src: '',
});
</script>

<template>
  <ElImage
    v-if="src"
    v-bind="$props"
    :preview-src-list="[src]"
    :src="src"
    :style="{
      width: props.width,
      height: props.height,
      verticalAlign: 'middle',
    }"
    close-on-press-escape
    hide-on-click-modal
    lazy
  >
    <!-- 占位图 -->
    <template #placeholder>
      <slot name="placeholder">
        <div class="flex h-full w-full items-center justify-center">
          <div
            class="icon-[ep--loading] h-5 w-5 animate-spin text-gray-100"
          ></div>
        </div>
      </slot>
    </template>

    <template #error>
      <slot name="error">
        <div class="flex h-full w-full items-center justify-center bg-gray-100">
          <div class="icon-[ep--picture-filled] h-5 w-5 text-gray-400"></div>
        </div>
      </slot>
    </template>

    <!-- 转发其他插槽 -->
    <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData"></slot>
    </template>
  </ElImage>
  <ElText v-else type="danger">{{ props.noImageText }}</ElText>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
