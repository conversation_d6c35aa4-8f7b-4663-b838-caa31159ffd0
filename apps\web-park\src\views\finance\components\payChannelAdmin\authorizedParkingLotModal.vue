<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSPay } from '#/api';
import type { NSFinance } from '#/api/park/finance';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PayApi } from '#/api';

defineOptions({
  name: 'AuthorizedParkingLotModal', // 支付渠道(微信/支付宝)-授权停车场弹窗
});

/**
 * 业务变量
 */
const curId = ref('');
const curChannelType = ref('');
const commonColumns = [
  {
    field: 'prk_park_id',
    title: 'ID',
    minWidth: 100,
    sortable: true,
  },
  {
    field: 'prk_park_name',
    title: '名称',
    minWidth: 150,
  },
  {
    field: 'prk_park_code',
    title: '编号',
    minWidth: 200,
  },
];

/**
 * 表格配置
 * @description停车缴费管理列表
 */
const gridOptions: VxeGridProps<NSFinance.IPagingDayReportsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  minHeight: 100,
  // 正常配置列
  columns: [],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPagePayChannelPark,
    },
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [AuthorizedParkingLotTable, APLTRef] = useVbenVxeGrid({
  gridOptions,
});

/**
 * 车场列表弹窗配置
 */
const [AuthorizedParkingLotModal, authorizedParkingLotModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = authorizedParkingLotModalApi.getData<Record<string, any>>();
      let extraColumns = [];
      extraColumns =
        data.channelType === 'wechat'
          ? [
              {
                field: 'sub_mchid',
                title: '名称',
                minWidth: 200,
              },
              {
                field: 'sub_appid',
                title: '编号',
                minWidth: 200,
              },
            ]
          : [
              {
                field: 'app_auth_token',
                title: '授权令牌',
                minWidth: 200,
              },
            ];
      curId.value = data.id;
      curChannelType.value = data.channelType;
      APLTRef.setGridOptions({ columns: [...commonColumns, ...extraColumns] });
    }
  },
});

/**
 * 获取停车缴费列表数据
 */
async function getPagePayChannelPark({ page }: any) {
  // 查询参数格式化
  const params = {
    page: page.currentPage,
    limit: page.pageSize,
    pay_channel_id: curId.value,
  };
  try {
    let res: NSPay.IPagePayChannelParkResult = {
      current_page: 1,
      page_count: 20,
      rows: [],
      total: 0,
    };
    res = await (curChannelType.value === 'wechat'
      ? PayApi.getPageWxPayChannelParkApi(params)
      : PayApi.getPageAliPayChannelParkApi(params));
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}
</script>
<template>
  <!-- 授权车场列表 -->
  <AuthorizedParkingLotModal
    :footer="false"
    class="w-[650px]"
    title="授权车场列表"
  >
    <AuthorizedParkingLotTable table-title="授权车场列表" />
  </AuthorizedParkingLotModal>
</template>
