import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};
export const PushApi = {
  /**
   * 消息推送配置设定
   */
  insertApi(data: any) {
    return requestClient.post<any>(
      '/console/park/push/message/insert',
      data,
      config,
    );
  },

  /**
   * 消息推送查询
   */
  pushMessageDetailApi(data: any) {
    return requestClient.post<any>(
      `/console/park/push/message/pushMessageDetail?id=${data.id}`,
      {},
    );
  },
};
