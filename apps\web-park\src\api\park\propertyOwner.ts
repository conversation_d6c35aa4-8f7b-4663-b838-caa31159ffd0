import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};
export const PropertyOwnerApi = {
  /**
   * 分页查找产权方
   */
  pagingPropertyOwnerApi(data: any) {
    return requestClient.post<any>(
      '/console/park/propertyOwner/pagingPropertyOwner',
      data,
    );
  },

  /**
   * 新建产权方
   */
  createPropertyOwnerApi(data: any) {
    return requestClient.post<any>(
      '/console/park/propertyOwner/addPropertyOwner',
      data,
      config,
    );
  },

  /**
   * 修改产权方
   */
  updatePropertyOwnerApi(data: any) {
    return requestClient.post<any>(
      '/console/park/propertyOwner/updatePropertyOwner',
      data,
      config,
    );
  },

  /**
   * 删除产权方
   */
  deletePropertyOwnerApi(id: any) {
    return requestClient.post<any>(
      `/console/park/propertyOwner/deletePropertyOwner/${id}`,
      {},
      config,
    );
  },

  /**
   * 产权方列表
   */
  listPropertyOwnerApi() {
    return requestClient.get<any>(
      '/console/park/propertyOwner/listPropertyOwner',
    );
  },
};
