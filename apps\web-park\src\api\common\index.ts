import { requestClient } from '#/api/request';

export namespace CommonModule {
  export enum EnumModuleType {
    CHANNEL = 'channel',
    ORDER = 'order',
    PARK = 'park',
    SYSTEM = 'system',
  }

  export interface IEnumItem {
    key: string;
    label?: string;
    [key: string]: any;
  }
}

/**
 * 查询枚举
 * @params module
 */
export async function findEnumsApi(
  module: CommonModule.EnumModuleType,
  data: Record<string, any>,
) {
  return requestClient
    .post<any>(`/console/common/findEnums/${module}`, data)
    .then((res) => {
      Object.entries(res).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          res[key] = value.map((item) => ({
            ...item,
            label: item.key,
          }));
        }
      });
      return res;
    });
}

export const CommonApi = {
  findEnumsApi,
};
