import { requestClient } from '#/api/request';

export namespace NSParkPark {
  export interface IListParkParams {
    page: number;
    limit: number;
    name: string;
  }

  export interface IListParkResult {
    current_page: number;
    page_count: number;
    rows: any[];
    total: number;
  }
}

/**
 * 获取停车场列表
 */
export async function getParkListApi(params: NSParkPark.IListParkParams) {
  return requestClient.post<NSParkPark.IListParkResult>(
    '/console/park/park/listPark',
    params,
  );
}
