<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee, NSPay } from '#/api';
import type { NSParkRegion } from '#/api/park/region';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal, VbenCountToAnimator } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElText } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ParkRegionApi, PayApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'CashTransfer', // 停车找零明细列表
});

interface IFormValues {
  time?: string[];
  start_time?: string;
  end_time?: string;
  park_id: string;
  park_region_id: string;
  order_no?: string;
  plate_no?: string;
  state?: string[];
}

/**
 * 业务变量
 */
const parkRegionOptions = ref<NSParkRegion.IListParkRegionItem[][]>([]);
const transferStatesOptions = ref([
  { label: '已受理', value: 'ACCEPTED' },
  { label: '处理中', value: 'PROCESSING' },
  { label: '待收款用户确认', value: 'WAIT_USER_CONFIRM' },
  { label: '转账结果未明确', value: 'TRANSFERING' },
  { label: '转账成功', value: 'SUCCESS' },
  { label: '转账失败', value: 'FAIL' },
  { label: '撤销中', value: 'CANCELING' },
  { label: '已撤销', value: 'CANCELLED' },
]);
const transferCountList = ref([
  { label: '找零总额', change: 0, sum: 0 },
  { label: '找零成功', change: 0, sum: 0 },
  { label: '找零失败', change: 0, sum: 0 },
]);
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      if (!selectParkCheck.value) {
        // eslint-disable-next-line no-use-before-define
        CTRef.formApi.setFieldValue('park_id', '');
        parkRegionOptions.value = [];
        // eslint-disable-next-line no-use-before-define
        CTRef.formApi.setFieldValue('park_region_id', '');
        PSModalRef.close();
        return;
      }
      // eslint-disable-next-line no-use-before-define
      CTRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      parkRegionOptions.value = await ParkRegionApi.getListParkRegionApi(
        selectParkCheck.value?.park_id,
      );
      // eslint-disable-next-line no-use-before-define
      CTRef.formApi.setFieldValue(
        'park_region_id',
        parkRegionOptions.value[0]?.value,
      );
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [ConfirmModal, ConfirmModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ConfirmModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ConfirmModalRef.close();
  };
  ConfirmModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  CTRef.formApi.resetForm();
  selectParkCheck.value = null;
  parkRegionOptions.value = [];
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  CTRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Select',
      fieldName: 'park_region_id',
      label: '子场名称：',
      componentProps: {
        multiple: false,
        clearable: false,
        options: parkRegionOptions,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'order_no',
      label: '业务单号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入业务单号',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入车牌号',
      },
    },

    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'state',
      label: '找零状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: transferStatesOptions,
      },
    },

    {
      component: 'DatePicker',
      defaultValue: [
        `${dayjs().subtract(1, 'month').format('YYYY-MM-DD')}`,
        `${dayjs().format('YYYY-MM-DD')}`,
      ],
      fieldName: 'time',
      label: '申请时间：',
      componentProps: {
        clearable: false,
        type: 'daterange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
  // 自定义重置表单方法
  handleReset: onReset,
};

/**
 * 表格配置
 * @description停车找零明细管理列表
 */
const gridOptions: VxeGridProps<NSPay.IWxTransferPageRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    { field: 'order_no', title: '业务单号', minWidth: 300 },
    { field: 'transfer_bill_no', title: '微信支付单号', minWidth: 300 },
    { field: 'openid', title: '收款用户OpenID', minWidth: 300 },
    { field: 'park_name', title: '停车场名称', minWidth: 150 },
    { field: 'park_region_name', title: '子场名称', minWidth: 150 },
    { field: 'plate_no', title: '车牌号码', minWidth: 150 },
    {
      field: 'transfer_amount',
      title: '找零金额',
      sortable: true,
      minWidth: 100,
    },
    { field: 'created_at', title: '创建时间', sortable: true, minWidth: 200 },
    {
      field: 'state',
      title: '找零状态',
      minWidth: 150,
      slots: { default: 'state' },
    },
    { field: 'fail_reason', title: '失败原因', minWidth: 200 },
    {
      field: 'last_order_update_time',
      title: '最后状态变更时间',
      sortable: true,
      minWidth: 200,
    },
    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
      width: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: getPagingParkPayRecords,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [CashTransferTable, CTRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 *  获取停车找零明细金额数目信息
 */
const getCountTransferByState = async (
  { page }: any,
  formValues: IFormValues,
) => {
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.time;
  try {
    const res = await PayApi.getCountTransferByStateApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    transferCountList.value = [
      {
        label: '找零总额',
        change: res.total_small_change,
        sum: res.total_small_sum,
      },
      {
        label: '找零成功',
        change: res.success_small_change,
        sum: res.success_small_sum,
      },
      {
        label: '找零失败',
        change: res.failure_small_change,
        sum: res.failure_small_sum,
      },
    ];
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
};

/**
 * 获取停车找零明细列表数据
 */
async function getPagingParkPayRecords({ page }: any, formValues: IFormValues) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  getCountTransferByState({ page }, formValues!);
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.time;
  try {
    const res = await PayApi.getWxTransferPageApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 撤销取消商户转账
 */
const cancelTransfer = (orderNo: string) => {
  showConfirmModal({
    title: '撤销商户转账',
    description: '撤销商户转账，确定撤销吗？',
    onConfirm: async () => {
      await PayApi.cancelTransferApi(orderNo);
      ElMessage.success('撤销商户转账成功');
      CTRef.query();
    },
  });
};

/**
 * 重新发起商家转账
 */
const reTryTransfer = (id: string) => {
  showConfirmModal({
    title: '重新发起商家转账',
    description: '重新发起商家转账，确定发起吗？',
    onConfirm: async () => {
      await PayApi.reTryTransferApi(id);
      ElMessage.success('重新发起商家转账成功');
      CTRef.query();
    },
  });
};

const loadData = () => {
  CTRef.setLoading(true);
  setTimeout(() => {
    CTRef.setLoading(false);
    CTRef.query();
  }, 200);
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    CTRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--停车找零明细管理表格 -->
    <CashTransferTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <div class="flex flex-wrap gap-4 text-center font-bold">
          <div v-for="(item, index) in transferCountList" :key="index">
            <span class="pr-1 font-normal">{{ item.label }}:</span>
            <VbenCountToAnimator
              :decimals="2"
              :duration="1000"
              :end-val="item.change"
              :start-val="0"
              color="#F56C6C"
              suffix="元"
            />/
            <VbenCountToAnimator
              :decimals="0"
              :duration="1000"
              :end-val="item.sum"
              :start-val="0"
              color="#F56C6C"
              suffix="笔"
            />
          </div>
        </div>
      </template>
      <template #state="{ row }">
        <ElText>
          {{
            transferStatesOptions.find((option) => option.value === row.state)!
              .label
          }}
        </ElText>
      </template>
      <!-- 自定义-操作项列 -->
      <template #actions="{ row }">
        <ElButton
          v-if="['ACCEPTED', 'WAIT_USER_CONFIRM'].includes(row.status)"
          link
          type="danger"
          @click="cancelTransfer(row.order_no)"
        >
          撤销转账
        </ElButton>
        <ElButton
          v-if="row.status === 'FAIL'"
          link
          type="danger"
          @click="reTryTransfer(row.id)"
        >
          撤销转账
        </ElButton>
      </template>
    </CashTransferTable>
    <!-- 更新支付渠道状态二次确认弹窗 -->
    <ConfirmModal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </ConfirmModal>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
