import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};
/**
 * 添加页面
 */
export async function createPageApi(data: any) {
  return requestClient.post<any>('/console/page/createPage', data, config);
}

/**
 * 修改页面
 */

export async function updatePageApi(data: any) {
  return requestClient.post<any>('/console/page/updatePage', data, config);
}

/**
 * 删除页面
 */
export async function deletePageApi(menuId: string) {
  return requestClient.get<any>(`/console/page/deletePage/${menuId}`, config);
}

/**
 * 获取页面
 */
export async function getPageByIdApi(menuId: string) {
  return requestClient.get<any>(`/console/page/getPageById/${menuId}`);
}
export const PageApi = {
  createPageApi,
  updatePageApi,
  deletePageApi,
  getPageByIdApi,
};
