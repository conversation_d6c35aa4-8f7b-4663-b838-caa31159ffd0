import { requestClient } from '#/api/request';

export namespace NSParkFee {
  export interface IPagingInRecordsRow {
    car_photo: string;
    car_photo_url: string;
    car_type: number;
    car_type_desc: string;
    days: number;
    gateway_id: string;
    gateway_name: string;
    id: string;
    in_time: string;
    main_brand: string;
    out_state: number;
    out_state_desc: string;
    park_code: string;
    park_id: string;
    park_name: string;
    park_region_id: string;
    park_region_name: string;
    plate_no: string;
    sub_brand: string;
  }

  export interface IPagingDeleteRecordsRow {
    car_type: number;
    car_type_desc: string;
    days: number;
    delete_reason: string;
    delete_time: string;
    id: string;
    in_time: string;
    park_id: string;
    park_name: number;
    park_region_id: string;
    park_region_name: string;
    plate_no: string;
    updator_name: string;
  }

  export interface IPagingOutRecordsRow {
    car_type: number;
    car_type_desc: string;
    duration: number;
    gateway_id: string;
    gateway_name: string;
    id: string;
    in_car_photo: string;
    in_car_photo_url: string;
    in_time: string;
    main_brand: string;
    out_car_photo: string;
    out_car_photo_url: string;
    out_reason: number;
    out_reason_desc: string;
    out_time: string;
    park_code: string;
    park_id: string;
    park_name: string;
    park_region_id: string;
    park_region_name: string;
    plate_no: string;
    sub_brand: string;
  }

  export interface IPagingInRecordsParams {
    page: number;
    limit: number;
    car_types?: number[];
    days?: string;
    start_time: string;
    end_time: string;
    park_id: string;
    park_name: string;
    plate_no?: string;
    out_park_states?: number[];
  }

  export interface IPagingInRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingInRecordsRow[];
    total: number;
  }

  export interface IDelPagingInRecordsParams {
    id: string[];
    delete_reason: string;
  }

  export interface IPagingDeleteRecordsParams {
    page: number;
    limit: number;
    car_types?: number[];
    start_time: string;
    end_time: string;
    park_id: string;
    park_name: string;
    plate_no?: string;
    updator_name?: string;
  }

  export interface IPagingDeleteRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingDeleteRecordsRow[];
    total: number;
  }

  export interface IPagingOutRecordsParams {
    page: number;
    limit: number;
    car_types?: number[];
    in_start_time: string;
    in_end_time: string;
    out_start_time: string;
    out_end_time: string;
    park_id: string;
    park_name: string;
    plate_no?: string;
  }

  export interface IPagingOutRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingOutRecordsRow[];
    total: number;
  }

  export interface IPagingParkPayRecordsRow {
    car_type: number;
    car_type_desc: string;
    charge_name: string;
    debate_money: number;
    id: string;
    in_car_photo: string;
    in_car_photo_url: string;
    in_gateway_id: string;
    in_gateway_name: string;
    in_time: string;
    invoice_state: number;
    invoice_state_desc: string;
    order_money: number;
    order_no: string;
    order_state: number;
    order_state_desc: string;
    out_car_photo: string;
    out_car_photo_url: string;
    out_gateway_id: string;
    out_gateway_name: string;
    park_id: string;
    park_name: string;
    park_region_id: string;
    park_region_name: string;
    pay_channel: number;
    pay_channel_desc: string;
    pay_method: number;
    pay_method_desc: string;
    pay_time?: string;
    plate_no: string;
    refund_state: number;
    refund_state_desc: string;
    should_pay_money: number;
    stop_car_type: number;
    stop_car_type_desc: string;
    time: string;
    to_time: string;
  }

  export interface ICountParkPayRecordResult {
    count_debate_money: number;
    count_order_money: number;
    count_should_pay_money: number;
  }

  export interface ICountPagingParkPayRecordsParams {
    page: number;
    limit: number;
    car_types?: number[];
    in_start_time?: string;
    in_end_time?: string;
    order_states?: number[];
    out_start_time?: string;
    out_end_time?: string;
    park_id: string;
    park_name: string;
    park_region_id: string;
    pay_channels?: number[];
    pay_methods?: number[];
    pay_type?: number[];
    refund_states?: number[];
  }

  export interface IPagingParkPayRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingParkPayRecordsRow[];
    total: number;
  }

  export interface IParkPayRecordsRefundApplyParams {
    mobile?: string;
    refund_money?: number;
    refund_user?: string;
    refund_channel?: number;
    refund_account?: string;
    refund_reason?: string;
    id: string;
    should_pay_money: number;
    order_no: string;
    park_id: string;
    plate_no: string;
  }

  export interface IPagingRentSpaceRecordsParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    pay_end_time: string;
    pay_start_time: string;
    plate_no: string;
    refund_states: number[];
    renew_states: number[];
    rent_states: number[];
  }

  export interface IPagingRentSpaceRecordsRow {
    id: string;
    order_no: string;
    park_id: string;
    park_name: string;
    park_code: string;
    rent_rule_name: string;
    product_type_desc: string;
    payed_money: number;
    payed_time: string;
    prk_rent_rule_type_desc: string;
    valid_start_time: string;
    valid_end_time: string;
    pay_state_desc: string;
    rent_state_desc: string;
    plate_nos: string;
    member_name: string;
    member_mobile: string;
    renew_state_desc: string;
    invoice_state_desc: string;
    refund_state_desc: string;
    pay_state: number;
    refund_state: number;
    rent_state: number;
  }

  export interface IPagingRentSpaceRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingRentSpaceRecordsRow[];
    total: number;
  }

  export interface IRentSpaceRecordsRefundApplyParams {
    id: string;
    park_id: string;
    mobile: string;
    member_name: string;
    time?: string;
    valid_start_time: string;
    valid_end_time: string;
    refund_money?: number;
    refund_channel?: number;
    refund_account?: string;
    refund_reason?: string;
    payed_money: number;
  }

  export interface ISentryBoxOperationRecordsRow {
    id: string;
    park_id: string;
    park_name: string;
    plate_no: string;
    event_content?: string;
    event_context?: string;
    updated_at?: string;
    created_at?: string;
    updator_name?: string;
    operator?: string;
    charge_name?: string;
  }

  export interface ISentryBoxOperationRecordsParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    in_start_time?: string;
    in_end_time?: string;
    plate_no?: string;
    operator_name?: string;
  }

  export interface ISentryBoxOperationRecordsResult {
    current_page: number;
    page_count: number;
    rows: ISentryBoxOperationRecordsRow[];
    total: number;
  }

  export interface IPagingReserveRecordsRow {
    id: string;
    park_code?: string;
    park_name?: string;
    park_region_name?: string;
    plate_no?: string;
    payed_money?: number;
    plan_start_time?: string;
    plan_end_time?: string;
    state_desc?: string;
    mbr_member_name?: string;
    mbr_member_mobile?: string;
    invoice_state_desc?: string;
  }

  export interface IPagingReserveRecordsParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    start_time?: string;
    end_time?: string;
    plate_no?: string;
    states?: number[];
  }
  export interface IPagingReserveRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingReserveRecordsRow[];
    total: number;
  }
}

/**
 * 获取入场记录列表
 */
export const getPagingInRecordsApi = (
  params: NSParkFee.IPagingInRecordsParams,
) => {
  return requestClient.post<NSParkFee.IPagingInRecordsResult>(
    '/console/park/fee/inRecords/pagingInRecords',
    params,
  );
};

/**
 * 删除入场记录
 */
export const delPagingInRecordsApi = (
  params: NSParkFee.IDelPagingInRecordsParams,
) => {
  return requestClient.post(
    '/console/park/fee/inRecords/deleteInRecords',
    params,
  );
};

/**
 * 查询已删除入场记录列表
 */
export const getPagingDeleteRecordsApi = (
  params: NSParkFee.IPagingDeleteRecordsParams,
) => {
  return requestClient.post<NSParkFee.IPagingDeleteRecordsResult>(
    '/console/park/fee/inRecords/pagingDeleteRecords',
    params,
  );
};

/**
 * 获取出场记录列表
 */
export const getPagingOutRecordsApi = (
  params: NSParkFee.IPagingOutRecordsParams,
) => {
  return requestClient.post<NSParkFee.IPagingOutRecordsResult>(
    '/console/park/fee/outRecords/pagingOutRecords',
    params,
  );
};

/**
 *  获取停车缴费金额数目信息
 */
export const getCountParkPayRecordApi = (
  params: NSParkFee.ICountPagingParkPayRecordsParams,
) => {
  return requestClient.post<NSParkFee.ICountParkPayRecordResult>(
    '/console/park/fee/parkPayRecords/countParkPayRecord',
    params,
  );
};

/**
 *  获取停车缴费列表内容
 */
export const getPagingParkPayRecordsApi = (
  params: NSParkFee.ICountPagingParkPayRecordsParams,
) => {
  return requestClient.post<NSParkFee.IPagingParkPayRecordsResult>(
    '/console/park/fee/parkPayRecords/pagingParkPayRecords',
    params,
  );
};

/**
 *  停车缴费申请退款
 */
export const parkPayRecordsRefundApplyApi = (
  params: NSParkFee.IParkPayRecordsRefundApplyParams,
) => {
  return requestClient.post(
    '/console/park/fee/parkPayRecords/refundApply',
    params,
  );
};

/**
 *  获取长租缴费列表内容
 */
export const getPagingRentSpaceRecordsApi = (
  params: NSParkFee.IPagingRentSpaceRecordsParams,
) => {
  return requestClient.post<NSParkFee.IPagingRentSpaceRecordsResult>(
    '/console/park/fee/rentSpaceRecords/pagingRentSpaceRecords',
    params,
  );
};

/**
 *  长租缴费申请退款
 */
export const rentSpaceRecordsRefundApplyApi = (
  params: NSParkFee.IRentSpaceRecordsRefundApplyParams,
) => {
  return requestClient.post(
    '/console/park/fee/rentSpaceRecords/refundApply',
    params,
  );
};

/**
 *  获取特殊放行列表内容
 */
export const getPagingSpecialReleaseRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingSpecialReleaseRecords',
    params,
  );
};

/**
 *  获取取消放行列表内容
 */
export const getPagingCancelReleaseRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingCancelReleaseRecords',
    params,
  );
};

/**
 *  获取车牌号矫正列表内容
 */
export const getPagingCorrectRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingCorrectRecords',
    params,
  );
};

/**
 *  获取入口抬杆列表内容
 */
export const getPagingEntranceRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingEntranceRecords',
    params,
  );
};

/**
 *  获取出口抬杆列表内容
 */
export const getPagingExitRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingExitRecords',
    params,
  );
};

/**
 *  获取手动匹配出场列表内容
 */
export const getPagingManualRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingManualRecords',
    params,
  );
};

/**
 *  获取重复入场列表内容
 */
export const getPagingRepeatRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingRepeatRecords',
    params,
  );
};

/**
 *  获取被冲车辆列表内容
 */
export const getPagingLossRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingLossRecords',
    params,
  );
};

/**
 *  获取切换费率列表内容
 */
export const getPagingRateRecordsApi = (
  params: NSParkFee.ISentryBoxOperationRecordsParams,
) => {
  return requestClient.post<NSParkFee.ISentryBoxOperationRecordsResult>(
    '/console/park/fee/sentryBoxRecords/pagingRateRecords',
    params,
  );
};

/**
 *  获取入场图片
 */
export const getByCarInRecordIdApi = (params: string) => {
  return requestClient.get(
    `/console/park/fee/inRecords/getByCarInRecordId/${params}`,
  );
};

/**
 *  获取出场图片
 */
export const getByCarOutRecordIdApi = (params: string) => {
  return requestClient.get(
    `/console/park/fee/outRecords/getByCarOutRecordId/${params}`,
  );
};

/**
 *  获取预约记录列表内容
 */
export const getPagingReserveRecordsApi = (
  params: NSParkFee.IPagingReserveRecordsParams,
) => {
  return requestClient.post<NSParkFee.IPagingReserveRecordsResult>(
    'console/park/fee/reserveRecord/pagingReserveRecords',
    params,
  );
};
