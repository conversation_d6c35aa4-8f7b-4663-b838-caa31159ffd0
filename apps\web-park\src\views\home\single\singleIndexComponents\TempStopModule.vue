<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { VbenCountToAnimator } from '@vben/common-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { HomepageApi } from '#/api';

const chartRef = ref<any>();
const chartRef2 = ref<any>();

const { renderEcharts, getChartInstance } = useEcharts(chartRef);
const { renderEcharts: renderEcharts2, getChartInstance: getChartInstance2 } =
  useEcharts(chartRef2);

const tempStopData = ref<any>([
  { name: '现金', value: '0' },
  { name: '电子', value: '0' },
  { name: 'ETC', value: '0' },
  { name: '第三方', value: '0' },
]);

const tempStopData2 = ref<any>([
  { name: '现金', value: '0' },
  { name: '电子', value: '0' },
  { name: 'ETC', value: '0' },
  { name: '第三方', value: '0' },
]);

onMounted(() => {
  renderEcharts({
    grid: {
      top: '0%',
      bottom: '0%',
      left: '0%',
      right: '0%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        animationDelay() {
          return Math.random() * 100;
        },
        animationEasing: 'exponentialInOut',
        animationType: 'scale',
        avoidLabelOverlap: false,
        color: ['#5570F1', '#C2A5F9', '#FFCC91', '#3BDBCF'],
        data: tempStopData.value,
        emphasis: {
          label: {
            fontSize: '10',
            fontWeight: 'bold',
            show: true,
            formatter: '{c|{c}元}\n{b|{b}}',
            rich: {
              b: {
                color: '#666666',
                fontSize: 12,
                lineHeight: 20,
              },
              c: {
                color: '#333333',
                fontSize: 14,
              },
            },
          },
        },
        itemStyle: {
          borderRadius: 0,
          borderWidth: 2,
        },
        label: {
          position: 'center',
          show: false,
        },
        labelLine: {
          show: false,
        },
        name: '临停交易',
        radius: ['45%', '70%'],
        center: ['50%', '50%'],
        type: 'pie',
      },
    ],
  });

  renderEcharts2({
    xAxis: {
      max: 'dataMax',
    },
    grid: {
      top: '0%',
      bottom: '0%',
      left: '0%',
      right: '15%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: ['现金', '电子', 'ETC', '第三方'],
      inverse: true,
      animationDuration: 300,
      animationDurationUpdate: 300,
      max: 4,
    },
    series: [
      {
        realtimeSort: true,
        name: 'X',
        type: 'bar',
        data: tempStopData2.value,
        itemStyle: {
          color: (params: any): string => {
            const colorList = ['#5570F1', '#C2A5F9', '#FFCC91', '#3BDBCF'];
            return colorList[params.dataIndex] || colorList[0] || '';
          },
        },
        label: {
          show: true,
          position: 'right',
          valueAnimation: true,
        },
      },
    ],
    animationDuration: 0,
    animationDurationUpdate: 3000,
    animationEasing: 'linear',
    animationEasingUpdate: 'linear',
  });
});

const fetchData = async (params: any) => {
  HomepageApi.statParkPaymentsApi(params).then((res) => {
    tempStopData.value[0].value = res.cash_payed_money || 0;
    tempStopData.value[1].value = res.electronic_payed_money || 0;
    tempStopData.value[2].value = res.etc_payed_money || 0;
    tempStopData.value[3].value = res.mall_coo_payed_money || 0;

    tempStopData2.value[0].value = res.cash_payed_num || 0;
    tempStopData2.value[1].value = res.electronic_payed_num || 0;
    tempStopData2.value[2].value = res.etc_payed_num || 0;
    tempStopData2.value[3].value = res.mall_coo_payed_num || 0;
  });

  setTimeout(() => {
    const chartInstance = getChartInstance();
    chartInstance?.setOption({
      series: [
        {
          data: tempStopData.value,
        },
      ],
    });

    const chartInstance2 = getChartInstance2();
    chartInstance2?.setOption({
      series: [
        {
          data: tempStopData2.value,
        },
      ],
    });
  }, 1000);
};

defineExpose({
  fetchData,
});
</script>

<template>
  <div class="flex h-[90%] items-center">
    <EchartsUI ref="chartRef" height="100%" width="20%" />
    <div class="flex h-full w-[30%] flex-wrap gap-2 px-4">
      <div
        v-for="(item, i) in tempStopData"
        :key="i"
        class="flex w-[calc(50%-1rem)] flex-col justify-center rounded-lg bg-[#1570FF]/[0.08] text-center"
      >
        <div class="text-sm">{{ item.name }}支付(元)</div>
        <div class="text-base font-bold text-[#5570F1]">
          <VbenCountToAnimator
            :duration="2000"
            :end-val="Number(item.value)"
            :start-val="1"
            :decimals="2"
          />
        </div>
      </div>
    </div>
    <EchartsUI ref="chartRef2" height="100%" width="25%" />
    <div class="flex h-full w-[25%] flex-wrap gap-1">
      <div
        v-for="(item, i) in tempStopData2"
        :key="i"
        class="flex h-[calc(50%-0.25rem)] w-[calc(50%-0.25rem)] flex-col justify-center rounded-lg bg-[#1570FF]/[0.08] text-center"
      >
        <div class="text-sm">{{ item.name }}支付(笔)</div>
        <div class="text-base font-bold text-[#5570F1]">
          <VbenCountToAnimator
            :duration="2000"
            :end-val="Number(item.value)"
            :start-val="1"
          />
        </div>
      </div>
    </div>
  </div>
</template>
