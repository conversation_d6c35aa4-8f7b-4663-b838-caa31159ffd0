<script name="DictTable" lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElText,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { DictApi } from '#/api';

const addFormRef = ref();
const editFormRef = ref();
const dictIdsList = ref<any>([]);
const route = useRoute();
const rules = {
  name: [
    {
      required: true,
      message: '请输入标签',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入编码',
      trigger: 'blur',
    },
  ],
  value: [
    {
      required: true,
      message: '请输入值',
      trigger: 'blur',
    },
  ],
};
const data = reactive<any>({
  params: route.query,
  queryParams: {
    id: '',
  },
  form: {
    name: '',
    code: '',
    value: '',
    weight: '',
    dict_type_id: '',
  },
  updateForm: {},
});
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '填写字典名称',
      },
      defaultValue: '',
      fieldName: 'name',
      label: '字典名称',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  // submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
interface RowType {
  category: string;
  color: string;
  id: string;
  price: string;
  productName: string;
  releaseDate: string;
}

const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: '',
  },
  columns: [
    { align: 'left', title: '', type: 'checkbox', width: 40 },
    { field: 'id', title: 'ID', width: 60 },
    { field: 'name', title: '字典名称' },
    { field: 'code', title: '字典编码', sortable: true },
    { field: 'value', title: '字典值', sortable: true },
    { field: 'weight', title: '排序', sortable: true },
    { field: 'updated_at', title: '更新时间' },
    {
      slots: { default: 'action' },
      title: '操作',
      field: 'action',
      fixed: 'right',
    },
  ],
  height: 'auto',
  keepSource: true,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } = await DictApi.pagingDictApi({
          page: page.currentPage,
          limit: page.pageSize,
          id: route.query.dictTypeId,
          ...formValues,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
onMounted(() => {
  if (data.params.dictTypeId !== null && data.params.dictTypeId !== undefined) {
    data.queryParams.id = data.params.dictTypeId;
    data.form.dict_type_id = data.params.dictTypeId;
  }
});

/**
 * 新增字典
 */
const [ModalDict, modalDictApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    addFormRef.value.validate().then(() => {
      DictApi.createDictApi(data.form)
        .then(() => {
          addFormRef.value.resetFields();
          gridApi.query();
          modalDictApi.close();
        })
        .catch(() => {
          gridApi.query();
        });
    });
  },
  onCancel: () => {
    addFormRef.value.resetFields();
    modalDictApi.close();
  },
});
// 新建字典
const addHandleCreate = () => {
  data.form = {
    name: undefined,
    code: undefined,
    value: undefined,
    weight: undefined,
    dict_type_id: data.params.dictTypeId,
  };
  // dictCreateDialogVisible.value = true;
  modalDictApi.open();
};

const batchDelete = (type: string) => {
  if (type === '批量') {
    dictIdsList.value = gridApi.grid.getCheckboxRecords();
  }
  if (dictIdsList.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的字典',
      type: 'warning',
    });
  } else {
    showConfirmModal({
      title: '字典删除',
      description: '确定要删除吗？',
      onConfirm: async () => {
        const pushIds = [];
        for (let i = 0; i < dictIdsList.value.length; i++) {
          pushIds.push(Number.parseInt(dictIdsList.value[i].id));
        }
        data.form.id = pushIds;
        DictApi.deleteDictApi(data.form)
          .then(() => {
            gridApi.query();
          })
          .catch(() => {
            gridApi.query();
          });
      },
    });
  }
};
// 删除字典
const tableDelete = (val: any) => {
  dictIdsList.value[0] = val;
  batchDelete('');
};

/**
 * 新增字典
 */
const [ModalDictUpdate, modalDictUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    editFormRef.value.validate().then(() => {
      DictApi.updateDictApi(data.updateForm)
        .then(() => {
          gridApi.query();
          editFormRef.value.resetFields();
          modalDictUpdateApi.close();
        })
        .catch(() => {
          gridApi.query();
        });
    });
  },
  onCancel: () => {
    editFormRef.value.resetFields();
    modalDictUpdateApi.close();
  },
});
// 修改字典
const tableEdit = (row: {
  code: any;
  id: any;
  name: any;
  value: any;
  weight: any;
}) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    value: row.value,
    weight: row.weight,
    dict_type_id: data.params.dictTypeId,
  };
  modalDictUpdateApi.open();
};
</script>

<template>
  <Page auto-content-height>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <Grid>
      <template #toolbar-actions>
        <ElButton type="primary" @click="addHandleCreate()">新 增</ElButton>
        <ElButton type="danger" @click="batchDelete('批量')">批量删除</ElButton>
      </template>
      <template #action="{ row }">
        <ElButton link type="primary" @click="tableEdit(row)"> 修改 </ElButton>
        <ElButton link type="danger" @click="tableDelete(row)"> 删除 </ElButton>
      </template>
    </Grid>
    <div>
      <ModalDict :fullscreen-button="false" class="w-[650px]" title="新增字典">
        <ElForm
          ref="addFormRef"
          :model="data.form"
          :rules="rules"
          label-width="50px"
        >
          <ElFormItem label="标签" prop="name">
            <ElInput v-model="data.form.name" />
          </ElFormItem>
          <ElFormItem label="编码" prop="code">
            <ElInput v-model="data.form.code" />
          </ElFormItem>
          <ElFormItem label="值" prop="value">
            <ElInput v-model="data.form.value" type="number" />
          </ElFormItem>
          <ElFormItem label="排序" prop="weight">
            <ElInput v-model="data.form.weight" />
          </ElFormItem>
        </ElForm>
      </ModalDict>
      <ModalDictUpdate
        :fullscreen-button="false"
        class="w-[650px]"
        title="修改字典"
      >
        <ElForm
          ref="editFormRef"
          :model="data.updateForm"
          :rules="rules"
          label-width="100px"
        >
          <ElFormItem label="标签" prop="name">
            <ElInput v-model="data.updateForm.name" />
          </ElFormItem>
          <ElFormItem label="编码" prop="code">
            <ElInput v-model="data.updateForm.code" />
          </ElFormItem>
          <ElFormItem label="值" prop="value">
            <ElInput v-model="data.updateForm.value" />
          </ElFormItem>
          <ElFormItem label="排序" prop="weight">
            <ElInput v-model="data.updateForm.weight" />
          </ElFormItem>
        </ElForm>
      </ModalDictUpdate>
    </div>
  </Page>
</template>
<style lang="scss" scoped></style>
