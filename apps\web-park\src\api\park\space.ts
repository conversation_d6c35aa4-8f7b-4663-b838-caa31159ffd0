import { requestClient } from '#/api/request';

export namespace NSParkSpace {
  export interface IParkSpaceListParams {
    page: number;
    limit: number;
    // 车场名称
    park_name?: string;
    // 字场名称
    park_region_name?: string;
    // 车位编码
    code?: string;
    // 车位类型
    types?: number[];
    // 车位属性
    properties?: number[];
  }

  export interface IParkSpaceListResult {
    current_page: number;
    page_count: number;
    rows: any[];
    total: number;
  }
}

/**
 * 获取停车场车位列表
 * @description 车场管理 - 车位管理列表
 * @param {NSParkSpace.IParkSpaceListParams} params 查询参数
 */
export async function getParkSpaceListApi(
  params: NSParkSpace.IParkSpaceListParams,
) {
  return requestClient.post<NSParkSpace.IParkSpaceListResult>(
    'console/park/space/pagingParkSpace',
    params,
  );
}
