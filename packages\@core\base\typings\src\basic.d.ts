interface BasicOption {
  label: string;
  value: string;
}

interface IBasicDict {
  id: string;
  name: string;
}

type BasicDict = IBasicDict;

type SelectOption = BasicOption;

type TabOption = BasicOption;

interface BasicUserInfo {
  /**
   * 头像
   */
  avatar: string;
  /**
   * 用户昵称
   */
  realName: string;
  /**
   * 用户角色
   */
  roles?: string[];
  /**
   * 用户id
   */
  userId: string;
  /**
   * 用户名
   */
  username: string;
}

type ClassType = Array<object | string> | object | string;

export type {
  BasicDict,
  BasicOption,
  BasicUserInfo,
  ClassType,
  SelectOption,
  TabOption,
};
