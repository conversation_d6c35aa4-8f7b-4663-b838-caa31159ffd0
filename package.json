{"name": "hd-park-admin-monorepo", "version": "5.5.3", "private": true, "keywords": ["monorepo", "turbo", "vue", "vue3"], "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 turbo build", "build:analyze": "turbo build:analyze", "build:docker": "./scripts/deploy/build-local-docker-image.sh", "build:web-park": "pnpm run build --filter=@vben/web-park", "build:web-business": "cross-env NODE_ENV=pre pnpm run build --filter=@vben/web-business", "build:play": "pnpm run build --filter=@vben/playground", "changeset": "pnpm exec changeset", "check": "pnpm run check:circular && pnpm run check:dep && pnpm run check:type && pnpm check:cspell", "check:circular": "vsh check-circular", "check:cspell": "cspell lint **/*.ts **/README.md .changeset/*.md --no-progress", "check:dep": "vsh check-dep", "check:type": "turbo run typecheck", "clean": "node ./scripts/clean.mjs", "commit": "czg", "dev": "turbo-run dev", "dev:web-park": "pnpm -F @vben/web-park run dev", "dev:web-business": "pnpm -F @vben/web-business run dev", "dev:play": "pnpm -F @vben/playground run dev", "format": "vsh lint --format", "lint": "vsh lint", "postinstall": "pnpm -r run stub --if-present", "preinstall": "npx only-allow pnpm", "prepare": "is-ci || husky", "preview": "turbo-run preview", "publint": "vsh publint", "reinstall": "pnpm clean --del-lock && pnpm install", "test:unit": "vitest run --dom", "test:e2e": "turbo run test:e2e", "update:deps": "npx taze -r -w", "version": "pnpm exec changeset version && pnpm install --no-frozen-lockfile"}, "devDependencies": {"@changesets/changelog-github": "catalog:", "@changesets/cli": "catalog:", "@playwright/test": "catalog:", "@types/node": "catalog:", "@vben/commitlint-config": "workspace:*", "@vben/eslint-config": "workspace:*", "@vben/prettier-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/tailwind-config": "workspace:*", "@vben/tsconfig": "workspace:*", "@vben/turbo-run": "workspace:*", "@vben/vite-config": "workspace:*", "@vben/vsh": "workspace:*", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "@vue/test-utils": "catalog:", "autoprefixer": "catalog:", "cross-env": "catalog:", "cspell": "catalog:", "happy-dom": "catalog:", "husky": "catalog:", "if-env": "^1.0.4", "is-ci": "catalog:", "lint-staged": "catalog:", "playwright": "catalog:", "rimraf": "catalog:", "tailwindcss": "catalog:", "turbo": "catalog:", "typescript": "catalog:", "unbuild": "catalog:", "vite": "catalog:", "vitest": "catalog:", "vue": "catalog:", "vue-tsc": "catalog:"}, "engines": {"node": ">=20.10.0", "pnpm": ">=9.12.0"}, "packageManager": "pnpm@9.15.3", "pnpm": {"peerDependencyRules": {"allowedVersions": {"eslint": "*"}}, "overrides": {"@ast-grep/napi": "catalog:", "@ctrl/tinycolor": "catalog:", "clsx": "catalog:", "esbuild": "0.24.0", "pinia": "catalog:", "vue": "catalog:"}, "neverBuiltDependencies": ["canvas", "node-gyp"]}}