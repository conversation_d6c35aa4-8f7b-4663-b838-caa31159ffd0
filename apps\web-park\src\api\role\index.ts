import type { BasicDict } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace NSRole {
  export interface IRoleListResult {
    code: string;
    created_at: null | string;
    enabled: 0 | 1;
    id: string;
    name: string;
    type: string;
    type_display: string;
  }
}
const config = {
  headers: {
    showMessage: true,
  },
};
/**
 * 获取用户角色列表字典
 */
async function getRoleListByUserIdApi() {
  return requestClient.get<BasicDict[]>('/console/role/getRoleListByUserId');
}

/**
 * 用户主动切换角色
 * @param targetRoleId 目标角色ID
 */
async function switchRoleApi(targetRoleId: number) {
  return requestClient.post(`/console/role/switchRole/${targetRoleId}`);
}

/**
 * 获取角色列表(全部)
 */
async function getRoleListApi() {
  return requestClient.get<NSRole.IRoleListResult[]>('/console/role/rolesList');
}

/**
 * 用户主动切换角色
 */
async function pagingRolesApi(data: any) {
  return requestClient.post(`/console/role/pagingRoles`, data);
}

/**
 * 添加角色
 */
async function createRoleApi(data: any) {
  return requestClient.post(`/console/role/createRole`, data, config);
}

/**
 * 修改角色
 */
async function updateRoleApi(data: any) {
  return requestClient.post(`/console/role/updateRole`, data, config);
}

/**
 * 删除角色
 */
async function deleteRolesApi(data: any) {
  return requestClient.post(`/console/role/deleteRoles`, data, config);
}

/**
 * 查询单条角色信息
 */
async function getRoleByIdApi(id: any) {
  return requestClient.post(`/console/role/getRoleById/${id}`, {});
}

/**
 * 启用角色
 */
async function enableRoleApi(roleId: any) {
  return requestClient.post(`/console/role/enableRole/${roleId}`, {}, config);
}

/**
 * 停用角色
 */
async function disableRoleApi(roleId: any) {
  return requestClient.post(`/console/role/disableRole/${roleId}`, {}, config);
}

/**
 * 查询角色
 */
async function findRolesApi() {
  return requestClient.get('/console/role/findRoles');
}

/**
 * 获得菜单树
 */
async function findMenuTreeApi(data: any) {
  return requestClient.post(`/console/role/findMenuTree`, data);
}

/**
 * 保存配置权限
 */
async function configPermissionsApi(data: any) {
  return requestClient.post(`/console/role/configPermissions`, data, config);
}

/**
 * 配置Api权限
 */
async function configApiPermissionsApi(data: any) {
  return requestClient.post(`/console/role/configApiPermissions`, data, config);
}

export const RoleApi = {
  getRoleListApi,
  switchRoleApi,
  getRoleListByUserIdApi,
  pagingRolesApi,
  createRoleApi,
  updateRoleApi,
  deleteRolesApi,
  getRoleByIdApi,
  enableRoleApi,
  disableRoleApi,
  findRolesApi,
  findMenuTreeApi,
  configPermissionsApi,
  configApiPermissionsApi,
};
