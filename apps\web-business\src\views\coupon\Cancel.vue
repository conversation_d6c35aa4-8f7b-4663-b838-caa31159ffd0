<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PlatformApi } from '#/api';
import { CommonApi, CommonModule } from '#/api/common';
// import { ElMessage } from 'element-plus';
// 查询参数类型
interface IFormValues {
  coupon_meta_name: string;
  member_name: string;
  mobile: string;
  plate_no: string;
  types: number[];
  use_states: number[];
  draw_types: number[];
  get_timer: string[];
  user_timer: string[];
}

// 优免卷列表行数据类型
interface IRowType {
  id: string;
  member_name: string;
  mobile: string;
  plate_no: string;
  coupon_meta_name: string;
  coupon_data: string;
  type_desc: string;
  draw_type_desc: string;
  created_at: string;
  used_time: string;
  use_state_desc: string;
  face_value: string;
}

// 优免卷类型 Options
const searchTypesOptions = ref<{ label: string; value: number }[]>([]);
// 优免卷使用状态状态 Options
const searchStatesOptions = ref<{ label: string; value: number }[]>([]);
// 优免卷获取方式 Options
const searchDrawTypesOptions = ref<{ label: string; value: number }[]>([]);
// 发送数据的参数
const paramsDate = ref<any>();
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'coupon_meta_name',
      label: '优免卷名称：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'member_name',
      label: '会员昵称：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'mobile',
      label: '手机号：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'types',
      label: '优免卷类型：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchTypesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'use_states',
      label: '使用状态：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchStatesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'draw_types',
      label: '获取方式：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchDrawTypesOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: null,
      fieldName: 'get_timer',
      label: '获取时间：',
      componentProps: {
        clearable: true,
        type: 'daterange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
    {
      component: 'DatePicker',
      defaultValue: null,
      fieldName: 'user_timer',
      label: '核销时间：',
      componentProps: {
        clearable: true,
        type: 'daterange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: false,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    { title: '序号', type: 'seq', minWidth: 60, width: 60 },
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'member_name',
      title: '会员昵称',
      minWidth: 100,
    },
    {
      field: 'mobile',
      title: '手机号',
      minWidth: 150,
    },
    {
      field: 'plate_no',
      title: '车牌号',
      minWidth: 100,
    },
    {
      field: 'coupon_meta_name',
      title: '优免卷名称',
      minWidth: 100,
    },
    {
      field: 'coupon_data',
      title: '优惠内容',
      slots: { default: 'coupon_data' },
      minWidth: 100,
    },
    {
      field: 'type_desc',
      title: '优免卷类型',
      minWidth: 100,
    },
    {
      field: 'draw_type_desc',
      title: '获取方式',
      minWidth: 100,
    },
    {
      field: 'created_at',
      title: '获取时间',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'used_time',
      title: '核销时间',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'use_state_desc',
      title: '使用状态',
      minWidth: 100,
    },
    {
      field: 'face_value',
      title: '抵扣金额',
      minWidth: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: getCouponList,
    },
  },
};

// 显示优免券内容
const showCouponData = (row: any) => {
  if (row.coupon_meta_param.coupon_type === 1) {
    return `${row.coupon_meta_param.derate_hour}小时`;
  }
  if (row.coupon_meta_param.coupon_type === 2) {
    return `${row.coupon_meta_param.derate_money}元`;
  }
  if (row.coupon_meta_param.coupon_type === 3) {
    return `${row.coupon_meta_param.discount_ratio}折`;
  }
  if (row.coupon_meta_param.coupon_type === 4) {
    return `全免券`;
  }
  if (row.coupon_meta_param.coupon_type === 5) {
    return `${
      row.coupon_meta_param.start_time
    }-${row.coupon_meta_param.end_time}`;
  }
};
/**
 * 初始化搜索条件
 * @description 初始化搜索条件 优免卷类型 优免卷使用状态 优免卷获取方式
 */
const initSearchData = () => {
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.COUPON, [
    { enum_key: 'types', enum_value: 'EnumCouponMetaType' },
  ]).then((res) => {
    searchTypesOptions.value = res.types;
  });

  CommonApi.findEnumsApi(CommonModule.EnumModuleType.PLATFORM, [
    { enum_key: 'use_states', enum_value: 'EnumUseState' },
    { enum_key: 'draw_types', enum_value: 'EnumDrawType' },
  ]).then((res) => {
    searchStatesOptions.value = res.use_states;
    searchDrawTypesOptions.value = res.draw_types;
  });
};

/**
 * 获取优免卷核销列表数据
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 * @returns {Promise<{items: IRowType[], total: number}>} 优免卷核销列表和总数
 */
async function getCouponList({ page }: any, formValues: IFormValues) {
  paramsDate.value = {
    ...formValues,
    limit: page.pageSize,
    page: page.currentPage,
  };
  // 查询参数格式化
  const params = {
    coupon_meta_name: formValues.coupon_meta_name,
    member_name: formValues.member_name,
    mobile: formValues.mobile,
    plate_no: formValues.plate_no,
    types: formValues.types,
    use_states: formValues.use_states,
    draw_types: formValues.draw_types,
    start_time: formValues.get_timer?.[0]
      ? dayjs(formValues.get_timer?.[0]).format('YYYY-MM-DD')
      : '',
    end_time: formValues.get_timer?.[1]
      ? dayjs(formValues.get_timer?.[1]).format('YYYY-MM-DD')
      : '',
    use_start_time: formValues.user_timer?.[0]
      ? dayjs(formValues.user_timer?.[0]).format('YYYY-MM-DD')
      : '',
    use_end_time: formValues.user_timer?.[1]
      ? dayjs(formValues.user_timer?.[1]).format('YYYY-MM-DD')
      : '',
  };
  try {
    const res = await PlatformApi.getPagingCouponRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 注册优免卷核销列表表格
 * @description 启用表格 配置formOptions与gridOptions
 */
const [CancelTable] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
// excl下载
const saveToFile = (data, name) => {
  const url = window.URL.createObjectURL(
    new Blob([data], { type: 'application/vnd.ms-excel' }),
  );
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', name);
  document.body.append(link);
  link.click();
  link.remove();
};

// 导出
const exportExel = () => {
  PlatformApi.exportCancel(paramsDate.value)
    .then((response) => {
      const fileName = response.headers['content-disposition']
        .split(';')[2]
        .split('filename=')[1]
        .replaceAll('"', '');
      saveToFile(response.data, decodeURIComponent(fileName));
    })
    .catch((error) => {
      console.error(error);
    });
};
onMounted(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <CancelTable>
      <template #toolbar-actions>
        <div class="flex h-full w-full items-center justify-end pr-2">
          <div
            class="cur flex size-[30px] cursor-pointer items-center justify-center rounded-[50%] border border-[#e6e6e6]"
            @click="exportExel"
          >
            <div
              class="icon-[material-symbols--arrow-downward-alt-rounded] text-[20px]"
            ></div>
          </div>
        </div>
      </template>
      <template #coupon_data="{ row }">
        {{ showCouponData(row) }}
      </template>
    </CancelTable>
  </Page>
</template>
