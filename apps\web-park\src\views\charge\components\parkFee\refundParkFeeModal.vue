<script lang="ts" setup>
import type { NSParkFee } from '#/api/park/fee';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElText } from 'element-plus';

import { useVbenForm, z } from '#/adapter/form';
import { CommonModule, ParkFeeApi } from '#/api';

defineOptions({
  name: 'RefundParkFeeModal', // 停车缴费-申请退款弹窗
});

const curRecordRow = ref<NSParkFee.IPagingParkPayRecordsRow>();
const payWayOptions = ref<CommonModule.IEnumItem[]>([]);
const [RefundModalForm, RMFRef] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    colon: true,
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false,
  layout: 'horizontal',
  schema: [
    {
      component: 'Text',
      fieldName: 'plate_no',
      label: '车牌号',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'refund_user',
      label: '姓名',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'mobile',
      label: '手机号',
      rules: z
        .string()
        .refine((value) => value, {
          message: '请输入手机号',
        })
        .refine((value) => /^1[3-9]\d{9}$/.test(value), {
          message: '请输入有效的手机号',
        }),
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'refund_money',
      label: '退款金额',
      rules: z
        .string()
        .refine((value) => value, {
          message: '请输入退款金额',
        })
        .refine((value) => Number(value) <= curRecordRow.value!.order_money, {
          message: '退款金额不能大于实缴金额',
        }),
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: payWayOptions,
      },
      fieldName: 'refund_channel',
      label: '退款渠道',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'refund_account',
      label: '退款账号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
      },
      fieldName: 'refund_reason',
      label: '退款原因',
    },
  ],
  wrapperClass: 'grid-cols-1',
});

/**
 * 弹窗配置
 */
const [RefundModal, refundModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = refundModalApi.getData<Record<string, any>>();
      curRecordRow.value = data.row;
      payWayOptions.value = data.options;
    } else {
      RMFRef.resetForm();
    }
  },
  onConfirm: () => {
    RMFRef.validateAndSubmitForm().then(async (res) => {
      if (!res) return;
      const { id, should_pay_money, order_no, park_id, plate_no } =
        curRecordRow.value!;
      const params = {
        ...res,
        refund_money: Number(res.refund_money),
        id,
        should_pay_money,
        order_no,
        park_id,
        plate_no,
      };
      await ParkFeeApi.parkPayRecordsRefundApplyApi(params);
      ElMessage.success('申请成功');
      const shareData = refundModalApi.getData<Record<string, any>>();
      if (shareData.confirmFn) {
        shareData.confirmFn();
      }
      refundModalApi.close();
    });
  },
});
</script>

<template>
  <!-- 申请退款弹窗 -->
  <RefundModal :close-on-click-modal="false" class="w-[650px]" title="申请退款">
    <RefundModalForm>
      <template #plate_no>
        <ElText class="mx-1">{{ curRecordRow!.plate_no }}</ElText>
      </template>
    </RefundModalForm>
  </RefundModal>
</template>
