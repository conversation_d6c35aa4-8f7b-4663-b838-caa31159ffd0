<script lang="ts" setup>
import { onMounted, ref, toRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useAccessStore } from '@vben/stores';

import {
  type CascaderValue,
  ElCascader,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElSelect,
  type FormInstance,
} from 'element-plus';

import { EmployeeApi, type NSEmployee, type NSRole, RoleApi } from '#/api';

// 是否是万信号员工
const isWandaEmployee = ref<boolean>(false);

// 角色列表
const roleList = ref<NSRole.IRoleListResult[]>([]);

// 万信号列表
const searchWxList = ref<any[]>([]);
const loading = ref(false);

// 部门列表
const departmentList = ref<any[]>([]);

// 表单实例
const formRef = ref<FormInstance>();

const baseDto = {
  name: '',
  wxNumber: '',
  phone: '',
  role: [],
  department: '',
  departmentName: '',
  enabled: '1',
  userId: '',
  id: '',
  type: '',
};

// 表单数据
const formState = ref(baseDto);

// 表单验证规则
const rules = ref({
  name: [{ required: true, message: '请填写姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请填写手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请填写正确的手机号',
      trigger: 'blur',
    },
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'blur' }],
  department: [{ required: true, message: '请选择部门', trigger: 'blur' }],
});

let query: Function | undefined = () => {};

/**
 * 注册弹窗
 */
const [Modal, ModalApi] = useVbenModal({
  draggable: true,
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      formState.value = { ...baseDto };
      const shareData = ModalApi.getData<Record<string, any>>();
      departmentList.value = shareData.departmentList.value;
      isWandaEmployee.value = shareData.userType !== 'non-wanda';
      formState.value.id = shareData.id || '';
      formState.value.type = shareData.type || 'add';
      query = shareData.query;

      if (shareData.type === 'edit') {
        const res = await EmployeeApi.getEmployeeByIdApi(
          Number(formState.value.id),
        );
        formState.value.name = res.name;
        formState.value.wxNumber = res.login_name;
        formState.value.phone = res.mobile;
        formState.value.role = res.role_id;
        formState.value.department = res.department_id;
        formState.value.departmentName = res.department_name;
        formState.value.enabled = res.enabled.toString();
        formState.value.userId = '';
        formState.value.id = res.id;
      }
    }
  },
  onConfirm: () => {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        const params: NSEmployee.ICreateEmployeeParams = {
          department_id: formState.value.department,
          department_name: formState.value.departmentName,
          enabled: formState.value.enabled as '0' | '1',
          id: formState.value.userId || formState.value.id,
          login_name: formState.value.wxNumber,
          mobile: formState.value.phone,
          name: formState.value.name,
          role_id: formState.value.role,
        };
        if (formState.value.type === 'add') {
          delete params.id;
        }
        if (formState.value.type === 'edit' && !isWandaEmployee.value) {
          params.login_name = formState.value.phone;
        }
        await (formState.value.type === 'add'
          ? EmployeeApi.createEmployeeApi(params)
          : EmployeeApi.updateEmployeeApi(params));
        ModalApi.close();
        query?.();
      }
    });
  },
});

const accessStore = useAccessStore();

/**
 * 万信号搜索
 * @param query 搜索关键字
 */
const remoteMethod = async (query: string) => {
  if (query) {
    const params = {
      iamToken: `Bearer ${accessStore.accessIamToken}`,
      userNo: '',
      fullName: '',
    };
    // 判断query是不是包含汉字
    // const isChinese = /[\u4E00-\u9FA5]/.test(query);
    // if (isChinese) {
    //   params.fullName = query;
    // } else {
    //   params.userNo = query;
    // }
    params.fullName = query;
    loading.value = true;
    try {
      const res = await EmployeeApi.getUserInfoApi(
        params.iamToken,
        params.userNo,
        params.fullName,
      );
      searchWxList.value = res.map((item) => ({
        label: `${item.fullName}(${item.userNo})`,
        value: item.userNo,
        mobile: item.mobile,
        name: item.fullName,
        pkId: item.userId,
      }));
    } catch {
      searchWxList.value = [];
    } finally {
      loading.value = false;
    }
  } else {
    searchWxList.value = [];
  }
};

/**
 * 递归查找部门名称
 * @param list 部门列表
 * @param id 部门ID
 * @returns 部门名称
 */
const findDepartment = (list: any[], id: string): any => {
  for (const item of list) {
    if (item.id === id) {
      return item;
    }
    if (item.children?.length) {
      const found = findDepartment(item.children, id);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 部门选择
 * @param value 部门ID
 */
const departmentChangeAction = (value: CascaderValue) => {
  formState.value.department = value.toString();
  const department = findDepartment(
    toRaw(departmentList.value),
    value.toString(),
  );
  formState.value.departmentName = department?.label || '';
};

/**
 * 万信号选择
 * @param value 万信号
 */
const wandaEmployeeChangeAction = (value: string) => {
  const item = searchWxList.value.find((item) => item.value === value);
  formState.value.wxNumber = item?.value;
  formState.value.name = item?.name;
  formState.value.phone = item?.mobile;
  formState.value.userId = item?.pkId;
};

/**
 * 获取角色列表
 */
const getRoleList = async () => {
  const res = await RoleApi.getRoleListApi();
  roleList.value = (res || []).filter((item) => item.enabled === 1);
};

onMounted(() => {
  getRoleList();
});
</script>
<template>
  <Modal
    :close-on-click-modal="false"
    :fullscreen-button="false"
    :title="`${formState.type === 'add' ? '添加' : '编辑'}${
      isWandaEmployee ? '员工' : '非万员工'
    }`"
    class="w-[650px]"
  >
    <ElForm
      ref="formRef"
      :model="formState"
      :rules="rules"
      class="p-4"
      label-position="right"
      label-width="auto"
    >
      <ElFormItem label="姓名：" prop="name">
        <ElInput
          v-if="!isWandaEmployee"
          v-model="formState.name"
          :disabled="formState.type === 'edit'"
          clearable
          placeholder="请填写姓名"
        />
        <ElSelect
          v-else
          v-model="formState.name"
          :disabled="formState.type === 'edit'"
          :loading="loading"
          :remote-method="remoteMethod"
          clearable
          filterable
          placeholder="按姓名或万信号搜索"
          remote
          remote-show-suffix
          reserve-keyword
          @change="wandaEmployeeChangeAction"
        >
          <ElOption
            v-for="item in searchWxList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem v-if="isWandaEmployee" label="万信号：" prop="wxNumber">
        <ElInput
          v-model="formState.wxNumber"
          clearable
          disabled
          placeholder="自动获取"
        />
      </ElFormItem>
      <ElFormItem label="手机号：" prop="phone">
        <ElInput
          v-model="formState.phone"
          clearable
          placeholder="请填写手机号"
        />
      </ElFormItem>
      <ElFormItem label="系统角色：" prop="role">
        <ElSelect
          v-model="formState.role"
          clearable
          filterable
          multiple
          placeholder="请选择角色"
        >
          <ElOption
            v-for="item in roleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="所在部门：" prop="department">
        <ElCascader
          v-model="formState.department"
          :options="departmentList"
          :props="{ checkStrictly: true, value: 'id', emitPath: false }"
          :show-all-levels="false"
          class="w-full"
          clearable
          placeholder="请选择部门"
          @change="departmentChangeAction"
        />
      </ElFormItem>
      <ElFormItem label="启用状态：" prop="enabled">
        <ElRadioGroup v-model="formState.enabled" size="large">
          <ElRadio value="1"> 启用 </ElRadio>
          <ElRadio value="0"> 禁用 </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
  </Modal>
</template>
