<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';

import { reactive, ref } from 'vue';

import { ElDrawer } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PermissionGroupApi } from '#/api';

const detailDialogVisibleModel = ref(false);
interface IdataFromValues {
  group_name: string;
  id?: string;
}
interface IdetailFormValues {
  name: string;
  code: string;
  type_display: string;
  id?: string;
  group_name?: string;
}
interface IdataValues {
  form: IdataFromValues;
  updateForm: IdataFromValues;
  detailForm: IdetailFormValues;
}
const data = reactive<IdataValues>({
  form: {
    group_name: '',
  },
  updateForm: {
    id: '',
    group_name: '',
  },
  detailForm: {
    name: '',
    code: '',
    type_display: '',
  },
});

// 列表详情页
interface RowTypeDetail {
  name: string;
  code: string;
  type_display: string;
}
const gridOptions: VxeGridProps<RowTypeDetail> = {
  checkboxConfig: {
    highlight: false,
    labelField: 'name',
  },
  columns: [
    { field: 'name', title: '权限名称' },
    { field: 'code', title: '权限编码' },
    { field: 'type_display', title: '权限类型' },
  ],
  exportConfig: {},
  height: 'auto', // 如果设置为 auto，则必须确保存在父节点且不允许存在相邻元素，否则会出现高度闪动问题
  keepSource: true,

  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const { rows, total } = await PermissionGroupApi.getPermissionByIdApi({
          limit: page.pageSize,
          page: page.currentPage,
          id: data.detailForm.id,
          group_name: data.detailForm.group_name,
        });
        const items = {
          items: rows,
          total,
        };
        return items;
      },
    },
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
// 分页查询权限组列表

const seeDetail = (params: {
  code: string;
  name: string;
  type_display: string;
}) => {
  data.detailForm = params;
  detailDialogVisibleModel.value = true;
};
defineExpose({
  seeDetail,
  gridApi,
});
</script>

<template>
  <div auto-content-height>
    <ElDrawer
      v-model="detailDialogVisibleModel"
      direction="rtl"
      size="50%"
      title="权限组详情"
    >
      <Grid />
    </ElDrawer>
  </div>
</template>
