<script lang="ts" setup>
import { onBeforeMount, ref, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { ElSegmented } from 'element-plus';

import { CommonApi, CommonModule } from '#/api';

import MonthReportByMoney from './components/monthReport/monthReportByMoney.vue';
import MonthReportByTime from './components/monthReport/monthReportByTime.vue';

defineOptions({
  name: 'MonthReport', // 金额月报表+按次月报表
});

const activeName = ref('');
const selectOptions = [
  { label: '金额', value: 'money' },
  { label: '按次', value: 'time' },
];
const monthReportByMoneyRef = useTemplateRef('monthReportByMoneyRef');
const monthReportByTimeRef = useTemplateRef('monthReportByTimeRef');

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = async () => {
  // 获取车辆属性
  const params = [
    {
      enum_key: 'types',
      enum_value: 'EnumParkType',
    },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    monthReportByMoneyRef.value!.parkTypeOptions = res.types;
    monthReportByTimeRef.value!.parkTypeOptions = res.types;
    activeName.value = 'money';
  } catch (error) {
    console.error(error);
  }
};

onBeforeMount(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <template #title>
      <ElSegmented
        v-model="activeName"
        :options="selectOptions"
        size="default"
      />
    </template>
    <MonthReportByMoney
      v-show="activeName === 'money'"
      ref="monthReportByMoneyRef"
    />
    <MonthReportByTime
      v-show="activeName === 'time'"
      ref="monthReportByTimeRef"
    />
  </Page>
</template>
