import { requestClient } from '#/api/request';

// const config = {
//   headers: {
//     showMessage: true,
//   },
// };
export const HomepageApi = {
  /**
   * @description 获取首页统计信息
   * @param {*} params
   * @returns
   */
  countParkInfoApi(data: any) {
    return requestClient.post<any>('/console/homepage/countParkInfo', data);
  },
  /**
   * @description 获取临停支付统计
   * @param {*} params
   * @returns
   */
  statParkPaymentsApi(data: any) {
    return requestClient.post<any>('/console/homepage/statParkPayments', data);
  },
  /**
   * @description 获取长租支付统计
   * @param {*} params
   * @returns
   */
  rentPayInfoApi(data: any) {
    return requestClient.post<any>('/console/homepage/rentPayInfo', data);
  },
  /**
   * @description 获取待办事项
   * @param {*} params
   * @returns
   */
  rentTodoApi(data: any) {
    return requestClient.post<any>('/console/homepage/rentTodo', data);
  },
  /**
   * @description 获取交接班记录
   * @param {*} params
   * @returns
   */
  shiftHandoverRecordsApi(data: any) {
    return requestClient.post<any>(
      '/console/homepage/shiftHandoverRecords',
      data,
    );
  },
  /**
   * @description 获取周转率
   * @param {*} params
   * @returns
   */
  parkTurnoverAndSpaceUsageApi(data: any) {
    return requestClient.post<any>(
      '/console/homepage/parkTurnoverAndSpaceUsage',
      data,
    );
  },
  /**
   * @description 获取停车时长
   * @param {*} params
   * @returns
   */
  parkingRentDurationApi(data: any) {
    return requestClient.post<any>(
      '/console/homepage/parkingRentDuration',
      data,
    );
  },
  // 新版停车平均时长数据
  statParkTrafficFlowsByIntervalApi(data: any) {
    return requestClient.post<any>(
      '/console/homepage/statParkTrafficFlowsByInterval',
      data,
    );
  },
  /**
   * @description 通行效率
   * @param {*} params
   * @returns
   */
  parkTrafficEfficiencyApi(data: any) {
    return requestClient.post<any>(
      '/console/homepage/parkTrafficEfficiency',
      data,
    );
  },
};
