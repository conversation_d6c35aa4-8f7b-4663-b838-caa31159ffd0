<script setup name="AppUpdate" lang="ts">
import { onActivated, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  ElButton,
  ElCard,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElRow,
  ElSelect,
} from 'element-plus';

import { AppApi, CommonApi, CommonModule } from '#/api';
import { router } from '#/router';

const appType = ref<any>([]);
const addForm = ref();
const route = useRoute();
interface IAppFormItem {
  id: string;
  name: string;
  logoUrl?: string;
  app_key: string;
  features: string;
  memo: string;
  app_type: string;
  app_code: string;
  app_public_key: string;
  app_private_key: string;
  app_sign_key: string;
  app_token_expiry: string;
  plat_public_key: string;
  plat_private_key: string;
  plat_sign_key: string;
  plat_token_expiry: string;
  app_params: string;
}
interface IAppForm {
  appForm: IAppFormItem;
}
const data = reactive<IAppForm>({
  appForm: {
    id: '',
    name: '',
    logoUrl: '',
    app_key: '',
    features: '',
    memo: '',
    app_type: '',
    app_code: '',
    app_public_key: '',
    app_private_key: '',
    app_sign_key: '',
    app_token_expiry: '',
    plat_public_key: '',
    plat_private_key: '',
    plat_sign_key: '',
    plat_token_expiry: '',
    app_params: '',
  },
});
const rules = {
  name: [
    {
      required: true,
      message: '请输入应用名称',
      trigger: 'blur',
    },
  ],
  app_key: [
    {
      required: true,
      message: '请输入App Key',
      trigger: 'blur',
    },
  ],
  features: [
    {
      required: true,
      message: '请输入应用介绍',
      trigger: 'blur',
    },
  ],
};
const initSelects = () => {
  const param = [{ enum_key: 'appType', enum_value: 'EnumAppType' }];
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, param).then(
    (response) => {
      appType.value = response.appType;
    },
  );
};

onMounted(() => {
  // 数据初始化
  initSelects();
});
onActivated(() => {
  if (!route.query) {
    return;
  }
  const param = route.query;
  const queryParams = reactive({
    app_id: param.id,
  });
  // 根据id查询app信息
  AppApi.AppApi.pagingAppApi(queryParams).then((response) => {
    const appDatas = response.rows[0];
    data.appForm = {
      id: appDatas.id,
      name: appDatas.name,
      app_key: appDatas.app_key,
      features: appDatas.features,
      memo: appDatas.memo,
      app_type: appDatas.app_type,
      app_code: appDatas.app_code,
      app_public_key: appDatas.app_public_key,
      app_private_key: appDatas.app_private_key,
      app_sign_key: appDatas.app_sign_key,
      app_token_expiry: appDatas.app_token_expiry,
      plat_public_key: appDatas.plat_public_key,
      plat_private_key: appDatas.plat_private_key,
      plat_sign_key: appDatas.plat_sign_key,
      plat_token_expiry: appDatas.plat_token_expiry,
      app_params: appDatas.app_params,
    };
  });
});

const closeTab = () => {
  addForm.value.resetFields();
  // closeCurrentTab({
  //   path: '/system/appAdmin',
  // });
  router.push({
    path: '/system/appAdmin',
  });
};

const save = (addForm: { resetFields?: () => void; validate?: any }) => {
  addForm.validate().then(() => {
    AppApi.AppApi.updateAppApi(data.appForm).then(() => {
      return closeTab();
    });
  });
};
</script>
<template>
  <Page>
    <ElCard class="card">
      <div class="content">
        <ElForm
          ref="addForm"
          label-width="200px"
          :rules="rules"
          :model="data.appForm"
        >
          <template #header>
            <div style="display: inline-block; line-height: 32px">编辑应用</div>
          </template>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="应用名称" class="required" prop="name">
                <ElInput v-model="data.appForm.name" maxlength="30" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="app_key" class="required" prop="app_key">
                <ElInput v-model="data.appForm.app_key" maxlength="200" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="应用类型" class="required" prop="app_type">
                <ElSelect
                  v-model="data.appForm.app_type"
                  placeholder="应用类型"
                  clearable
                >
                  <ElOption
                    v-for="item in appType"
                    :key="item.value"
                    :label="item.key"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="应用标识" class="required" prop="app_code">
                <ElInput v-model="data.appForm.app_code" maxlength="200" />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="应用公钥" prop="app_public_key">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.app_public_key"
                  maxlength="1000"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="应用私钥" prop="app_private_key">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.app_private_key"
                  maxlength="1000"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="应用签名秘钥" prop="app_sign_key">
                <ElInput v-model="data.appForm.app_sign_key" maxlength="30" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="应用Token有效期(分钟)" prop="app_token_expiry">
                <ElInput
                  v-model="data.appForm.app_token_expiry"
                  maxlength="200"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="平台公钥" prop="plat_public_key">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.plat_public_key"
                  maxlength="1000"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="平台私钥" prop="plat_private_key">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.plat_private_key"
                  maxlength="1000"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="平台签名秘钥" prop="plat_sign_key">
                <ElInput v-model="data.appForm.plat_sign_key" maxlength="30" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem
                label="平台Token有效期(分钟)"
                prop="plat_token_expiry"
              >
                <ElInput
                  v-model="data.appForm.plat_token_expiry"
                  maxlength="200"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="24">
              <ElFormItem label="其他参数（json）" prop="app_params">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.app_params"
                  maxlength="1000"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5">
            <ElCol :span="12">
              <ElFormItem label="应用介绍" class="required" prop="features">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.features"
                  maxlength="500"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="备注" prop="memo">
                <ElInput
                  type="textarea"
                  :rows="4"
                  v-model="data.appForm.memo"
                  maxlength="500"
                  show-word-limit
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="5" class="btn-group">
            <ElCol :span="24">
              <ElFormItem style="text-align: center">
                <ElButton style="margin-top: 38px" @click="closeTab()">
                  取消
                </ElButton>
                <ElButton
                  type="primary"
                  style="margin-top: 38px"
                  @click="save(addForm)"
                >
                  保存
                </ElButton>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </ElCard>
  </Page>
</template>
<style scoped>
.card {
  /* height: 100%; */
  vertical-align: middle;
}

.content {
  /* width: 1000px; */

  /* margin: 50px auto; */
}

.form {
  /* width: 600px;
  margin: 50px auto; */
}

.desc {
  width: 100%;
  padding: 0;
  color: rgb(0 0 0 / 45%);
}

.desc h3 {
  margin: 0 0 12px;
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
  color: rgb(0 0 0 / 45%);
}

.desc h4 {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: rgb(0 0 0 / 45%);
}

.desc p {
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 22px;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  color: red;
  content: '* ';
}

.el-upload-dragger {
  width: 500px;
}

.btn-group :deep(.el-form-item__content) {
  justify-content: center;
  margin-left: 0;
}
</style>
