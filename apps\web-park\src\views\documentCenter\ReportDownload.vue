<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSReport } from '#/api';

import { Page } from '@vben/common-ui';

import { ElButton } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { FileApi, ReportApi } from '#/api';
import { saveToFile } from '#/utils';

defineOptions({
  name: 'ReportDownload', // 报表下载
});

/**
 * 业务变量
 */

/**
 * 表格配置
 * @description预约记录管理列表
 */
const gridOptions: VxeGridProps<NSReport.IPagingReportExportRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    { field: 'name', title: '文件名称', align: 'left', minWidth: 300 },
    { field: 'module_desc', title: '所属模块', minWidth: 150 },
    { field: 'size', title: '大小（字节）', sortable: true, minWidth: 150 },
    { field: 'operator_name', title: '导出人', minWidth: 150 },
    { field: 'expired_time', title: '失效时间', sortable: true, minWidth: 200 },
    { field: 'created_at', title: '创建时间', sortable: true, minWidth: 200 },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPagingReportExportRecords,
    },
  },
};

/**
 * 表格事件
 */
// const gridEvents: VxeGridListeners = {
//   checkboxChange({ row }) {
//     console.error(row);
//   },
// };

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [ReportDownloadTable] = useVbenVxeGrid({
  gridOptions,
  // gridEvents,
});

/**
 * 获取报表列表数据
 */
async function getPagingReportExportRecords({ page }: any) {
  try {
    const res = await ReportApi.getPagingReportExportRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 下载报表
 */
const downloadReportFile = async (
  row: NSReport.IPagingReportExportRecordsRow,
) => {
  row.exportLoading = true;
  try {
    const downloadRes = await FileApi.downloadFileApi(row.path);
    const fileName = downloadRes.disposition
      .split(';')[2]
      .split('filename=')[1]
      .replaceAll('"', '');
    saveToFile(downloadRes.data, decodeURIComponent(fileName));
  } catch (error) {
    console.error(error);
  } finally {
    row.exportLoading = false;
  }
};
</script>
<template>
  <Page auto-content-height>
    <!--报表下载表格 -->
    <ReportDownloadTable table-title="报表下载列表">
      <!-- 自定义-操作项列 -->
      <template #actions="{ row }">
        <ElButton
          link
          type="primary"
          :loading="row.exportLoading"
          @click.stop="downloadReportFile(row)"
        >
          下载
        </ElButton>
      </template>
    </ReportDownloadTable>
  </Page>
</template>
