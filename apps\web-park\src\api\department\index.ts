import { requestClient } from '#/api/request';
/**
 * 显示消息配置
 * @description 配置请求头
 */
const showMessageConfig = {
  headers: {
    showMessage: true,
  },
};
/**
 * 获取部门列表
 */
async function listDepartmentTreeApi() {
  return requestClient.get<any>('/console/department/listDepartmentTree');
}
/**
 * 分页查询部门
 */
async function pagingDepartmentApi(data: any) {
  return requestClient.post<any>('/console/department/pagingDepartment', data);
}
/**
 * 保存部门
 */
async function addDepartmentApi(data: any) {
  return requestClient.post<any>(
    '/console/department/addDepartment',
    data,
    showMessageConfig,
  );
}
/**
 * 修改部门
 */
async function updateDepartmentApi(data: any) {
  return requestClient.post<any>(
    '/console/department/updateDepartment',
    data,
    showMessageConfig,
  );
}
/**
 * 删除部门
 */
async function deleteDepartmentApi(departmentId: any) {
  return requestClient.post<any>(
    `/console/department/deleteDepartment/${departmentId}`,
    {},
    showMessageConfig,
  );
}
// // 员工下拉框
// export const listEmployees = () => {
//   return $({
//     url: '/console/employee/employeeList',
//     method: 'get'
//   });
// };

export const DepartmentApi = {
  listDepartmentTreeApi,
  pagingDepartmentApi,
  addDepartmentApi,
  updateDepartmentApi,
  deleteDepartmentApi,
};
