<script setup lang="ts">
import { ref } from 'vue';

import { ElEmpty } from 'element-plus';
/**
 * @description 全国公告栏
 * @component BulletinBoardModule
 *
 * 功能：
 * 1. 展示全国公告栏
 * 2. 响应式布局适配
 */

// 定义数据
const pageData = ref<any>([
  // {
  //   title:
  //     '惠达停车系统用户使用手册V2惠达停车系统用户使用手册V2惠达停车系统用户使用手册V2惠达停车系统用户使用手册V2.3',
  //   id: '1',
  // },
  // {
  //   title: '惠达停车系统用户使用手册V2.3',
  //   id: '2',
  // },
  // {
  //   title: '惠达停车系统用户使用手册V2.3',
  //   id: '3',
  // },
  // {
  //   title: '惠达停车系统用户使用手册V2.3',
  //   id: '4',
  // },
  // {
  //   title: '惠达停车系统用户使用手册V2.3',
  //   id: '5',
  // },
  // {
  //   title: '惠达停车系统用户使用手册V2.3',
  //   id: '6',
  // },
]);

const color = [
  'bg-[#F93C00]/[0.6]',
  'bg-[#FECE00]/[0.8]',
  'bg-[#1570FF]/[0.6]',
  'bg-[#000000]/[0.2]',
];
</script>

<template>
  <div class="mt-3 space-y-2 overflow-y-auto">
    <ElEmpty
      :image-size="58"
      v-if="pageData.length === 0"
      description="暂无公告"
    />
    <div
      v-for="(item, index) in pageData"
      :key="index"
      class="flex items-center justify-between"
    >
      <div
        :class="color[index < 3 ? index : 3]"
        class="h-[20px] w-[20px] rounded text-center text-white"
      >
        {{ item.id }}
      </div>
      <div class="ml-3 flex-1 cursor-pointer overflow-hidden">
        <div class="truncate text-base font-medium">
          {{ item.title }}
        </div>
        <div class="truncate text-sm text-gray-500">2024-11-28 12:45:00</div>
      </div>
    </div>
  </div>
</template>
