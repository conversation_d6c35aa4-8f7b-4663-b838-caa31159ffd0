import { requestClient } from '#/api/request';

export namespace NSStatisticalReport {
  export interface IListSpaceUseRatiosParams {
    park_id: string;
    park_name: string;
    organization_ids: string;
    department_name: string;
    start_time: string;
    end_time: string;
    time_type: number;
  }

  export interface IListSpaceUseRatiosRow {
    id: string;
    statistics_date: string;
    park_name: string;
    park_id: string;
    region_name: string;
    organizational_structure: string;
    province_name: string;
    city_name: string;
    district_name: string;
    parking_balance: number;
    rent_balance: number;
    receivable_money: number;
    receivable_number: number;
    electronic_payment_money: number;
    electronic_payment_add_coupon_money: number;
    electronic_payment_number: number;
    electronic_payment_add_coupon_number: number;
    electronic_payment_money_proportion: string;
    electronic_payment_add_coupon_money_proportion: string;
    electronic_payment_number_proportion: string;
    electronic_payment_add_coupon_number_proportion: string;
  }
}

/**
 * 获取车位利用率报表列表
 * @param params 获取车位利用率报表列表
 */
async function getListSpaceUseRatiosApi(
  params: NSStatisticalReport.IListSpaceUseRatiosParams,
) {
  return requestClient.post<NSStatisticalReport.IListSpaceUseRatiosRow[]>(
    '/console/statistics/space/use/ratio/listSpaceUseRatios',
    params,
  );
}

export const StatisticalReportApi = {
  getListSpaceUseRatiosApi,
};

export namespace NSElectronicPayPercent {
  export interface IListElectronicPaymentProportionsParams {
    park_id: string | undefined;
    park_name: string | undefined;
    organization_ids: string | undefined;
    department_name: string | undefined;
    start_time: string | undefined;
    end_time: string | undefined;
  }

  export interface IListElectronicPaymentProportionsRow {
    id: string;
    statistics_date: string;
    park_name: string;
    park_id: string;
    region_name: string;
    organizational_structure: string;
    province_name: string;
    city_name: string;
    district_name: string;
    space_number: string;
    parking_total_hours: string;
    average_use_ratio: string;
    use_ratio_plate_no: string;
    use_ratio_no_plate_no: string;
    midday_fastigium_use_ratio: string;
    night_fastigium_use_ratio: string;
    business_hours_use_ratio: string;
    morning_no_business_hours_use_ratio: string;
    night_no_business_hours_use_ratio: string;
  }
}

/**
 * 获取电子支付占比报表列表
 * @param params 获取电子支付占比报表列表
 */
async function getListElectronicPaymentProportionsApi(
  params: NSElectronicPayPercent.IListElectronicPaymentProportionsParams,
) {
  return requestClient.post<
    NSElectronicPayPercent.IListElectronicPaymentProportionsRow[]
  >(
    '/console/statistics/electronic/payment/proportion/listElectronicPaymentProportions',
    params,
  );
}

export const ElectronicPayPercentApi = {
  getListElectronicPaymentProportionsApi,
};

export namespace NSAbnormalRecord {
  export interface IListAbnormalOperateRecordsParams {
    park_id: string | undefined;
    park_name: string | undefined;
    organization_ids: string | undefined;
    department_name: string | undefined;
    start_time: string | undefined;
    end_time: string | undefined;
  }

  export interface IListAbnormalOperateRecordsRow {
    id: string;
    statistics_date: string;
    park_name: string;
    park_id: string;
    region_name: string;
    organizational_structure: string;
    province_name: string;
    city_name: string;
    district_name: string;
    space_number: string;
    parking_total_hours: string;
    average_use_ratio: string;
    use_ratio_plate_no: string;
    use_ratio_no_plate_no: string;
    midday_fastigium_use_ratio: string;
    night_fastigium_use_ratio: string;
    business_hours_use_ratio: string;
    morning_no_business_hours_use_ratio: string;
    night_no_business_hours_use_ratio: string;
  }
}

/**
 * 获取电子支付占比报表列表
 * @param params 获取电子支付占比报表列表
 */
async function getListAbnormalOperateRecordsApi(
  params: NSAbnormalRecord.IListAbnormalOperateRecordsParams,
) {
  return requestClient.post<NSAbnormalRecord.IListAbnormalOperateRecordsRow[]>(
    '/console/statistics/abnormal/operate/record/listAbnormalOperateRecords',
    params,
  );
}

export const AbnormalRecordApi = {
  getListAbnormalOperateRecordsApi,
};

export namespace NSParkCarDailyPayment {
  export interface IListParkingSpaceAverageIncomesParams {
    park_id: string | undefined;
    park_name: string | undefined;
    organization_ids: string | undefined;
    department_name: string | undefined;
    start_time: string | undefined;
    end_time: string | undefined;
  }

  export interface IListParkingSpaceAverageIncomesRow {
    id: string;
    statistics_date: string;
    park_name: string;
    park_id: string;
    region_name: string;
    organizational_structure: string;
    province_name: string;
    city_name: string;
    district_name: string;
    total_spaces: number;
    parking_payed_number: number;
    parking_payed_money: number;
    parking_space_average_income_proportion: number;
    parking_third_party_income: number;
    daily_average_income: number;
    licensed_car_income: number;
    licensed_car_average_income: number;
    unlicensed_car_income: number;
    unlicensed_car_average_income: number;
    assessment_licensed_car_income: number;
  }
}

/**
 * 获取电子支付占比报表列表
 * @param params 获取电子支付占比报表列表
 */
async function getListParkingSpaceAverageIncomesApi(
  params: NSParkCarDailyPayment.IListParkingSpaceAverageIncomesParams,
) {
  return requestClient.post<
    NSParkCarDailyPayment.IListParkingSpaceAverageIncomesRow[]
  >(
    '/console/statistics/park/space/average/income/listParkingSpaceAverageIncomes',
    params,
  );
}

export const ParkCarDailyPaymentApi = {
  getListParkingSpaceAverageIncomesApi,
};

export namespace NSParkCarNotPayRecord {
  export interface IListReceivableUnpaidRecordsParams {
    park_id: string | undefined;
    park_name: string | undefined;
    organization_ids: string | undefined;
    department_name: string | undefined;
    start_time: string | undefined;
    end_time: string | undefined;
  }

  export interface IListReceivableUnpaidRecordsRow {
    id: string;
    statistics_date: string;
    park_name: string;
    park_id: string;
    region_name: string;
    organizational_structure: string;
    province_name: string;
    city_name: string;
    district_name: string;

    cloud_watch_desc: string;
    park_type_desc: string;
    flush_loss_money_cnt: number;
    flush_loss_money: number;
  }
}

/**
 * 获取电子支付占比报表列表
 * @param params 获取电子支付占比报表列表
 */
async function getListReceivableUnpaidRecordsApi(
  params: NSParkCarNotPayRecord.IListReceivableUnpaidRecordsParams,
) {
  return requestClient.post<
    NSParkCarNotPayRecord.IListReceivableUnpaidRecordsRow[]
  >(
    '/console/statistics/receivable/unpaid/record/listReceivableUnpaidRecords',
    params,
  );
}

export const ParkCarNotPayRecordApi = {
  getListReceivableUnpaidRecordsApi,
};

export namespace NSCarInTimesPercent {
  export interface IListCarInNumberProportionParams {
    park_id: string | undefined;
    park_name: string | undefined;
    organization_ids: string | undefined;
    department_name: string | undefined;
    month: string | undefined;
  }

  export interface IListCarInNumberProportionRow {
    id: string;
    statistics_date: string;
    park_name: string;
    park_id: string;
    region_name: string;
    organizational_structure: string;
    province_name: string;
    city_name: string;
    district_name: string;

    total_car_in: number;
    total_car_in_one: number;
    car_in_one_proportion: number;
    total_car_in_two: number;
    car_in_two_proportion: number;
    total_car_in_three: number;
    car_in_three_proportion: number;
  }
}

/**
 * 获取电子支付占比报表列表
 * @param params 获取电子支付占比报表列表
 */
async function getListCarInNumberProportionApi(
  params: NSCarInTimesPercent.IListCarInNumberProportionParams,
) {
  return requestClient.post<
    NSCarInTimesPercent.IListCarInNumberProportionRow[]
  >(
    '/console/statistics/car/in/number/proportion/listCarInNumberProportion',
    params,
  );
}

export const CarInTimesPercentApi = {
  getListCarInNumberProportionApi,
};
