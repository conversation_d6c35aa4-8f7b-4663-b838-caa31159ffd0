import { requestClient } from '#/api/request';

export namespace NSParkRegion {
  export interface IListParkRegionItem {
    id?: string;
    name?: string;
    label?: string;
    value?: string;
  }
}

/**
 * 查询关联车场的子场名称
 */
export const getListParkRegionApi = (parkId: string) => {
  return requestClient
    .get<
      NSParkRegion.IListParkRegionItem[]
    >(`/console/park/region/listParkRegion/${parkId}`)
    .then((res) => {
      return res.map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    });
};
