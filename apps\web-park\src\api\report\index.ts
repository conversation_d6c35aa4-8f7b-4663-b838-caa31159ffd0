import { requestClient } from '#/api/request';

export namespace NSReport {
  export interface IPagingReportExportRecordsRow {
    created_at: string;
    expired_time: string;
    module_desc: string;
    name: string;
    operator_name?: string;
    path: string;
    size: string;
    exportLoading?: boolean;
  }

  export interface IPagingReportExportRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingReportExportRecordsRow[];
    total: number;
  }
}

/**
 * 查询报表列表数据接口
 * @param params 查询报表列表数据接口
 */
async function getPagingReportExportRecordsApi(params: {
  limit: number;
  page: number;
}) {
  return requestClient.post<NSReport.IPagingReportExportRecordsResult>(
    '/console/report/pagingReportExportRecords',
    params,
  );
}

export const ReportApi = {
  getPagingReportExportRecordsApi,
};
