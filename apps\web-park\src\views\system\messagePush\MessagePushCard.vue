<script name="MessagePushCard" setup lang="ts">
import { computed, nextTick, onBeforeMount, reactive, ref, watch } from 'vue';

// import MessagePushService from '@/service/system/MessagePushService';
import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElCheckboxGroup,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElLoading,
  ElMessage,
  ElOption,
  ElSelect,
  ElSwitch,
  ElTag,
} from 'element-plus';

import { PushApi, RoleApi } from '#/api';

const props = defineProps({
  radioType: {
    type: Number,
    default: 1,
  },
});
// import { dayjs, ElMessage } from 'element-plus';
// import parkingCarFlowService from '@/service/statisticalReport/ParkingCarFlowService';
const tamplateMap = ref<any>({
  1: {
    pushContext:
      '【惠达云停车】尊敬的用户，您在【XX停车场】的【长租服规则】即将于【到期日期】结束。为了确保您的使用不受影响，建议您提前完成续费手续。您可以通过“惠达停车”小程序轻松完成续费或选择新的停车位。感谢您的配合与支持，祝您出行顺畅，用车愉快！',
    remindTypeLabel: '长租车到期提醒方式：',
    onOffLabel: '长租车到期提醒：',
    durationLable: '提前提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '天',
  },
  2: {
    pushContext:
      '【惠达云停车】您的【XX停车场】的【车牌号】车辆已临停超过【duration1】天，为避免影响您的正常使用，敬请尽早续费。',
    remindTypeLabel: '车辆长期临停提醒方式：',
    onOffLabel: '车辆长期临停提醒功能：',
    durationLable: '临停天数提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '天',
  },
  3: {
    pushContext:
      '【XX停车场】的【拥堵车道名称】从【开始拥堵时间】至【触发拥堵报警时间】已经拥堵超过【duration1/duration2】分钟，请尽快处理。',
    remindTypeLabel: '车道拥堵提醒方式：',
    onOffLabel: '车道拥堵期提醒功能：',
    durationLable: '持续时间提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '分钟',
  },
  4: {
    pushContext:
      '【XX停车场】位于【所属通道】的【离线设备名称】从【开始离线时间】至【触发离线报警时间】已经离线超过【duration1/duration2】分钟，请尽快处理。',
    remindTypeLabel: '设备离线提醒方式：',
    onOffLabel: '设备离线提醒功能：',
    durationLable: '持续时间提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '分钟',
  },
  5: {
    pushContext:
      '【XX停车场】贵宾车辆入场提醒，车牌号为【车牌号】的要客车辆已由车主【车主姓名】于【具体入场时间】驶入【具体车道名称】。请留意并提供相应服务。',
    remindTypeLabel: '入场功能提醒方式：',
    onOffLabel: '入场功能提醒功能：',
    durationLable: '提醒人员设置：',
    pushContextLabel: '内容模板：',
    unit: '分钟',
  },
  6: {
    pushContext:
      '【XX停车场】访客车入场提醒，车牌号为【车牌号】的访客【访客姓名】于【具体入场时间】驶入【具体车道名称】；访客类型:【访客类型】,访问事由:【来访事由】。',
    remindTypeLabel: '入场功能提醒方式：',
    onOffLabel: '入场功能提醒功能：',
    durationLable: '提醒人员设置：',
    pushContextLabel: '内容模板：',
    unit: '分钟',
  },
});
const cardRef = ref();
const cardRefLoading = ref();
const select1Ref = ref();
const select2Ref = ref();
const select1 = ref('');
const select2 = ref('');
const controller = reactive({
  selectPushUser1Visiable: false,
  selectPushUser2Visiable: false,
  select1Ref,
  select2Ref,
});
const roleList = ref<any>([]);

interface IpushUser2List {
  id: number;
  name: string;
}
interface IformData {
  remindType: any;
  onOff: any;
  duration1: string;
  duration2: string;
  pushUser1: string;
  pushUser1List: IpushUser2List[];
  pushUser2: string;
  pushUser2List: IpushUser2List[];
  pushContext: string;
}
const formData = reactive<IformData>({
  remindType: [],
  onOff: false,
  duration1: '5',
  duration2: '15',
  pushUser1: '',
  pushUser1List: [],
  pushUser2: '',
  pushUser2List: [],
  pushContext: '',
});

const contentText = computed(() => {
  let retTemp = tamplateMap.value[String(props.radioType)].pushContext || '';
  retTemp = retTemp.replace('duration1', formData.duration1);
  retTemp = retTemp.replace('duration2', formData.duration2);
  return retTemp;
});
const radioHide = computed(() => {
  let status = true;
  if (props.radioType === 1 || props.radioType === 2) {
    status = false;
  }
  return status;
});

const getConfig = () => {
  cardRefLoading.value = ElLoading.service({
    target: cardRef.value,
  });
  PushApi.PushApi.pushMessageDetailApi({ id: props.radioType })
    .then((res) => {
      // Object.assign(formData, res.data);
      const obj = res;
      formData.remindType =
        (obj && obj.remind_type && obj.remind_type.split(',')) || [];
      formData.onOff = !!(obj && obj.on_off === '1');
      formData.duration1 = (obj && obj.duration1) || '';
      formData.duration2 = (obj && obj.duration2) || '';
      formData.pushUser1List = [];
      formData.pushUser2List = [];
      if (obj && obj.push_user1) {
        obj.push_user1.split(',').forEach((element: any) => {
          const getV = roleList.value.find((j: any) => j.id === element);
          if (getV) {
            formData.pushUser1List.push(getV);
          }
        });
      }
      if (obj && obj.push_user2) {
        obj.push_user2.split(',').forEach((element: any) => {
          const getV = roleList.value.find((j: any) => j.id === element);
          if (getV) {
            formData.pushUser2List.push(getV);
          }
        });
      }
      // formData.pushUser1List =
      //   obj && obj.push_user1
      //     ? obj.push_user1
      //         .split(',')
      //         .map((i: any) => roleList.value.find((j: any) => j.id === i))
      //     : [];
      // formData.pushUser2List =
      //   obj && obj.push_user2
      //     ? obj.push_user2
      //         .split(',')
      //         .map((i: any) => roleList.value.find((j: any) => j.id === i))
      //     : [];
      // if (!formData.pushUser1List[0]) {
      //   formData.pushUser1List = [];
      // }
      // if (!formData.pushUser2List[0]) {
      //   formData.pushUser2List = [];
      // }
    })
    .finally(() => {
      cardRefLoading.value.close();
    });
};

const getMessagePushRoleList = () => {
  RoleApi.pagingRolesApi({ page: 1, limit: 30 }).then((res) => {
    roleList.value = res.rows.map((i: { id: any; name: any }) => ({
      label: i.name,
      value: i.id,
      id: i.id,
      name: i.name,
    }));
    // console.log('getConfig', res);
  });
};

const onSubmit = () => {
  if (formData.remindType.length === 0) {
    ElMessage({
      message: '至少选中一个提醒方式',
      type: 'warning',
    });
    return;
  }
  const params = {
    pushType: props.radioType,
    remindType: formData.remindType.join(','),
    onOff: formData.onOff ? '1' : '0',
    duration1: String(formData.duration1),
    duration2: String(formData.duration2),
    pushUser1:
      formData.pushUser1List.length > 0
        ? formData.pushUser1List.map((i: any) => i.id).join(',')
        : '',
    pushUserName1:
      formData.pushUser1List.length > 0
        ? formData.pushUser1List.map((i: any) => i.name).join(',')
        : '',
    pushUser2:
      formData.pushUser2List.length > 0
        ? formData.pushUser2List.map((i: any) => i.id).join(',')
        : '',
    pushUserName2:
      formData.pushUser2List.length > 0
        ? formData.pushUser2List.map((i: any) => i.name).join(',')
        : '',
    pushContext: contentText.value,
  };
  PushApi.PushApi.insertApi(params).then(() => {
    // if (res.success) {
    //   ElMessage({
    //     message: res.message ?? '推送配置成功',
    //     type: 'success',
    //   });
    //   getConfig();
    // }
    getConfig();
  });
};

const handleClose = (tag: any, listKey: any) => {
  // @ts-ignore
  formData[listKey].splice(formData[listKey].indexOf(tag), 1);
};

const selectRole = (key: string) => {
  // console.log('key', key, arg);
  if (key === '1') {
    controller.selectPushUser1Visiable = false;
    // console.log(select1.value);
    if (roleList.value.some((i: any) => i.id === select1.value))
      formData.pushUser1List.push(
        roleList.value.find((i: any) => i.id === select1.value),
      );
    select1.value = '';
  } else {
    controller.selectPushUser2Visiable = false;
    if (roleList.value.some((i: any) => i.id === select2.value))
      formData.pushUser2List.push(
        roleList.value.find((i: any) => i.id === select2.value),
      );
    select2.value = '';
  }
};

const showSelection = (key: string) => {
  if (key === '1') {
    controller.selectPushUser1Visiable = true;
    nextTick(() => {
      controller.select1Ref.focus();
    });
  } else {
    controller.selectPushUser2Visiable = true;
    nextTick(() => {
      controller.select2Ref.focus();
    });
  }
  // nextTick(() => {
  //   InputRef.value!.input!.focus()
  // })
};
onBeforeMount(() => {
  getMessagePushRoleList();
});
watch(
  () => props.radioType,
  (newValue) => {
    formData.pushContext = tamplateMap.value[props.radioType].pushContext;
    if (Number(newValue) === 1 || Number(newValue) === 2) {
      formData.remindType = ['0'];
    }
    getConfig();
  },
  { immediate: true },
);
</script>
<template>
  <div ref="cardRef" class="mt-[1%] h-full w-full">
    <ElCard shadow="never" class="w-full" style="height: 100%">
      <ElForm :model="formData">
        <ElFormItem :label="tamplateMap[radioType].remindTypeLabel">
          <ElCheckboxGroup v-model="formData.remindType" class="ml-4">
            <ElCheckbox label="短信" value="0" />
            <ElCheckbox label="万信" v-if="radioHide" value="1" />
          </ElCheckboxGroup>
        </ElFormItem>
        <ElFormItem :label="tamplateMap[radioType].onOffLabel">
          <ElSwitch
            v-model="formData.onOff"
            size="large"
            active-text="开启"
            inactive-text="关闭"
          />
        </ElFormItem>
        <ElFormItem :label="tamplateMap[radioType].durationLable">
          <template v-if="radioType < 5">
            <ElCol :span="2">
              <ElInput type="number" v-model="formData.duration1" />
            </ElCol>
            <ElCol :span="2"> {{ tamplateMap[radioType].unit }}</ElCol>
          </template>
          <ElCol :span="5">
            <ElTag type="primary" round v-if="Number(radioType) === 1">
              会员
            </ElTag>
            <ElTag type="primary" round v-else-if="Number(radioType) === 2">
              临停会员
            </ElTag>
            <template v-else>
              <ElTag
                v-for="tag in formData.pushUser1List"
                :key="tag.id"
                closable
                :disable-transitions="false"
                @close="handleClose(tag, 'pushUser1List')"
              >
                {{ tag.name }}
              </ElTag>
              <ElSelect
                v-if="controller.selectPushUser1Visiable"
                ref="select1Ref"
                v-model="select1"
                class="w-20"
                size="small"
                @change="selectRole('1')"
                @blur="selectRole('1')"
              >
                <ElOption
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </ElSelect>
              <!-- @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm" -->
              <ElButton
                v-else
                class="button-new-tag"
                size="small"
                @click="showSelection('1')"
              >
                添加角色
              </ElButton>
            </template>
          </ElCol>
        </ElFormItem>
        <ElFormItem
          class="ml-[110px]"
          v-show="[3, 4].includes(Number(radioType))"
        >
          <ElCol :span="2">
            <ElInput type="number" v-model="formData.duration2" />
          </ElCol>
          <ElCol :span="2"> {{ tamplateMap[radioType].unit }}</ElCol>
          <ElCol :span="5">
            <ElTag
              v-for="tag in formData.pushUser2List"
              :key="tag.id"
              closable
              :disable-transitions="false"
              @close="handleClose(tag, 'pushUser2List')"
            >
              {{ tag.name }}
            </ElTag>

            <ElSelect
              v-if="controller.selectPushUser2Visiable"
              ref="select2Ref"
              v-model="select2"
              class="w-20"
              size="small"
              @change="selectRole('2')"
              @blur="selectRole('2')"
            >
              <ElOption
                v-for="item in roleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </ElSelect>
            <!-- @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm" -->
            <ElButton
              v-else
              class="button-new-tag"
              size="small"
              @click="showSelection('2')"
            >
              添加角色
            </ElButton>
          </ElCol>
        </ElFormItem>
        <ElFormItem :label="tamplateMap[radioType].pushContextLabel">
          <ElCol :span="12">
            <ElInput type="textarea" autosize :value="contentText" disabled />
          </ElCol>
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="onSubmit">保存设置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>
  </div>
  <!-- <el-empty description="description"/> -->
</template>

<style lang="scss" scoped></style>
