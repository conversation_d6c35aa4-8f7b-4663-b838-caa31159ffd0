<script setup name="AppPublish" lang="ts">
import { onActivated, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useAccessStore } from '@vben/stores';

import {
  ElButton,
  ElCard,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElRow,
  ElStep,
  ElSteps,
  ElUpload,
} from 'element-plus';

import { AppApi } from '#/api';
import { router } from '#/router';

const accessStore = useAccessStore();
const uploadExeUrl = ref(
  // ${import.meta.env.VITE_WZT_ORIGIN_URL
  `/console/park/app/uploadApp`,
);
const uploadYmlUrl = ref(`/console/park/app/uploadYml`);
const route = useRoute();
const headers = reactive({
  Authorization: accessStore.accessToken,
});
const addForm = ref();
const loading = ref<boolean>(false);
interface IappForm {
  app_id?: string;
  appId?: string;
  name?: string;
  current_version?: string;
  file_path?: string;
  ymlFilePath?: string;
  features?: string;
  memo?: string;
}

interface Iresult {
  success: boolean;
  message: string;
}
interface Idata {
  active: number;
  appForm: IappForm;
  form: IappForm;
  result: Iresult;
}
const data = reactive<Idata>({
  active: 1,
  appForm: {
    app_id: '',
    appId: '',
    name: '',
    current_version: '',
    file_path: '',
    ymlFilePath: '',
    features: '',
    memo: '',
  },
  form: {
    app_id: '',
    appId: '',
    name: '',
    current_version: '',
    file_path: '',
    ymlFilePath: '',
    features: '',
    memo: '',
  },
  result: {
    success: true,
    message: '',
  },
});
const rules = {
  current_version: [
    {
      required: true,
      message: '版本号不能为空',
      trigger: 'blur',
    },
  ],
  features: [
    {
      required: true,
      message: '版本更新说明不能为空',
      trigger: 'blur',
    },
  ],
};

onActivated(() => {
  if (!route.query) {
    return;
  }
  const param = route.query;
  data.appForm = {
    appId: String(param.id),
  };
  data.active = Number(param.active);
  // 查询单条记录
  AppApi.AppApi.getAppApi(String(param.id)).then((response) => {
    if (response.id) {
      const data = response;
      data.appForm = {
        appId: data.id || '',
        logoUrl: data.logoUrl,
        name: data.name,
      };
    } else {
      ElMessage({
        message: response.message,
        type: 'error',
      });
    }
  });
});

const before = () => {
  if (data.active-- < 2) data.active = 0;
};

const exeNext = () => {
  if (data.appForm.file_path === undefined) {
    ElMessage({
      message: '请先上传EXE文件',
      type: 'warning',
    });
    return false;
  }
  data.active++;
};

const ymlNext = () => {
  if (data.appForm.ymlFilePath === undefined) {
    ElMessage({
      message: '请先上传yml文件',
      type: 'warning',
    });
    return false;
  }
  data.active++;
};

const closeTab = () => {
  router.push({
    path: '/system/appAdmin',
  });
};

const finish = () => {
  // 版本号
  if (
    data.appForm.current_version === undefined ||
    data.appForm.current_version === ''
  ) {
    ElMessage({
      message: '版本号不能为空',
      type: 'warning',
    });
    return false;
  }
  if (data.appForm.features === undefined || data.appForm.features === '') {
    ElMessage({
      message: '版本更新说明不能为空',
      type: 'warning',
    });
    return false;
  }
  data.appForm.app_id = data.appForm.appId;
  AppApi.AppApi.releaseAppApi(data.appForm).then(() => {
    // if (response.success === true) {
    //   ElMessage({
    //     message: response.message,
    //     type: 'success',
    //   });
    //   data.result = {
    //     success: response.success,
    //     message: response.message,
    //   };
    // } else {
    //   ElMessage({
    //     message: response.detail_message,
    //     type: 'warning',
    //   });
    // }
    // data.result = {
    //   success: response.success,
    //   message: response.message,
    // };
  });

  data.active++;
};

const exeBeforeUpload = (file: { size: number }) => {
  data.form = data.appForm;
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage({
      message: '上传文件大小不能超过 100MB!',
      type: 'warning',
    });
  }
};

const exeOnProgressUpload = () => {
  loading.value = true;
};

const exeOnSuccessUpload = (response: {
  data: { file: string | undefined };
  detail_message: any;
  success: boolean;
}) => {
  loading.value = false;
  if (response.success === true) {
    data.appForm.file_path = response.data.file;
  } else {
    ElMessage({
      message: response.detail_message,
      type: 'warning',
    });
  }
};

const exeOnRemoveFile = () => {
  data.appForm.file_path = undefined;
};

const ymlBeforeUpload = (file: { size: number }) => {
  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    ElMessage({
      message: '上传文件大小不能超过 1MB!',
      type: 'warning',
    });
  }
};

const ymlOnProgressUpload = () => {
  loading.value = true;
};

const ymlOnSuccessUpload = (response: {
  data: { file: string | undefined };
  detail_message: any;
  success: boolean;
}) => {
  loading.value = false;
  if (response.success === true) {
    data.appForm.ymlFilePath = response.data.file;
  } else {
    ElMessage({
      message: response.detail_message,
      type: 'warning',
    });
  }
};

const ymlOnRemoveFile = () => {
  data.appForm.ymlFilePath = undefined;
};
</script>
<template>
  <Page>
    <ElCard class="card">
      <div class="content">
        <ElSteps :active="data.active" style="width: 100%" align-center>
          <ElStep title="上传EXE">
            <template #icon>
              <span
                class="icon-[ep--document-add]"
                style="width: 40px; height: 40px"
              ></span>
            </template>
          </ElStep>
          <ElStep title="上传YML">
            <template #icon>
              <span
                class="icon-[ep--document-add]"
                style="width: 40px; height: 40px"
              ></span>
            </template>
          </ElStep>
          <ElStep title="发布APP">
            <template #icon>
              <span
                class="icon-[ep--setting]"
                style="width: 40px; height: 40px"
              ></span>
            </template>
          </ElStep>
          <ElStep title="完成">
            <template #icon>
              <span
                class="icon-[ep--star]"
                style="width: 40px; height: 40px"
              ></span>
            </template>
          </ElStep>
        </ElSteps>
        <div style="margin-top: 60px; margin-bottom: 100px; text-align: center">
          <div v-if="Number(data.active) === 1" v-loading="loading">
            <div style="display: inline-block">
              <ElUpload
                class="upload-demo"
                drag
                :auto-upload="true"
                :action="uploadExeUrl"
                accept=".exe"
                :data="data.form"
                :before-upload="exeBeforeUpload"
                :on-progress="exeOnProgressUpload"
                :on-success="exeOnSuccessUpload"
                :on-remove="exeOnRemoveFile"
                :headers="headers"
                :limit="1"
              >
                <span
                  class="icon-[ep--upload-filled]"
                  style="width: 50px; height: 50px; color: #9b9696"
                ></span>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
              </ElUpload>
            </div>
            <div class="desc">
              <p>
                点击按钮选择应用的安装包，或拖拽文件到此区域
                <br />
                仅支持exe文件，文件大小不超过100MB
              </p>
            </div>
            <div style="width: 500px; margin: 0 auto">
              <ElButton
                type="primary"
                style="width: 500px; margin-top: 12px"
                @click="exeNext()"
              >
                下一步
              </ElButton>
            </div>
          </div>
          <div v-if="Number(data.active) === 2" v-loading="loading">
            <div style="display: inline-block">
              <ElUpload
                class="upload-demo"
                drag
                :auto-upload="true"
                :action="uploadYmlUrl"
                accept=".yml"
                :before-upload="ymlBeforeUpload"
                :on-progress="ymlOnProgressUpload"
                :on-success="ymlOnSuccessUpload"
                :on-remove="ymlOnRemoveFile"
                :headers="headers"
                :limit="1"
              >
                <span
                  class="icon-[ep--upload-filled]"
                  style="width: 50px; height: 50px; color: #9b9696"
                ></span>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
              </ElUpload>
            </div>
            <div class="desc">
              <p>
                点击按钮选择应用的安装包，或拖拽文件到此区域
                <br />
                仅支持yml文件，文件大小不超过100MB
              </p>
            </div>
            <div style="width: 500px; margin: 0 auto">
              <ElButton
                type="primary"
                style="width: 500px; margin-top: 12px"
                @click="ymlNext()"
              >
                下一步
              </ElButton>
            </div>
          </div>
          <template v-if="Number(data.active) === 3">
            <div
              style="margin-top: 60px; margin-bottom: 100px; text-align: center"
            >
              <ElForm
                :model="data.appForm"
                :rules="rules"
                ref="addForm"
                label-width="110px"
                size="default"
              >
                <ElFormItem
                  label="当前版本"
                  class="required"
                  prop="current_version"
                >
                  <ElInput v-model="data.appForm.current_version" />
                </ElFormItem>
                <ElFormItem
                  label="版本更新说明"
                  class="required"
                  prop="features"
                >
                  <ElInput
                    type="textarea"
                    :rows="4"
                    v-model="data.appForm.features"
                    maxlength="500"
                    show-word-limit
                  />
                </ElFormItem>
                <ElFormItem label="备注" prop="memo">
                  <ElInput
                    type="textarea"
                    :rows="4"
                    v-model="data.appForm.memo"
                    maxlength="500"
                    show-word-limit
                  />
                </ElFormItem>
              </ElForm>
            </div>
            <div style="width: 660px; margin: 0 auto; margin-top: 12px">
              <ElRow :gutter="10">
                <ElCol :span="12">
                  <ElButton style="width: 100%" @click="before()">
                    上一步
                  </ElButton>
                </ElCol>
                <ElCol :span="12">
                  <ElButton
                    type="primary"
                    style="width: 100%"
                    @click="finish()"
                  >
                    完成
                  </ElButton>
                </ElCol>
              </ElRow>
            </div>
          </template>
          <template v-if="Number(data.active) === 4">
            <template v-if="data.result.success">
              <i
                class="el-icon-success"
                style="font-size: 80pt; color: #67c23a"
              ></i>
              <br /><br />
              <div style="font-size: 25pt">应用发布成功</div>
              <div>
                <ElButton
                  type="primary"
                  style="margin-top: 12px"
                  size="default"
                  @click="closeTab()"
                >
                  关&ensp;闭
                </ElButton>
              </div>
            </template>
            <template v-else>
              <i
                class="el-icon-error"
                style="font-size: 80pt; color: #f56c6c"
              ></i>
              <br /><br />
              <div style="font-size: 25pt">应用发布失败</div>
              <div style="margin-top: 10px; font-size: 14px; color: #999">
                <i class="el-icon-warning" style="color: #f56c6c"></i>&ensp;{{
                  data.result.message
                }}
              </div>
              <div>
                <ElButton
                  type="primary"
                  style="margin-top: 12px"
                  size="default"
                  @click="closeTab()"
                >
                  关&ensp;闭
                </ElButton>
              </div>
            </template>
          </template>
        </div>
      </div>
    </ElCard>
  </Page>
</template>
<style lang="scss" scoped>
.card {
  height: 100%;
  vertical-align: middle;
}

.content {
  width: 1000px;
  height: calc(100vh - 264px);
  margin: 50px auto;
}

.form {
  width: 600px;
  margin: 50px auto;
}

.desc {
  width: 100%;
  padding: 0;
  color: rgb(0 0 0 / 45%);
}

.desc h3 {
  margin: 0 0 12px;
  font-size: 16px;
  font-weight: 500;
  line-height: 32px;
  color: rgb(0 0 0 / 45%);
}

.desc h4 {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: rgb(0 0 0 / 45%);
}

.desc p {
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 22px;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  color: red;
  content: '* ';
}

.el-upload-dragger {
  width: 500px !important;
  margin: 0 auto !important;
}
</style>
