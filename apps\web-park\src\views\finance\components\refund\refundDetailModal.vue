<script lang="ts" setup>
import type { FormInstance } from 'element-plus';

import type { NSFinance } from '#/api/park/finance';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { ElButton, ElForm, ElFormItem, ElInput, ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { FinanceApi } from '#/api';

defineOptions({
  name: 'RefundDetailModal', // 退款管理-临停/长租退款详情弹窗
});

const emits = defineEmits(['update']);
const formState = ref<NSFinance.IRefundDetailResult>({});
const dialogType = ref('');
const curId = ref('');
const curRefundType = ref(1);
const deleteForm = ref({
  id: '',
  audit_reason: '',
});
const rules = ref({
  audit_reason: [
    { required: true, message: '请输入取消原因', trigger: 'blur' },
  ],
});
const returnLoading = ref(false);
const confirmLoading = ref(false);
const stopSchema = [
  {
    component: 'Text',
    fieldName: 'order_no',
    label: '订单编号',
  },
  {
    component: 'Text',
    fieldName: 'park_name',
    label: '停车场名称',
  },
  {
    component: 'Text',
    fieldName: 'park_region_name',
    label: '子场名称',
  },
  {
    component: 'Text',
    fieldName: 'in_time',
    label: '入场时间',
  },
  {
    component: 'Text',
    fieldName: 'in_gateway_name',
    label: '入场通道',
  },
  {
    component: 'Text',
    fieldName: 'out_time',
    label: '出场时间',
  },
  {
    component: 'Text',
    fieldName: 'out_gateway_name',
    label: '出场通道',
  },
  {
    component: 'Text',
    fieldName: 'duration',
    label: '停车时长',
  },
  {
    component: 'Text',
    fieldName: 'plate_no',
    label: '车牌号码',
  },
  {
    component: 'Text',
    fieldName: 'should_pay_money',
    label: '缴费金额',
  },
  {
    component: 'Text',
    fieldName: 'refund_user',
    label: '退款人姓名',
  },
  {
    component: 'Text',
    fieldName: 'card_mobile',
    label: '退款人手机号',
  },
];
const leaveSchema = [
  {
    component: 'Text',
    fieldName: 'park_name',
    label: '停车场名称',
  },
  {
    component: 'Text',
    fieldName: 'space_code',
    label: '车位编号',
  },
  {
    component: 'Text',
    fieldName: 'rent_rule_name',
    label: '规则名称',
  },
  {
    component: 'Text',
    fieldName: 'rent_rule_type_desc',
    label: '长租类型',
  },
  {
    component: 'Text',
    fieldName: 'product_name',
    label: '产品类型',
  },
  {
    component: 'Text',
    fieldName: 'order_money',
    label: '产品金额',
  },
  {
    component: 'Text',
    fieldName: 'valid_time',
    label: '有效期',
  },
  {
    component: 'Text',
    fieldName: 'plate_nos',
    label: '车牌号',
  },
  {
    component: 'Text',
    fieldName: 'mbr_member_name',
    label: '车主姓名',
  },
  {
    component: 'Text',
    fieldName: 'mbr_member_mobile',
    label: '手机号',
  },
  {
    component: 'Text',
    fieldName: 'rent_end_time',
    label: '长租截止日期',
  },
];
const commonSchema = [
  {
    component: 'Text',
    fieldName: 'refund_money',
    label: '退款金额',
  },
  {
    component: 'Text',
    fieldName: 'refund_channel_desc',
    label: '退款渠道',
  },
  {
    component: 'Text',
    fieldName: 'refund_account',
    label: '退款账号',
  },
  {
    component: 'Text',
    fieldName: 'refund_reason',
    label: '退款原因',
  },
];
const formRef = ref<FormInstance>();
const [RefundDetailModalForm, RDMFRef] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    colon: true,
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false,
  layout: 'horizontal',
  schema: [],
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});

/**
 * 取消退款原因弹窗配置
 */
const [DelModal, delModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (!isOpen) {
      formRef.value!.resetFields();
    }
  },
  onConfirm: () => {
    if (!formRef.value) return;
    formRef.value.validate(async (valid) => {
      if (valid) {
        const params = {
          ...cloneDeep(deleteForm.value),
          ...delModalApi.getData(),
        };
        try {
          await (curRefundType.value === 1
            ? FinanceApi.stopRefundOrderCancelApi(params)
            : FinanceApi.leaveRefundOrderCancelApi(params));
          ElMessage.success('取消打款成功');
          delModalApi.close();
          // eslint-disable-next-line no-use-before-define
          refundDetailModalApi.close();
          emits('update');
        } catch (error) {
          console.error(error);
        }
      }
    });
  },
});

/**
 * 退款详情弹窗配置
 */
const [RefundDetailModal, refundDetailModalApi] = useVbenModal({
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = refundDetailModalApi.getData<Record<string, any>>();
      const { id, type, refundType } = data;
      curId.value = id;
      dialogType.value = type;
      curRefundType.value = refundType;
      if (curRefundType.value === 1) {
        // 临停退款详情
        refundDetailModalApi.setState({ title: '临停退款详情' });
        RDMFRef.setState(() => {
          return {
            schema: [...stopSchema, ...commonSchema],
          };
        });
        refundDetailModalApi.setState({ loading: true });
        try {
          formState.value = await FinanceApi.getRefundStopDetailApi(
            curId.value,
          );
        } finally {
          refundDetailModalApi.setState({ loading: false });
        }
      } else {
        // 长租退款详情
        refundDetailModalApi.setState({ title: '长租退款详情' });
        RDMFRef.setState(() => {
          return {
            schema: [...leaveSchema, ...commonSchema],
          };
        });
        refundDetailModalApi.setState({ loading: true });
        try {
          formState.value = await FinanceApi.getRefundLeaveDetailApi(
            curId.value,
          );
        } finally {
          refundDetailModalApi.setState({ loading: false });
        }
      }
    } else {
      RDMFRef.resetForm();
    }
  },
});

const cancelStop = () => {
  delModalApi.setData({
    id: curId.value,
  });
  delModalApi.open();
};

const returnHandle = async () => {
  const params = {
    id: curId.value,
  };
  returnLoading.value = true;
  try {
    await FinanceApi.refundYopOrderApi(params);
    ElMessage.success('原路返回成功');
    refundDetailModalApi.close();
    emits('update');
  } catch (error) {
    console.error(error);
  } finally {
    returnLoading.value = false;
  }
};

const stopSubmit = async () => {
  const params = {
    id: curId.value,
  };
  confirmLoading.value = true;
  try {
    await (curRefundType.value === 1
      ? FinanceApi.stopCompleteApi(params)
      : FinanceApi.leaveCompleteApi(params));
    ElMessage.success('打款完成成功');
    refundDetailModalApi.close();
    emits('update');
  } catch (error) {
    console.error(error);
  } finally {
    confirmLoading.value = false;
  }
};
</script>

<template>
  <!-- 申请退款弹窗 -->
  <RefundDetailModal
    :close-on-click-modal="false"
    :show-confirm-button="false"
    class="w-[650px]"
  >
    <RefundDetailModalForm>
      <template #order_no>
        {{ formState.order_no || '无' }}
      </template>
      <template #park_name>
        {{ formState.park_name || '无' }}
      </template>
      <template #park_region_name>
        {{ formState.park_region_name || '无' }}
      </template>
      <template #in_time>
        {{ formState.in_time || '无' }}
      </template>
      <template #in_gateway_name>
        {{ formState.in_gateway_name || '无' }}
      </template>
      <template #out_time>
        {{ formState.out_time || '无' }}
      </template>
      <template #out_gateway_name>
        {{ formState.out_gateway_name || '无' }}
      </template>
      <template #duration>
        {{ formState.duration || '无' }}
      </template>
      <template #plate_no>
        {{ formState.plate_no || '无' }}
      </template>
      <template #should_pay_money>
        {{ formState.should_pay_money || '无' }}
      </template>
      <template #refund_user>
        {{ formState.refund_user || '无' }}
      </template>
      <template #card_mobile>
        {{ formState.card_mobile || '无' }}
      </template>
      <template #space_code>
        {{ formState.space_code || '无' }}
      </template>
      <template #rent_rule_name>
        {{ formState.rent_rule_name || '无' }}
      </template>
      <template #rent_rule_type_desc>
        {{ formState.rent_rule_type_desc || '无' }}
      </template>
      <template #product_name>
        {{ formState.product_name || '无' }}
      </template>
      <template #order_money>
        {{ formState.order_money || '无' }}
      </template>
      <template #valid_time>
        <span v-if="formState.valid_start_time && formState.valid_end_time">
          {{ formState.valid_start_time }}——{{ formState.valid_end_time }}
        </span>
        <span v-else>无</span>
      </template>
      <template #plate_nos>
        {{ formState.plate_nos || '无' }}
      </template>
      <template #mbr_member_name>
        {{ formState.mbr_member_name || '无' }}
      </template>
      <template #mbr_member_mobile>
        {{ formState.mbr_member_mobile || '无' }}
      </template>
      <template #rent_end_time>
        {{ formState.rent_end_time || '无' }}
      </template>
      <template #refund_money>
        {{ formState.refund_money || '无' }}
      </template>
      <template #refund_channel_desc>
        {{ formState.refund_channel_desc || '无' }}
      </template>
      <template #refund_account>
        {{ formState.refund_account || '无' }}
      </template>
      <template #refund_reason>
        {{ formState.refund_reason || '无' }}
      </template>
    </RefundDetailModalForm>
    <template #prepend-footer>
      <template v-if="dialogType !== 'review'">
        <ElButton v-if="formState.refund_state === 1" @click="cancelStop">
          取消打款
        </ElButton>
        <template v-if="formState.refund_state === 1">
          <ElButton
            v-if="formState.refund_channel === 7"
            type="primary"
            :loading="returnLoading"
            @click="returnHandle"
          >
            原路返回
          </ElButton>
          <ElButton
            v-else
            type="primary"
            :loading="confirmLoading"
            @click="stopSubmit"
          >
            打款完成
          </ElButton>
        </template>
      </template>
    </template>
  </RefundDetailModal>
  <DelModal
    :close-on-click-modal="false"
    class="w-[650px]"
    title="取消退款原因"
  >
    <ElForm
      ref="formRef"
      :model="deleteForm"
      :rules="rules"
      class="p-4"
      label-position="right"
      label-width="auto"
    >
      <ElFormItem label="取消原因：" prop="audit_reason">
        <ElInput
          v-model="deleteForm.audit_reason"
          clearable
          placeholder="请填写删除原因"
        />
      </ElFormItem>
    </ElForm>
  </DelModal>
</template>
