<script setup lang="ts">
/**
 * @description 客服电话组件
 * @component CustomerServicePhone
 *
 * 功能：
 * 1. 展示客服电话
 * 2. 展示微信小程序二维码
 * 3. 响应式布局适配
 */

const parkCode = '15269';
const servicePhone = '17184037800';
</script>

<template>
  <div class="mt-2 flex h-full items-center justify-between">
    <div class="flex-1 text-base">
      <div>
        <span>车场编码：</span>
        <span class="text-xl">{{ parkCode }}</span>
      </div>
      <div class="mt-2">
        <span>客服电话：</span>
        <span class="text-xl">{{ servicePhone }}</span>
      </div>
    </div>
    <div class="w-1/4 text-center">
      <img
        alt="微信小程序"
        class="w-[94%] object-contain"
        src="/static/qr-code.png"
      />
      <div class="text-sm text-gray-500">微信小程序</div>
    </div>
  </div>
</template>
