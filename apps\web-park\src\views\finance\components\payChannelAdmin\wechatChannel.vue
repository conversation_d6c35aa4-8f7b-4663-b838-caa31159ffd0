<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee, NSPay } from '#/api';

import { computed, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElTag, ElText } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PayApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

import AuthorizedParkingLotModal from './authorizedParkingLotModal.vue';
import WechatChannelDetailModal from './wechatChannelDetailModal.vue';

defineOptions({
  name: 'WechatChannel', // 微信支付渠道
});

interface IFormValues {
  channel_name?: string;
  pay_channel_state?: number;
  time?: string[];
}

interface IParkListItem extends NSEmployee.IEmployeeParkListResult {
  code: string;
  id: string;
  name: string;
}

/**
 * 业务变量
 */
const channelData = ref<NSPay.IChannelData>({});
const titleMap = {
  add: '添加支付渠道',
  edit: '修改支付渠道',
  view: '支付渠道详情',
};
const isReset = ref(false);

const stateOptions = computed(() => {
  return channelData.value.states || [];
});

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开授权停车场列表弹窗
 */
const [APLModal, aplModalApi] = useVbenModal({
  connectedComponent: AuthorizedParkingLotModal,
});

/**
 * 打开支付渠道详情弹窗
 */
const [WCDModal, wcdModalApi] = useVbenModal({
  connectedComponent: WechatChannelDetailModal,
});

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});
/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [ConfirmModal, ConfirmModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ConfirmModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ConfirmModalRef.close();
  };
  ConfirmModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  WCRef.formApi.resetForm();
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  WCRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'channel_name',
      label: '渠道名称：',
      componentProps: {
        clearable: true,
        placeholder: '请输入支付渠道名称',
      },
    },
    {
      component: 'Select',
      fieldName: 'pay_channel_state',
      label: '渠道状态：',
      componentProps: {
        multiple: false,
        clearable: true,
        options: stateOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [],
      fieldName: 'time',
      label: '创建时间：',
      componentProps: {
        clearable: false,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
  // 自定义重置表单方法
  handleReset: onReset,
};

/**
 * 表格配置
 * @description停车缴费管理列表
 */
const gridOptions: VxeGridProps<NSPay.IPagePayChannelRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    {
      field: 'park_count',
      title: '授权停车场',
      sortable: true,
      slots: { default: 'park_count' },
      minWidth: 100,
    },
    {
      field: 'channel_name',
      title: '支付渠道名称',
      minWidth: 200,
    },
    {
      field: 'wx_app_type_display',
      title: '微信应用类型',
      minWidth: 150,
    },
    {
      field: 'pay_channel_state_display',
      title: '支付渠道状态',
      slots: { default: 'pay_channel_state_display' },
      minWidth: 150,
    },
    {
      field: 'created_at',
      title: '创建时间',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 350,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPageWxPayChannel,
    },
  },
};

/**
 * 表格事件
 */
// const gridEvents: VxeGridListeners = {
//   checkboxChange({ row }) {
//     console.error(row);
//   },
// };

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [WechatChannelTable, WCRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  // gridEvents,
});

/**
 * 获取停车缴费列表数据
 */
async function getPageWxPayChannel({ page }: any, formValues: IFormValues) {
  // 查询参数格式化
  let params = {
    ...formValues,
    start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.time;
  if (isReset.value) {
    params = {
      start_time: '',
      end_time: '',
    };
    isReset.value = false;
  }
  try {
    const res = await PayApi.getPageWxPayChannelApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 添加/修改/查看支付渠道
 */
const editPayChannel = async (
  type: keyof typeof titleMap,
  row?: NSPay.IPayChannelDetailResult,
) => {
  let formData = type === 'edit' ? row : undefined;
  if (type === 'view') {
    try {
      wcdModalApi.setState({ loading: true });
      formData = await PayApi.getWxPayChannelDetailApi(row!.id!);
    } catch (error) {
      console.error(error);
    } finally {
      wcdModalApi.setState({ loading: false });
    }
  }
  wcdModalApi.setState({
    title: titleMap[type],
    footer: type !== 'view',
  });
  wcdModalApi
    .setData({
      type,
      formData,
      channelData: channelData.value,
      confirmFn: async (data: NSPay.IPayChannelDetailResult) => {
        let params = data;
        wcdModalApi.setState({ confirmLoading: true });
        try {
          if (type === 'edit') {
            params = {
              ...params,
              id: row!.id,
            };
            await PayApi.updateWxPayChannelApi(params);
          } else {
            await PayApi.createWxPayChannelApi(params);
          }
          ElMessage.success(`${titleMap[type]}成功`);
          wcdModalApi.close();
          WCRef.query();
        } catch (error) {
          console.error(error);
        } finally {
          wcdModalApi.setState({ confirmLoading: false });
        }
      },
    })
    .open();
};

/**
 * 禁用支付渠道
 */
const updatePayChannelState = async (row: NSPay.IPagePayChannelRow) => {
  const { id, pay_channel_state } = row;
  const curState = pay_channel_state === 1 ? '禁用' : '启用';
  showConfirmModal({
    title: `支付渠道${curState}`,
    description: `${curState}支付渠道，确定${curState}吗？`,
    onConfirm: async () => {
      const params = {
        id,
        pay_channel_state,
      };
      await PayApi.updateWxPayChannelStateApi(params);
      ElMessage.success('更新状态成功');
      WCRef.query();
    },
  });
};

/**
 * 查看授权车场
 */
const showSelectPark = async (id: string) => {
  const res = await PayApi.getWxPayChannelDetailApi(id);
  const selectParkCheck = res.prk_park_list!.map((item) => {
    return {
      ...item,
      park_id: item.prk_park_id,
      park_name: item.prk_park_name,
    };
  });
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    id,
    isNoEmpty: true,
    isMultiple: true,
    needAllParameter: true,
    selectArray: selectParkCheck,
    confirmFn: async (newSelectArray: IParkListItem[]) => {
      const params = {
        id,
        prk_park_list: newSelectArray.map((item) => {
          return {
            app_auth_token: item.app_auth_token,
            prk_park_code: item.code,
            prk_park_id: item.id,
            prk_park_name: item.name,
            sub_appid: item.sub_appid,
            sub_mchid: item.sub_mchid,
          };
        }),
      };
      await PayApi.authWxPayChannelParkApi(params);
      ElMessage.success('更改授权车场成功');
      PSModalRef.close();
      WCRef.query();
    },
  }).open();
};

/**
 * 打开授权停车场列表弹窗
 */
const showAuthorizedParkingLot = (id: string) => {
  aplModalApi
    .setData({
      id,
      channelType: 'wechat',
    })
    .open();
};

/**
 * 获取支付渠道状态标签颜色
 */
const getPayChannelStateColor = (state: number) => {
  return state === 1 ? 'success' : 'danger';
};

defineExpose({ channelData });
</script>
<template>
  <div class="h-full">
    <!-- 微信支付渠道列表 -->
    <WechatChannelTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElButton type="primary" @click="editPayChannel('add')">
          {{ titleMap.add }}
        </ElButton>
      </template>
      <template #park_count="{ row }">
        <ElButton link type="primary" @click="showAuthorizedParkingLot(row.id)">
          {{ row.park_count }}
        </ElButton>
      </template>
      <template #pay_channel_state_display="{ row }">
        <ElTag :type="getPayChannelStateColor(row.pay_channel_state)">
          {{ row.pay_channel_state_display }}
        </ElTag>
      </template>
      <template #actions="{ row }">
        <ElButton link type="primary" @click="editPayChannel('view', row)">
          详情
        </ElButton>
        <ElButton link type="primary" @click="editPayChannel('edit', row)">
          修改
        </ElButton>
        <ElButton link type="primary" @click="showSelectPark(row.id)">
          授权车场
        </ElButton>
        <ElButton
          link
          :type="row.pay_channel_state === 1 ? 'danger' : 'success'"
          @click="updatePayChannelState(row)"
        >
          {{ row.pay_channel_state === 1 ? '禁用' : '启用' }}
        </ElButton>
      </template>
    </WechatChannelTable>
    <!-- 支付渠道详情弹窗 -->
    <WCDModal />
    <!-- 授权车场列表 -->
    <APLModal />
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
    <!-- 更新支付渠道状态二次确认弹窗 -->
    <ConfirmModal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </ConfirmModal>
  </div>
</template>
