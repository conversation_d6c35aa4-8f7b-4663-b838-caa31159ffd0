<script name="Department" setup lang="ts">
import { onActivated, reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElCol, ElMessage, ElRow, ElTabPane, ElTabs } from 'element-plus';

import DepartmentEmployeeTable from './department/DepartmentEmployeeTable.vue';
import DepartmentTree from './department/DepartmentTree.vue';
import SubDepartmentTable from './department/SubDepartmentTable.vue';

const sub_table = ref(SubDepartmentTable);
const emp_table = ref(DepartmentEmployeeTable);
const activeDate = reactive({
  activeName: 'subDepartment',
});
let dept = reactive({
  name: '',
  parent_department_id: 0,
});
// 员工通讯录查询
const searchEmployees = () => {
  // queryParamsEmp.department_id = dept.parent_department_id;
  emp_table.value.getDataList(dept.parent_department_id);
};
// 构建查询
const initDeptData = () => {
  if (activeDate.activeName === 'subDepartment') {
    sub_table.value.getData(dept.parent_department_id);
  }
  if (activeDate.activeName === 'departmentEmployee') {
    searchEmployees();
  }
};
onActivated(() => {
  initDeptData(); // 初始化查询
});
// 树节点点击调用
const initDepartmentTree = (queryDept: {
  name: string;
  parent_department_id: number;
}) => {
  dept = queryDept;
  initDeptData();
};
// 标签页切换
const tabsHandleClick = (_tab: any) => {
  initDeptData();
};
const dept_tree = ref<any>(null);
const flushTree = () => {
  dept_tree.value.initDepartmentTree();
};
// 新增部门

const handleAdd = () => {
  if (
    dept.parent_department_id === undefined ||
    dept.parent_department_id === 0
  ) {
    ElMessage({
      message: '请选择部门',
      type: 'warning',
    });
  } else {
    sub_table.value.handleEdit('add', dept);
  }
};
</script>
<template>
  <Page auto-content-height class="flex h-full flex-col">
    <ElRow :gutter="10" style="height: 100%">
      <ElCol :xs="24" :sm="4" style="height: 100%">
        <DepartmentTree
          ref="dept_tree"
          @init-data="initDepartmentTree"
          style="height: 100%"
        />
      </ElCol>
      <ElCol
        :xs="24"
        :sm="20"
        style="height: 100%; overflow: hidden; border-radius: 8px"
      >
        <ElTabs
          v-model="activeDate.activeName"
          type="border-card"
          @tab-click="tabsHandleClick"
          class="my-table-container"
          style="height: 100%"
        >
          <ElTabPane
            style="height: 100%"
            label="子部门信息"
            name="subDepartment"
          >
            <div style="height: 100%">
              <SubDepartmentTable
                @handle-add="handleAdd"
                ref="sub_table"
                @flush="flushTree"
              />
            </div>
          </ElTabPane>
          <ElTabPane
            label="员工通讯录"
            name="departmentEmployee"
            class="table-warp h-full"
            style="height: 100%"
          >
            <DepartmentEmployeeTable ref="emp_table" />
          </ElTabPane>
        </ElTabs>
      </ElCol>
    </ElRow>
  </Page>
</template>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  flex: 1;
}

:deep(.el-tabs) {
  padding: 0 !important;
  overflow: hidden !important;
  background: transparent !important;
  border-radius: 8px !important;
}

::v-deep .el-tabs__content {
  padding: 0 !important;
}

::v-deep .rounded-md {
  border-radius: 0 !important;
}

::v-deep .el-card__body {
  height: calc(100% - 40px);
  padding: 0 !important;
}
</style>
