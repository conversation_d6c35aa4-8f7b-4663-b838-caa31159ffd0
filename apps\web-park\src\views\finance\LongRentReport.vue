<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSFinance } from '#/api/park/finance';

import { onBeforeMount, onMounted, ref } from 'vue';

import { Page, useVbenModal, VbenCountToAnimator } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, FinanceApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'LongRentReport', // 长租报表列表
});

interface IFormValues {
  park_id: string;
  park_name: string;
  time?: string[];
  start_time: string;
  end_time: string;
  park_types?: number[];
  time_type?: number;
}

/**
 * 业务变量
 */
const parkTypeOptions = ref<CommonModule.IEnumItem[]>([]);
const payCountList = ref([
  { label: '长租总收入', amount: 0, count: 0 },
  { label: '新开通总收入', amount: 0, count: 0 },
  { label: '续费总收入', amount: 0, count: 0 },
]);
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 禁用日期方法
 */
const disabledDate = (
  date: Date | dayjs.Dayjs | null | number | string | undefined,
) => {
  return dayjs(date).isAfter(Date.now());
};

/**
 * 快捷日期选项
 */
const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      LRRRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  LRRRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  LRRRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [
        `${dayjs().startOf('month').format('YYYY-MM-DD')}`,
        `${dayjs().format('YYYY-MM-DD')}`,
      ],
      fieldName: 'time',
      label: '统计日期：',
      componentProps: {
        clearable: false,
        type: 'daterange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '统计开始日期',
        endPlaceholder: '统计结束日期',
        disabledDate,
        shortcuts,
      },
    },
    {
      component: 'Select',
      fieldName: 'park_types',
      label: '车辆属性：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: parkTypeOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
  // 自定义重置表单方法
  handleReset: onReset,
};

/**
 * 表格配置
 * @description停车缴费管理列表
 */
const gridOptions: VxeGridProps<NSFinance.IPagingRentSpaceReportsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    {
      field: 'statistics_date',
      title: '统计日期',
      sortable: true,
      minWidth: 150,
    },
    { field: 'park_name', title: '停车场名称', minWidth: 200 },
    { field: 'id', title: '车场ID', sortable: true, minWidth: 100 },
    {
      field: 'total_money',
      title: '长租总收入(元)',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'new_money',
      title: '新开通收入(元)',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'new_count',
      title: '新开通笔数',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'renew_park_money',
      title: '车场续费收入(元)',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'renew_park_count',
      title: '车场续费笔数',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'renew_money',
      title: '小程序续费收入(元)',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'renew_count',
      title: '小程序续费笔数',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'refund_money',
      title: '退款支出(元)',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'refund_count',
      title: '退款笔数',
      sortable: true,
      minWidth: 200,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPagingRentSpaceReports,
    },
  },
};

/**
 * 表格事件
 */
// const gridEvents: VxeGridListeners = {
//   checkboxChange({ row }) {
//     console.error(row);
//   },
// };

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [LongRentReportTable, LRRRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  // gridEvents,
});

/**
 *  获取停车缴费金额数目信息
 */
const getRentSpaceReportMoney = async (
  { page }: any,
  formValues: IFormValues,
) => {
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.time;
  if (isReset.value) {
    delete params.park_types;
  }
  try {
    const res = await FinanceApi.getRentSpaceReportMoneyApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    const amounts = [res.total_money, res.new_money, res.renew_money];
    const counts = [res.total_count, res.new_count, res.renew_count];
    payCountList.value = payCountList.value.map((item, index) => {
      return {
        ...item,
        amount: amounts[index]!,
        count: counts[index]!,
      };
    });
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
};

/**
 * 获取停车缴费列表数据
 */
async function getPagingRentSpaceReports(
  { page }: any,
  formValues: IFormValues,
) {
  getRentSpaceReportMoney({ page }, formValues!);
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    start_time: formValues!.time?.[0]
      ? dayjs(formValues!.time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.time?.[1]
      ? dayjs(formValues!.time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.time;
  if (isReset.value) {
    delete params.park_types;
    isReset.value = false;
  }
  try {
    const res = await FinanceApi.getPagingRentSpaceReportsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSelectOptionsData = async () => {
  // 获取车辆属性
  const params = [
    {
      enum_key: 'types',
      enum_value: 'EnumParkType',
    },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    parkTypeOptions.value = res.types;
  } catch (error) {
    console.error(error);
  }
};

const loadData = () => {
  LRRRef.setLoading(true);
  setTimeout(() => {
    LRRRef.setLoading(false);
    LRRRef.query();
  }, 200);
};

onBeforeMount(() => {
  initSelectOptionsData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.role_id === 1) {
    loadData();
    return false;
  }
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    LRRRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--长租报表 -->
    <LongRentReportTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <div class="flex flex-wrap gap-4 text-center font-bold">
          <div v-for="(item, index) in payCountList" :key="index">
            <span class="pr-1 font-normal">{{ item.label }}:</span>
            <VbenCountToAnimator
              class="pr-1"
              :decimals="2"
              :duration="1000"
              :end-val="item.amount"
              :start-val="0"
              color="#F56C6C"
              suffix="元"
            />(<VbenCountToAnimator
              :decimals="0"
              :duration="1000"
              :end-val="item.count"
              :start-val="0"
              suffix="笔"
            />)
          </div>
        </div>
      </template>
    </LongRentReportTable>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
