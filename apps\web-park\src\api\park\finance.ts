import { requestClient } from '#/api/request';

export namespace NSFinance {
  export interface IPagingShiftHandoverRecordsParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    shift_name: string;
    on_time: string;
    off_time: string;
  }

  export interface IPagingShiftHandoverRecordsRow {
    id: string;
    park_name: string;
    ali_money: number;
    cash_money: number;
    debate_money: number;
    etc_money: number;
    flush_loss_money: number;
    manual_money: number;
    payed_money: number;
    should_pay_money: number;
    special_loss_money: number;
    special_money: number;
    wx_money: number;
    off_time: string;
    on_time: string;
    shift_name: string;
  }

  export interface IPagingShiftHandoverRecordsResult {
    current_page: number;
    page_count: number;
    rows: IPagingShiftHandoverRecordsRow[];
    total: number;
  }

  export interface IDayReportTotalResult {
    total_payed_money: number;
    total_payed_money_cnt: number;
    total_should_pay_money: number;
    total_should_pay_money_cnt: number;
    vos: {
      title: string;
      total_count: number;
      total_money: number;
    }[];
  }

  export interface IPagingDayReportsParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    park_types?: number[];
    time: string;
  }

  export interface IPagingDayReportsRow {
    time: string;
    park_name: string;
    park_id: string;
    location: string;
    should_pay_money: number;
    should_pay_money_cnt: number;
    electronic_money_cnt: number;
    payed_money: number;
    payed_money_cnt: number;
    etc_money: number;
    ali_money: number;
    ali_money_cnt: number;
    wx_money: number;
    wx_money_cnt: number;
    parking_third_party_income: number;
    parking_third_party_num: number;
    cash_money: number;
    cash_money_cnt: number;
    special_money: number;
    special_money_cnt: number;
    debate_money: number;
    debate_money_cnt: number;
    special_loss_money: number;
    special_loss_money_cnt: number;
    flush_loss_money: number;
    flush_loss_money_cnt: number;
    manual_money: number;
    manual_money_cnt: number;
    park_in_num: number;
    rent_in_num: number;
    park_out_num: number;
    rent_out_num: number;
  }

  export interface IPagingDayReportsResult {
    current_page: number;
    page_count: number;
    rows: IPagingDayReportsRow[];
    total: number;
  }

  export interface IPagingRentSpaceReportsParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    start_time: string;
    end_time: string;
    park_types?: number[];
    time_type?: number;
  }

  export interface IRentSpaceReportMoneyResult {
    total_money: number;
    total_count: number;
    new_money: number;
    new_count: number;
    renew_money: number;
    renew_count: number;
  }

  export interface IPagingRentSpaceReportsRow {
    statistics_date: string;
    park_name: string;
    id: string;
    total_money: number;
    new_money: number;
    new_count: number;
    renew_park_money: number;
    renew_park_count: number;
    renew_money: number;
    renew_count: number;
    refund_money: number;
    refund_count: number;
  }

  export interface IPagingRentSpaceReportsResult {
    current_page: number;
    page_count: number;
    rows: IPagingRentSpaceReportsRow[];
    total: number;
  }

  export interface IPagingRefundOrderParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    start_time: string;
    end_time: string;
    refund_types?: number[];
    refund_states?: number[];
  }

  export interface IPagingRefundOrderRow {
    apply_operator: string;
    audit_reason: string;
    created_at: string;
    id: string;
    park_name: string;
    refund_money: string;
    refund_state: number;
    refund_state_desc: string;
    refund_time: string;
    refund_type: number;
    refund_type_desc: string;
  }

  export interface IPagingRefundOrderResult {
    current_page: number;
    page_count: number;
    rows: IPagingRefundOrderRow[];
    total: number;
  }

  export interface IRefundDetailResult {
    order_no?: string;
    park_name?: string;
    park_region_name?: string;
    in_time?: string;
    in_gateway_name?: string;
    out_time?: string;
    out_gateway_name?: string;
    duration?: string;
    plate_no?: string;
    should_pay_money?: number;
    refund_user?: string;
    card_mobile?: string;
    space_code?: string;
    rent_rule_name?: string;
    rent_rule_type_desc?: string;
    product_name?: string;
    order_money?: number;
    valid_start_time?: string;
    valid_end_time?: string;
    plate_nos?: string;
    mbr_member_name?: string;
    mbr_member_mobile?: string;
    rent_end_time?: string;
    refund_money?: number;
    refund_channel_desc?: string;
    refund_account?: string;
    refund_reason?: string;
    refund_state?: number;
    refund_channel?: number;
  }

  export interface IStopRefundOrderCancelParams {
    id: string;
    audit_reason: string;
  }
}

/**
 * 获取交接班报表列表
 * @param params 获取交接班报表列表
 */
export const getPagingShiftHandoverRecordsApi = (
  params: NSFinance.IPagingShiftHandoverRecordsParams,
) => {
  return requestClient.post<NSFinance.IPagingShiftHandoverRecordsResult>(
    '/console/park/finance/shiftHandoverRecords/pagingShiftHandoverRecords',
    params,
  );
};

/**
 * 获取日报表关键指标数据
 * @param params 获取日报表关键指标数据
 */
export const getPagingDayTotalApi = (
  params: NSFinance.IPagingDayReportsParams,
) => {
  return requestClient.post<NSFinance.IDayReportTotalResult>(
    '/console/park/finance/dayReport/getTotal',
    params,
  );
};

/**
 * 获取日报表列表
 * @param params 获取日报表列表
 */
export const getPagingDayReportsApi = (
  params: NSFinance.IPagingDayReportsParams,
) => {
  return requestClient.post<NSFinance.IPagingDayReportsResult>(
    '/console/park/finance/dayReport/pagingDayReports',
    params,
  );
};

/**
 * 获取月报表关键指标数据
 * @param params 获取月报表关键指标数据
 */
export const getPagingMonthTotalApi = (
  params: NSFinance.IPagingDayReportsParams,
) => {
  return requestClient.post<NSFinance.IDayReportTotalResult>(
    '/console/park/finance/monthReport/getTotal',
    params,
  );
};

/**
 * 获取月报表列表
 * @param params 获取月报表列表
 */
export const getPagingMonthReportsApi = (
  params: NSFinance.IPagingDayReportsParams,
) => {
  return requestClient.post<NSFinance.IPagingDayReportsResult>(
    '/console/park/finance/monthReport/pagingMonthReports',
    params,
  );
};

/**
 * 获取长租报表关键指标数据
 * @param params 获取长租报表关键指标数据
 */
export const getRentSpaceReportMoneyApi = (
  params: NSFinance.IPagingRentSpaceReportsParams,
) => {
  return requestClient.post<NSFinance.IRentSpaceReportMoneyResult>(
    '/console/park/finance/rentSpaceReport/getMoney',
    params,
  );
};

/**
 * 获取长租报表列表
 * @param params 获取长租报表列表
 */
export const getPagingRentSpaceReportsApi = (
  params: NSFinance.IPagingRentSpaceReportsParams,
) => {
  return requestClient.post<NSFinance.IPagingRentSpaceReportsResult>(
    '/console/park/finance/rentSpaceReport/pagingRentSpaceReports',
    params,
  );
};

/**
 * 获取退款管理列表
 * @param params 获取退款管理列表
 */
export const getPagingRefundOrderApi = (
  params: NSFinance.IPagingRefundOrderParams,
) => {
  return requestClient.post<NSFinance.IPagingRefundOrderResult>(
    '/console/park/finance/refundOrder/pagingRefundOrder',
    params,
  );
};

/**
 * 获取临停退款详情
 * @param params 获取临停退款详情
 */
export const getRefundStopDetailApi = (id: string) => {
  return requestClient.post<NSFinance.IRefundDetailResult>(
    `/console/park/finance/refundOrder/getRefundStopDetail/${id}`,
  );
};

/**
 * 获取长租退款详情
 * @param params 获取长租退款详情
 */
export const getRefundLeaveDetailApi = (id: string) => {
  return requestClient.post<NSFinance.IRefundDetailResult>(
    `/console/park/finance/refundOrder/getRefundLeaveDetail/${id}`,
  );
};

/**
 * 临停取消打款
 * @param params 临停取消打款
 */
export const stopRefundOrderCancelApi = (
  params: NSFinance.IStopRefundOrderCancelParams,
) => {
  return requestClient.post(
    '/console/park/finance/refundOrder/stopCancel',
    params,
  );
};

/**
 * 长租取消打款
 * @param params 长租取消打款
 */
export const leaveRefundOrderCancelApi = (
  params: NSFinance.IStopRefundOrderCancelParams,
) => {
  return requestClient.post(
    '/console/park/finance/refundOrder/leavingCancel',
    params,
  );
};

/**
 * 原路返回
 * @param params 原路返回
 */
export const refundYopOrderApi = (params: { id: string }) => {
  return requestClient.post(
    '/console/park/finance/refundOrder/refundYopOrder',
    params,
  );
};

/**
 * 临停打款完成
 * @param params 临停打款完成
 */
export const stopCompleteApi = (params: { id: string }) => {
  return requestClient.post(
    '/console/park/finance/refundOrder/stopComplete',
    params,
  );
};

/**
 * 长租打款完成
 * @param params 长租打款完成
 */
export const leaveCompleteApi = (params: { id: string }) => {
  return requestClient.post(
    '/console/park/finance/refundOrder/leavingComplete',
    params,
  );
};
