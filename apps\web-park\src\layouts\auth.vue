<script lang="ts" setup>
import { computed } from 'vue';

import { AuthPageLayout } from '@vben/layouts';
import { preferences } from '@vben/preferences';

const appName = computed(() => preferences.app.name);
const logo = '/logo.png';
</script>

<template>
  <AuthPageLayout
    :app-name="appName"
    :logo="logo"
    :toolbar-list="['color', 'language', 'theme']"
    page-description="融合移动互联网、物联网、大数据、人工智能技术"
    page-title="专注物联网产品创新 引领智慧城市未来发展"
  >
    <!-- 自定义工具栏 -->
    <!-- <template #toolbar></template> -->
  </AuthPageLayout>
</template>
