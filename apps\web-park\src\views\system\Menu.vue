<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElCol, ElRow } from 'element-plus';

import MenuConfigModule from './menuComponents/MenuConfigModule.vue';
import MenuTreeModule from './menuComponents/MenuTreeModule.vue';
import PermissionConfigModule from './menuComponents/PermissionConfigModule.vue';

const menuTreeRef = ref();
const data = reactive({
  menu: {
    id: undefined,
    name: undefined,
  },
});
// 检查菜单节点
const changeMenuNode = (menus: { id: undefined; name: undefined }) => {
  data.menu = menus;
};
// 获取菜单树
const getMenuTree = () => {
  menuTreeRef.value.getMenuTree();
  data.menu = {
    id: undefined,
    name: undefined,
  };
};
</script>

<template>
  <Page class="overflow-hidden">
    <ElRow :gutter="10" class="h-full">
      <ElCol :span="5" class="h-full">
        <MenuTreeModule
          ref="menuTreeRef"
          style="height: calc(100vh - 120px)"
          @check-menu-node="changeMenuNode"
        />
      </ElCol>
      <ElCol :span="19" class="my-table-container">
        <MenuConfigModule :menu="data.menu" @get-menu-tree="getMenuTree" />
        <PermissionConfigModule :menu="data.menu" />
      </ElCol>
    </ElRow>
  </Page>
</template>
