<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSInvoice } from '#/api';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElSegmented } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { InvoiceApi } from '#/api';

defineOptions({
  name: 'RelativeOrdersModal', // 关联订单弹窗
});

interface resultType {
  current_page: number;
  page_count: number;
  rows?: (
    | NSInvoice.IRelativeParkOrderPagingRow
    | NSInvoice.IRelativeRentOrderPagingRow
    | NSInvoice.IRelativeReserveOrderPagingRow
  )[];
  total: number;
}

/**
 * 业务变量
 */
let ids = reactive({
  park_order_ids: [],
  reserve_order_ids: [],
  rent_order_ids: [],
});
const curFeeType = ref(1); // 1-临停与预约 2-长租费用
const activeName = ref('park');
const selectOptions = [
  { label: '临停费用', value: 'park' },
  { label: '预约车位', value: 'reserve' },
];
// 临停数据grid实例
const parkColumns = [
  { field: 'order_no', title: '订单号', minWidth: 300 },
  { field: 'park_name', title: '停车场名称', minWidth: 150 },
  { field: 'park_region_name', title: '子场名称', minWidth: 150 },
  { field: 'in_time', title: '入场时间', sortable: true, minWidth: 150 },
  { field: 'in_gateway_name', title: '入场通道', minWidth: 150 },
  { field: 'to_time', title: '出场时间', sortable: true, minWidth: 150 },
  { field: 'out_gateway_name', title: '出场通道', minWidth: 150 },
  {
    field: 'park_time',
    title: '停车时长（分钟）',
    sortable: true,
    minWidth: 150,
  },
  { field: 'car_type_desc', title: '车辆类型', minWidth: 150 },
  { field: 'park_type_desc', title: '停车类型', minWidth: 150 },
  { field: 'plate_no', title: '车牌号', minWidth: 150 },
  { field: 'order_state_desc', title: '订单状态', minWidth: 150 },
  { field: 'pay_method_desc', title: '支付方式', minWidth: 100 },
  {
    field: 'should_pay_money',
    title: '应交金额',
    sortable: true,
    minWidth: 100,
  },
  {
    field: 'current_coupon_money',
    title: '优惠金额',
    sortable: true,
    minWidth: 100,
  },
  { field: 'payed_money', title: '实缴金额', sortable: true, minWidth: 100 },
  { field: 'refund_state_desc', title: '退款状态', minWidth: 150 },
  { field: 'charge_name', title: '收费员', minWidth: 150 },
];
// 预约数据grid实例
const reserveColumns = [
  { field: 'code', title: '停车场编号', minWidth: 150 },
  { field: 'park_name', title: '停车场名称', minWidth: 150 },
  { field: 'park_region_name', title: '子场名称', minWidth: 150 },
  { field: 'plate_no', title: '车牌号', minWidth: 150 },
  { field: 'payed_money', title: '预约费', sortable: true, minWidth: 100 },
  {
    field: 'plan_start_time',
    title: '入场时间',
    sortable: true,
    minWidth: 150,
  },
  { field: 'plan_end_time', title: '出场时间', sortable: true, minWidth: 150 },
  { field: 'state_desc', title: '预约状态', minWidth: 150 },
  { field: 'mbr_member_name', title: '会员昵称', minWidth: 150 },
  { field: 'mobile', title: '手机号', minWidth: 150 },
];
// 长租数据grid实例
const rentColumns = [
  { field: 'park_name', title: '停车场名称', minWidth: 150 },
  { field: 'code', title: 'code', minWidth: 100 },
  { field: 'rule_name', title: '规则名称', minWidth: 200 },
  { field: 'long_rent_type_desc', title: '长租类型', minWidth: 150 },
  { field: 'product_name', title: '产品名称', minWidth: 150 },
  { field: 'product_price', title: '产品金额', sortable: true, minWidth: 100 },
  {
    field: 'valid_time',
    title: '长租有效期',
    slots: { default: 'valid_time' },
    minWidth: 200,
  },
  { field: 'pay_state_desc', title: '支付状态', minWidth: 150 },
  { field: 'rent_state_desc', title: '长租状态', minWidth: 150 },
  { field: 'plate_no', title: '车牌号', minWidth: 150 },
  { field: 'mbr_member_nickname', title: '车主姓名', minWidth: 150 },
  { field: 'mbr_member_mobile', title: '手机号', minWidth: 150 },
  { field: 'renew_state_desc', title: '是否续费', minWidth: 150 },
  { field: 'refund_state_desc', title: '退款状态', minWidth: 150 },
];

/**
 * 表格配置
 * @description停车缴费管理列表
 */
const gridOptions: VxeGridProps<
  | NSInvoice.IRelativeParkOrderPagingRow
  | NSInvoice.IRelativeRentOrderPagingRow
  | NSInvoice.IRelativeReserveOrderPagingRow
> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  minHeight: 100,
  // 正常配置列
  columns: [],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getRelativeOrder,
    },
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [RelativeOrdersTable, ROTRef] = useVbenVxeGrid({
  gridOptions,
});

/**
 * 车场列表弹窗配置
 */
const [RelativeOrdersModal, relativeOrdersModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const shareData = relativeOrdersModalApi.getData<Record<string, any>>();
      const { data } = shareData;
      curFeeType.value = data.fee_type;
      ids = {
        park_order_ids: data.park_order_id,
        reserve_order_ids: data.reserve_order_id,
        rent_order_ids: data.rent_order_id,
      };
      if (curFeeType.value === 1) {
        ROTRef.setGridOptions({ columns: parkColumns });
      } else {
        ROTRef.setGridOptions({ columns: rentColumns });
      }
    }
  },
});

/**
 * 获取停车缴费列表数据
 */
async function getRelativeOrder({ page }: any) {
  // 查询参数格式化
  const params = {
    page: page.currentPage,
    limit: page.pageSize,
    ...ids,
  };
  try {
    let res: resultType = {
      current_page: 1,
      page_count: 20,
      total: 0,
    };
    res =
      curFeeType.value === 2
        ? await InvoiceApi.getRelativeRentOrderPagingApi(params)
        : await (activeName.value === 'park'
            ? InvoiceApi.getRelativeParkOrderPagingApi(params)
            : InvoiceApi.getRelativeReserveOrderPagingApi(params));
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 切换tab事件
 */
const changeActiveName = (e: string) => {
  if (e === 'park') {
    ROTRef.setGridOptions({ columns: parkColumns });
  } else {
    ROTRef.setGridOptions({ columns: reserveColumns });
  }
  ROTRef.reload();
};
</script>
<template>
  <!-- 关联订单列表 -->
  <RelativeOrdersModal :footer="false" class="w-[1200px]" title="关联订单列表">
    <RelativeOrdersTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElSegmented
          v-show="curFeeType === 1"
          v-model="activeName"
          :options="selectOptions"
          size="default"
          @change="changeActiveName"
        />
      </template>
      <!-- 自定义-长租有效期 -->
      <template #valid_time="{ row }">
        <span>{{ row.valid_start_time }}~{{ row.valid_end_time }}</span>
      </template>
    </RelativeOrdersTable>
  </RelativeOrdersModal>
</template>
