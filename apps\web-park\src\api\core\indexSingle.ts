import { requestClient } from '#/api/request';

export const IndexSingle = {
  getApiById(data: any) {
    return requestClient.post<any>(`/console/api/getApiById/${data}`);
  },

  // api接口列表
  apiList() {
    return requestClient.get<any>('/console/api/apiList');
  },

  // 查询权限分组下的api树
  getApiPermissionByGroupId(data: any) {
    return requestClient.post<any>(
      `/console/api/getApiPermissionByGroupId/${data}`,
    );
  },

  // 查询角色权限分组下的api树
  getRoleApiPermissionTree(data: any) {
    return requestClient.post<any>(
      `/console/api/getRoleApiPermissionTree/${data}`,
    );
  },
};
