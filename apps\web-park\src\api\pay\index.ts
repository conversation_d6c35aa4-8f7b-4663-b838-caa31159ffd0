import { requestClient } from '#/api/request';

export namespace NSPay {
  export interface IYeeReconciliationVosRow {
    date: string;
    merchant_no: string;
    number: number;
    paid_in_handling_fees: string;
    settlement_handling_fee: string;
    total_amount: string;
    total_amount_received: string;
    total_settlement_amount: string;
    downLoadLoading?: boolean;
  }

  export interface IMonthYeeSettleResult {
    pay_park_summary_vo: {
      count: number | string;
      date: string;
      sum_should_pay_money: number | string;
      sum_total_money: number | string;
    };
    pay_rent_summary_vo: {
      count: number | string;
      date: string;
      sum_should_pay_money: number | string;
      sum_total_money: number | string;
    };
    refund_summary_vo: {
      count: number | string;
      date: string;
      sum_real_refund_amount: number | string;
      sum_refund_amount: number | string;
      sum_refund_merchant_fee: number | string;
    };
    yee_reconciliation_vos: IYeeReconciliationVosRow[];
  }

  export interface IChannelData {
    mchTypes?: {
      key: string;
      value: number;
    }[];
    states?: {
      key: string;
      value: number;
    }[];
    types?: {
      key: string;
      value: number;
    }[];
  }

  export interface IPagePayChannelParams {
    page: number;
    limit: number;
    channel_name?: string;
    pay_channel_state?: number;
    start_time?: string;
    end_time?: string;
  }

  export interface IPagePayChannelRow {
    channel_name: string;
    created_at: string;
    id: string;
    park_count: number;
    pay_channel_state: number;
    pay_channel_state_display: string;
    sub_appid?: string;
    sub_mchid?: string;
    updated_at: string;
    wx_api_v2_private_key: string;
    wx_api_v3_private_key: string;
    wx_apiclient_cert: string;
    wx_apiclient_key: string;
    wx_app_id: string;
    wx_app_name: string;
    wx_app_type: number;
    wx_app_type_display: string;
    wx_mch_id: string;
    wx_mch_name: string;
    wx_mch_type: number;
    wx_mch_type_display: string;
    ali_app_id: string;
    ali_private_key: string;
    ali_public_key: string;
    app_auth_token?: string;
  }

  export interface IPagePayChannelResult {
    current_page: number;
    page_count: number;
    rows: IPagePayChannelRow[];
    total: number;
  }

  export interface IUpdatePayChannelStateParams {
    id: string;
    pay_channel_state: number;
  }

  export interface IPagePayChannelParkParams {
    page: number;
    limit: number;
    pay_channel_id: string;
  }

  export interface IPagePayChannelParkRow {
    app_auth_token?: string;
    prk_park_code: string;
    prk_park_id: string;
    prk_park_name: string;
    sub_appid?: string;
    sub_mchid?: string;
  }

  export interface IPagePayChannelParkResult {
    current_page: number;
    page_count: number;
    rows: IPagePayChannelParkRow[];
    total: number;
  }

  export interface IPayChannelDetailResult {
    id?: string;
    channel_name?: string;
    pay_channel_state?: number;
    pay_channel_state_display?: string;
    wx_mch_id?: string;
    wx_mch_name?: string;
    wx_mch_type?: number;
    wx_mch_type_display?: string;
    wx_app_type?: number;
    wx_app_type_display?: string;
    wx_app_id?: string;
    wx_app_name?: string;
    wx_api_v3_private_key?: string;
    wx_apiclient_cert?: string;
    wx_apiclient_key?: string;
    ali_app_id?: string;
    ali_private_key?: string;
    ali_public_key?: string;
    prk_park_list?: {
      app_auth_token?: string;
      prk_park_code: string;
      prk_park_id: string;
      prk_park_name: string;
      sub_appid: string;
      sub_mchid: string;
    }[];
  }

  export interface IAuthPayChannelParkParams {
    id: string;
    prk_park_list: {
      app_auth_token?: string;
      prk_park_code?: string;
      prk_park_id?: string;
      prk_park_name?: string;
      sub_appid?: string;
      sub_mchid?: string;
    }[];
  }

  export interface IUpdatePayChannelParams {
    id?: string;
    channel_name?: string;
    pay_channel_state?: number;
    wx_api_v3_private_key?: string;
    wx_apiclient_cert?: string;
    wx_apiclient_key?: string;
    wx_app_id?: string;
    wx_app_name?: string;
    wx_app_type?: number;
    wx_mch_id?: string;
    wx_mch_name?: string;
    wx_mch_type?: number;
  }

  export interface ICountTransferByStateResult {
    failure_small_change: number;
    failure_small_sum: number;
    success_small_change: number;
    success_small_sum: number;
    total_small_change: number;
    total_small_sum: number;
  }

  export interface IWxTransferPageParams {
    page: number;
    limit: number;
    park_id: string;
    park_region_id: string;
    order_no?: string;
    plate_no?: string;
    state?: string[];
    start_time?: string;
    end_time?: string;
  }

  export interface IWxTransferPageRow {
    created_at: string;
    fail_reason: string;
    id: string;
    last_order_update_time: string;
    openid: string;
    order_no: string;
    park_name: string;
    park_region_name: string;
    plate_no: string;
    state: string;
    transfer_amount: number;
    transfer_bill_no: string;
  }
  export interface IWxTransferPageResult {
    current_page: number;
    page_count: number;
    rows: IWxTransferPageRow[];
    total: number;
  }
}

/**
 * 获取易宝数据交易月汇总
 * @param params 获取易宝数据交易月汇总
 */
async function getMonthYeeSettleApi(id: string, month: string) {
  return requestClient.get<NSPay.IMonthYeeSettleResult>(
    `/console/pay/order/park/monthYeeSettle/${id}/${month}`,
  );
}

/**
 * 获取易宝数据交易日汇总
 * @param params 获取易宝数据交易日汇总
 */
async function getDayYeeSettleApi(id: string, day: string) {
  return requestClient.get<NSPay.IMonthYeeSettleResult>(
    `/console/pay/order/park/dayYeeSettle/${id}/${day}`,
  );
}

/**
 * 下载易宝数据对账单
 * @param params 下载易宝数据对账单
 */
async function getSettleFileDownloadApi(id: string, day: string) {
  return requestClient.get(
    `/console/pay/order/park/settleFileDownload/${id}/${day}`,
    {
      headers: {
        downLoad: true,
      },
    },
  );
}

/**
 * 获取微信支付渠道列表数据
 * @param params 获取微信支付渠道列表数据
 */
async function getPageWxPayChannelApi(params: NSPay.IPagePayChannelParams) {
  return requestClient.post<NSPay.IPagePayChannelResult>(
    '/console/pay/channel/pageWxPayChannel',
    params,
  );
}

/**
 * 更新微信支付渠道状态(1-启用/2-禁用)
 * @param params 更新微信支付渠道状态(1-启用/2-禁用)
 */
async function updateWxPayChannelStateApi(
  params: NSPay.IUpdatePayChannelStateParams,
) {
  return requestClient.post(
    '/console/pay/channel/updateWxPayChannelState',
    params,
  );
}

/**
 * 获取微信授权车场列表数据
 * @param params 获取微信授权车场列表数据
 */
async function getPageWxPayChannelParkApi(
  params: NSPay.IPagePayChannelParkParams,
) {
  return requestClient.post<NSPay.IPagePayChannelParkResult>(
    '/console/pay/channel/pageWxPayChannelPark',
    params,
  );
}

/**
 * 获取微信授权车场详情数据
 * @param params 获取微信授权车场详情数据
 */
async function getWxPayChannelDetailApi(id: string) {
  return requestClient.post<NSPay.IPayChannelDetailResult>(
    `/console/pay/channel/getWxPayChannelDetail/${id}`,
  );
}

/**
 * 更新微信授权车场详情数据
 * @param params 更新微信授权车场详情数据
 */
async function authWxPayChannelParkApi(
  params: NSPay.IAuthPayChannelParkParams,
) {
  return requestClient.post(
    '/console/pay/channel/authWxPayChannelPark',
    params,
  );
}

/**
 * 新建微信支付渠道设置内容
 * @param params 新建微信支付渠道设置内容
 */
async function createWxPayChannelApi(params: NSPay.IUpdatePayChannelParams) {
  return requestClient.post('/console/pay/channel/createWxPayChannel', params);
}

/**
 * 修改微信支付渠道设置内容
 * @param params 修改微信支付渠道设置内容
 */
async function updateWxPayChannelApi(params: NSPay.IUpdatePayChannelParams) {
  return requestClient.post('/console/pay/channel/updateWxPayChannel', params);
}

/**
 * 获取支付宝支付渠道列表数据
 * @param params 获取支付宝支付渠道列表数据
 */
async function getPageAliPayChannelApi(params: NSPay.IPagePayChannelParams) {
  return requestClient.post<NSPay.IPagePayChannelResult>(
    '/console/pay/channel/pageAliPayChannel',
    params,
  );
}

/**
 * 更新支付宝支付渠道状态(1-启用/2-禁用)
 * @param params 更新支付宝支付渠道状态(1-启用/2-禁用)
 */
async function updateAliPayChannelStateApi(
  params: NSPay.IUpdatePayChannelStateParams,
) {
  return requestClient.post(
    '/console/pay/channel/updateAliPayChannelState',
    params,
  );
}

/**
 * 获取支付宝授权车场列表数据
 * @param params 获取支付宝授权车场列表数据
 */
async function getPageAliPayChannelParkApi(
  params: NSPay.IPagePayChannelParkParams,
) {
  return requestClient.post<NSPay.IPagePayChannelParkResult>(
    '/console/pay/channel/pageAliPayChannelPark',
    params,
  );
}

/**
 * 获取支付宝授权车场详情数据
 * @param params 获取支付宝授权车场详情数据
 */
async function getAliPayChannelDetailApi(id: string) {
  return requestClient.post<NSPay.IPayChannelDetailResult>(
    `/console/pay/channel/getAliPayChannelDetail/${id}`,
  );
}

/**
 * 更新支付宝授权车场详情数据
 * @param params 更新支付宝授权车场详情数据
 */
async function authAliPayChannelParkApi(
  params: NSPay.IAuthPayChannelParkParams,
) {
  return requestClient.post(
    '/console/pay/channel/authAliPayChannelPark',
    params,
  );
}

/**
 * 新建支付宝支付渠道设置内容
 * @param params 新建支付宝支付渠道设置内容
 */
async function createAliPayChannelApi(params: NSPay.IUpdatePayChannelParams) {
  return requestClient.post('/console/pay/channel/createAliPayChannel', params);
}

/**
 * 修改支付宝支付渠道设置内容
 * @param params 修改支付宝支付渠道设置内容
 */
async function updateAliPayChannelApi(params: NSPay.IUpdatePayChannelParams) {
  return requestClient.post('/console/pay/channel/updateAliPayChannel', params);
}

/**
 * 获取停车找零明细金额数据
 * @param params 获取停车找零明细金额数据
 */
async function getCountTransferByStateApi(params: NSPay.IWxTransferPageParams) {
  return requestClient.post<NSPay.ICountTransferByStateResult>(
    '/console/pay/transfer/wx/countTransferByState',
    params,
  );
}

/**
 * 获取停车找零明细列表数据
 * @param params 获取停车找零明细列表数据
 */
async function getWxTransferPageApi(params: NSPay.IWxTransferPageParams) {
  return requestClient.post<NSPay.IWxTransferPageResult>(
    '/console/pay/transfer/wx/getWxTransferPage',
    params,
  );
}

/**
 * 撤销取消商户转账
 * @param params 撤销取消商户转账
 */
async function cancelTransferApi(orderNo: string) {
  return requestClient.post(
    `/console/pay/transfer/wx/cancelTransfer?orderNo=${orderNo}`,
  );
}

/**
 * 重新发起商家转账
 * @param params 重新发起商家转账
 */
async function reTryTransferApi(transferId: string) {
  return requestClient.post(
    `/console/pay/transfer/wx/reTryTransfer?transferId=${transferId}`,
  );
}

export const PayApi = {
  getMonthYeeSettleApi,
  getDayYeeSettleApi,
  getSettleFileDownloadApi,
  getPageWxPayChannelApi,
  updateWxPayChannelStateApi,
  getPageWxPayChannelParkApi,
  getWxPayChannelDetailApi,
  authWxPayChannelParkApi,
  createWxPayChannelApi,
  updateWxPayChannelApi,
  getPageAliPayChannelApi,
  updateAliPayChannelStateApi,
  getPageAliPayChannelParkApi,
  getAliPayChannelDetailApi,
  authAliPayChannelParkApi,
  createAliPayChannelApi,
  updateAliPayChannelApi,
  getCountTransferByStateApi,
  getWxTransferPageApi,
  cancelTransferApi,
  reTryTransferApi,
};
