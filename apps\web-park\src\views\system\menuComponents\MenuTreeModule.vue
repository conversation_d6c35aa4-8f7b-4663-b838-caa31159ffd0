<script name="MenuTreeModule" lang="ts" setup>
import { onActivated, reactive, ref } from 'vue';

import { ElCard, ElOption, ElSelect, ElTree } from 'element-plus';

import { MenuApi } from '#/api';

const emits = defineEmits(['checkMenuNode']);
const treeRef = ref();
const categoriesList = ref<any>([]);
const categoryStr = ref('');
const treeDataList = ref<any>([]);
const defaultProps = reactive({
  children: 'children',
  label: 'name',
});
// 获取菜单树
const getMenuTree = () => {
  treeRef.value.setCheckedKeys([]);
  emits('checkMenuNode', {
    id: undefined,
    name: undefined,
  });
  MenuApi.getMenuTreeApi(categoryStr.value).then((response) => {
    if (response) {
      treeDataList.value = response;
    }
  });
};
// 查询菜单分类
const menuCategoriesList = () => {
  MenuApi.listMenuCategoriesApi().then((response) => {
    if (response) {
      categoriesList.value = response;
      if (categoriesList.value.length > 0) {
        categoryStr.value = categoriesList.value[0].id;
        getMenuTree();
      }
    }
  });
};
onActivated(() => {
  menuCategoriesList();
});

// 当改变菜单分类，菜单树也对应改变
const handleChangeMenuCategory = () => {
  getMenuTree();
};
// 检查菜单节点
const setCheckMenuNode = (data: { id: any }) => {
  const isCheck = treeRef.value.getCheckedNodes().includes(data);
  if (isCheck === true) {
    treeRef.value.setCheckedKeys([data.id]);
    emits('checkMenuNode', data);
  }
};
// 父组件调用子组件需要引入并添加defineExpose
defineExpose({
  getMenuTree,
});
</script>

<template>
  <ElCard class="tree-card" shadow="never">
    <template #header>
      <div class="card-header">
        <span>菜单树</span>
        <ElSelect
          v-model="categoryStr"
          style="width: 120px"
          @change="handleChangeMenuCategory"
        >
          <ElOption
            v-for="item in categoriesList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </ElSelect>
      </div>
    </template>
    <ElTree
      ref="treeRef"
      :data="treeDataList"
      :default-expand-all="true"
      :props="defaultProps"
      check-strictly
      node-key="id"
      show-checkbox
      style="
        max-height: calc(100vh - 200px);
        padding-bottom: 10px;
        overflow: scroll;
      "
      @check="setCheckMenuNode"
    />
  </ElCard>
</template>
<style lang="scss" scoped>
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tree-card {
  height: 100%;
}
</style>
