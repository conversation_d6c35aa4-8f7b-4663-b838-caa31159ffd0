<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';

import { computed, h, onMounted, ref } from 'vue';

import { AuthenticationLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { getCaptchaApi } from '#/api';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });
const authStore = useAuthStore();

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入验证码',
      },
      fieldName: 'captcha',
      label: '验证码',
      rules: z.string().min(1, { message: '请输入验证码' }),
      suffix: renderCaptcha,
    },
  ];
});

// 添加验证码相关的状态
const captchaImg = ref('');

// 获取验证码的函数
async function getCaptcha() {
  try {
    // 这里替换成实际的验证码接口
    const res = await getCaptchaApi();
    captchaImg.value = res.captcha;
    authStore.captchaKey = res.key;
  } catch (error) {
    console.error('获取验证码失败:', error);
  }
}

// 验证码图片渲染函数
function renderCaptcha() {
  return h('img', {
    src: captchaImg.value,
    alt: '验证码',
    class:
      'cursor-pointer h-[40px] w-[150px] border border-gray-300 rounded-md',
    onClick: getCaptcha,
    title: '点击刷新验证码',
  });
}

// 在组件挂载时获取验证码
onMounted(() => {
  getCaptcha();
});
</script>

<template>
  <AuthenticationLogin
    title="您好,商户"
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    :show-forget-password="false"
    :show-register="false"
    :show-third-party-login="false"
    sub-title="请输入您的帐户开启智慧停车共创双赢新时代"
    :show-code-login="false"
    :show-qrcode-login="false"
    @submit="authStore.authLogin"
  />
</template>
