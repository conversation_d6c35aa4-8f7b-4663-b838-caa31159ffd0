<script lang="ts" setup>
import type { TagProps } from 'element-plus';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSParkFee } from '#/api/park/fee';

import { onBeforeMount, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElMessage, ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, ParkFeeApi } from '#/api';
import ParkImage from '#/components/park-image.vue';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'CarOutRecord', // 出场记录列表
});

interface IFormValues {
  car_types?: number[];
  in_time: string[];
  out_time: string[];
  park_id?: string;
  park_name?: string;
  plate_no?: string;
}

type Item = { label: number; type: TagProps['type'] };

/**
 * 业务变量
 */
const carTypeDescMap = ref<Array<Item>>([
  { type: 'danger', label: 0 },
  { type: 'primary', label: 1 },
  { type: 'warning', label: 2 },
  { type: 'success', label: 3 },
  { type: 'info', label: 4 },
]);
const carTypeOptions = ref<CommonModule.IEnumItem[]>([]);
const isReset = ref(false);
const userStore = useUserStore();

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);
const chooseDay = ref(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */
const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      CORRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

/**
 * 禁用日期方法
 */
const disabledDate = (
  date: Date | dayjs.Dayjs | null | number | string | undefined,
) => {
  if (!chooseDay.value) {
    return false;
  }
  // 超过31天后禁用
  const after31Days = dayjs(date).isAfter(
    dayjs(chooseDay.value).add(31, 'day'),
  );
  // 超过31天后禁用
  const before31Days = dayjs(date).isBefore(
    dayjs(chooseDay.value).subtract(31, 'day'),
  );
  return after31Days || before31Days;
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  CORRef.formApi.resetForm();
  selectParkCheck.value = null;
  chooseDay.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  CORRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入车牌号',
      },
    },
    {
      component: 'Select',
      fieldName: 'car_types',
      label: '车辆类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: carTypeOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [
        `${dayjs().format('YYYY-MM-DD')} 00:00:00`,
        `${dayjs().format('YYYY-MM-DD')} 23:59:59`,
      ],
      fieldName: 'out_time',
      label: '出场日期：',
      componentProps: {
        clearable: false,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '出场开始日期',
        endPlaceholder: '出场结束日期',
        disabledDate,
        onCalendarChange: (event: any) => {
          chooseDay.value = event[0];
        },
      },
    },
    {
      component: 'DatePicker',
      defaultValue: null,
      fieldName: 'in_time',
      label: '入场日期：',
      componentProps: {
        clearable: true,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '入场开始日期',
        endPlaceholder: '入场结束日期',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

/**
 * 表格配置
 * @description出场记录管理列表
 */
const gridOptions: VxeGridProps<NSParkFee.IPagingOutRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    { field: 'park_code', title: '停车场编号', minWidth: 120 },
    { field: 'park_name', title: '停车场名称', minWidth: 100 },
    { field: 'park_region_name', title: '子场名称', minWidth: 100, width: 100 },
    { field: 'plate_no', title: '车牌号', minWidth: 100 },
    { field: 'in_time', title: '入场时间', sortable: true, minWidth: 160 },
    { field: 'out_time', title: '出场时间', sortable: true, minWidth: 160 },
    { field: 'duration', title: '停车时长', sortable: true, minWidth: 100 },
    { field: 'gateway_name', title: '出场通道', minWidth: 100, width: 100 },
    { field: 'out_reason_desc', title: '放行原因', minWidth: 100, width: 100 },
    { field: 'main_brand', title: '车辆品牌', minWidth: 100 },
    { field: 'sub_brand', title: '车辆型号', minWidth: 100 },
    {
      field: 'car_type_desc',
      title: '车辆类型',
      minWidth: 120,
      width: 120,
      slots: { default: 'car_type_desc' },
    },
    {
      field: 'in_car_photo_url',
      title: '入场图片',
      slots: { default: 'in_car_photo_url' },
      minWidth: 100,
    },
    {
      field: 'out_car_photo_url',
      title: '出场图片',
      slots: { default: 'out_car_photo_url' },
      minWidth: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: getPagingOutRecords,
    },
  },
};

/**
 * 获取出场记录列表数据
 */
async function getPagingOutRecords({ page }: any, formValues?: IFormValues) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    out_start_time: formValues!.out_time?.[0]
      ? dayjs(formValues!.out_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    out_end_time: formValues!.out_time?.[1]
      ? dayjs(formValues!.out_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_start_time: formValues!.in_time?.[0]
      ? dayjs(formValues!.in_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_end_time: formValues!.in_time?.[1]
      ? dayjs(formValues!.in_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.in_time;
  delete params.out_time;
  try {
    const res = await ParkFeeApi.getPagingOutRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [CarOutRecordTable, CORRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 *  重新加载列表数据
 */
const loadData = () => {
  CORRef.setLoading(true);
  setTimeout(() => {
    CORRef.setLoading(false);
    CORRef.query();
  }, 200);
};

/**
 * 获取车型类型标签颜色
 */
const getCarTypeDescColor = (type: number) => {
  return carTypeDescMap.value.find((item) => item.label === type)?.type;
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = async () => {
  // 获取车辆类型及是否出场
  const params = [
    {
      enum_key: 'outParkStateList',
      enum_value: 'EnumOutParkState',
    },
    {
      enum_key: 'carTypeList',
      enum_value: 'EnumCarType',
    },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    carTypeOptions.value = res.carTypeList.map((item: any) => {
      return {
        ...item,
        label: item.key,
      };
    });
  } catch {
    carTypeOptions.value = [];
  }
};

onBeforeMount(() => {
  initSearchData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    CORRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--出场记录管理表格 -->
    <CarOutRecordTable table-title="出场记录列表">
      <!-- 自定义-车辆类型列 -->
      <template #car_type_desc="{ row }">
        <ElTag :type="getCarTypeDescColor(row.car_type)">
          {{ row.car_type_desc }}
        </ElTag>
      </template>
      <!-- 自定义-入场图片列 -->
      <template #in_car_photo_url="{ row }">
        <ParkImage :src="row.in_car_photo_url" />
      </template>
      <!-- 自定义-出场图片列 -->
      <template #out_car_photo_url="{ row }">
        <ParkImage :src="row.out_car_photo_url" />
      </template>
    </CarOutRecordTable>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
