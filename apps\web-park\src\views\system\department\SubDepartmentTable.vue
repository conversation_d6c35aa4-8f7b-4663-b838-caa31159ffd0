<script name="SubDepartmentTable" setup lang="ts">
import type { ComponentInternalInstance } from 'vue';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getCurrentInstance, onMounted, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElLink,
  ElMessage,
  ElText,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { DepartmentApi } from '#/api';

const addForm = ref();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const tableData = ref([]);
const loading = ref(false);
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
interface Iform {
  id?: string;
  name?: string;
  parent_department_id?: number;
  path_id?: string;
  root?: any;
}
interface Idata {
  queryParams?: object;
  dialogTitle?: string;
  dialogVisible?: any;
  parentDepartmentName?: string;
  deptId?: number;
  form: Iform;
}
const data = reactive<Idata>({
  queryParams: {},
  dialogTitle: 'add',
  dialogVisible: false,
  parentDepartmentName: '',
  deptId: 0,
  form: {
    name: '',
    path_id: '1',
    root: '1',
    parent_department_id: 0,
    id: '',
  },
});
const rules = {
  name: [
    { required: true, message: '部门名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '请输入1-50个字符', trigger: 'blur' },
  ],
  employees: [],
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'department_name',
      label: '部门名称：',
      componentProps: {
        clearable: true,
        placeholder: '部门名称',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-3',
};

/**
 * 表格配置
 * @description 员工管理列表
 */
const gridOptions: VxeGridProps = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    // { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'name',
      title: '部门名称',
    },

    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } = await DepartmentApi.pagingDepartmentApi({
          page: page.currentPage,
          limit: page.pageSize,
          parent_department_id: data.form.parent_department_id,
          ...formValues,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
};
const [DepartmentTable, ETRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
// 保存
const handleSave = () => {
  addForm.value.validate().then(() => {
    if (data.deptId === 0) {
      DepartmentApi.addDepartmentApi(data.form).then(() => {
        ETRef.query();
        // 调用刷新树
        if (proxy) {
          proxy.$emit('flush');
        }
        addForm.value.resetFields();
        // data.dialogVisible = false;
      });
    } else {
      data.form.id = String(data.deptId);
      DepartmentApi.updateDepartmentApi(data.form).then(() => {
        ETRef.query();
        // 调用刷新树
        if (proxy) {
          proxy.$emit('flush');
        }
        addForm.value.resetFields();
        // data.dialogVisible = false;
      });
    }
  });
};
const [depart, departApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    handleSave();
    departApi.close();
  },
  onCancel: () => {
    addForm.value.resetFields();
    departApi.close();
  },
});
const handleEdit = (
  oper: string,
  dept: {
    id: number;
    name: null | undefined;
    parent_department_id: any;
    parent_id: any;
    path_id: any;
    root: any;
  },
) => {
  data.deptId = 0;
  if (oper === 'add') {
    // 新增
    if (dept.name === null || dept.name === undefined) {
      ElMessage({
        message: '请先选择一个上级部门',
        type: 'warning',
      });
      return;
    }
    data.parentDepartmentName = dept.name;
    data.form = {
      name: '',
      parent_department_id: dept.parent_department_id,
    };
  } else {
    // 修改，先去查询部门信息
    data.deptId = dept.id;
    data.form = {
      path_id: dept.path_id,
      root: dept.root,
      name: dept.name || '',
      parent_department_id: dept.parent_id,
    };
  }
  data.dialogTitle = oper;
  departApi.open();
};

const getData = (params: number) => {
  data.form.parent_department_id = params;
  ETRef.query();
};
// 查询条件
const getSearchData = (params: { departmentName?: any }) => {
  loading.value = true;
  data.parentDepartmentName = params.departmentName;
  data.queryParams = params;
  DepartmentApi.pagingDepartmentApi(params).then((response) => {
    tableData.value = response.rows;
    loading.value = false;
  });
};
const handleDel = (row: { id: any }) => {
  showConfirmModal({
    title: '字典类型删除',
    description: '确定要删除吗？',
    onConfirm: async () => {
      DepartmentApi.deleteDepartmentApi(row.id).then(() => {
        ETRef.query();
        // 调用刷新树
        if (proxy) {
          proxy.$emit('flush');
        }
      });
    },
  });
};

onMounted(() => {});

const handleAdd = () => {
  if (proxy) {
    proxy.$emit('handleAdd');
  }
};
defineExpose({ handleEdit, getData, getSearchData });
</script>
<template>
  <div class="h-full">
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <div auto-content-height class="h-full">
      <!--表格内容栏-->
      <DepartmentTable>
        <!-- 表格顶部左侧按钮栏 -->
        <template #toolbar-actions>
          <ElButton type="primary" @click="handleAdd"> 新建子部门 </ElButton>
        </template>
        <template #actions="{ row }">
          <div class="flex items-center justify-center">
            <ElLink
              type="primary"
              :underline="false"
              @click="handleEdit('edit', row)"
            >
              修改&ensp;
            </ElLink>
            <ElLink type="danger" :underline="false" @click="handleDel(row)">
              删除&ensp;
            </ElLink>
          </div>
        </template>
      </DepartmentTable>
    </div>
    <!-- 部门新增/修改 -->
    <depart
      :fullscreen-button="false"
      class="w-[650px]"
      :title="data.dialogTitle === 'add' ? '新增部门' : '修改部门'"
    >
      <ElForm
        ref="addForm"
        :model="data.form"
        :rules="rules"
        label-width="100px"
      >
        <template v-if="data.dialogTitle === 'add'">
          <ElFormItem label="上级部门">
            <ElInput v-model="data.parentDepartmentName" readonly />
          </ElFormItem>
        </template>
        <ElFormItem label="部门名称" prop="name">
          <ElInput v-model="data.form.name" />
        </ElFormItem>
        <ElFormItem label="路径ID" prop="pathId">
          <ElInput v-model="data.form.path_id" />
        </ElFormItem>
        <ElFormItem label="权限" prop="root">
          <ElInputNumber v-model="data.form.root" />
        </ElFormItem>
      </ElForm>
    </depart>
  </div>
</template>

<style lang="scss"></style>
