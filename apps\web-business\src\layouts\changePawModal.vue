<script lang="ts" setup>
import type { FormInstance } from 'element-plus';

import type { NSPlatform } from '#/api';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElForm, ElFormItem, ElInput, ElNotification } from 'element-plus';

import { PlatformApi } from '#/api';
import { useAuthStore } from '#/store';

const pageState = reactive<{
  new_passwd: string;
  old_passwd: string;
  repeat_passwd: string;
}>({
  old_passwd: '',
  new_passwd: '',
  repeat_passwd: '',
});

const rules = ref({
  old_passwd: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  new_passwd: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    {
      min: 6,
      max: 20,
      message: '密码长度在6-20位之间',
    },
    {
      pattern:
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,20}$/,
      message: '密码应包含大写、小写字母和数字以及字符',
    },
  ],
  repeat_passwd: [
    { required: true, message: '请输入确认密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (value === pageState.new_passwd) {
          callback();
        } else {
          callback(new Error('两次输入密码不一致'));
        }
      },
    },
  ],
});
const authStore = useAuthStore();
const formRef = ref<FormInstance>();
const [Modal] = useVbenModal({
  draggable: true,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      pageState.new_passwd = '';
      pageState.old_passwd = '';
      pageState.repeat_passwd = '';
    }
  },
  onConfirm() {
    formRef.value?.validate((valid) => {
      if (valid) {
        const params: NSPlatform.IChangePasswdParams = {
          old_passwd: pageState.old_passwd,
          new_passwd: pageState.new_passwd,
          repeat_passwd: pageState.repeat_passwd,
        };
        PlatformApi.setChangePasswdApi(params).then(async () => {
          ElNotification({
            title: '密码修改成功',
            message: '为了您的账户安全，请您重新登录',
            type: 'success',
          });
          await authStore.logout(false);
        });
      }
    });
  },
});
</script>
<template>
  <Modal
    :fullscreen-button="false"
    title="密码修改"
    title-tooltip="密码修改后为了保证您的账户安全，请您重新登录"
  >
    <ElForm
      :model="pageState"
      class="mt-8 px-8"
      label-width="auto"
      size="large"
      ref="formRef"
      :rules="rules"
    >
      <ElFormItem label="旧密码：" prop="old_passwd">
        <ElInput
          v-model="pageState.old_passwd"
          placeholder="请输入旧密码"
          type="password"
          show-password
        />
      </ElFormItem>
      <ElFormItem label="新密码：" prop="new_passwd">
        <ElInput
          v-model="pageState.new_passwd"
          placeholder="请输入新密码"
          type="password"
          show-password
        />
      </ElFormItem>
      <ElFormItem label="确认密码：" prop="repeat_passwd">
        <ElInput
          v-model="pageState.repeat_passwd"
          placeholder="请输入确认密码"
          type="password"
          show-password
        />
      </ElFormItem>
    </ElForm>
  </Modal>
</template>
