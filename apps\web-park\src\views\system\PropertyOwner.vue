<script name="PropertyOwnerTable" lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { nextTick, onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton, ElForm, ElFormItem, ElInput, ElText } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PropertyOwnerApi } from '#/api';

const validateMobilePhone = (
  _rule: any,
  value: string,
  callback: (arg0: Error | undefined) => void,
) => {
  if (value !== '') {
    const reg = /^1[3-9]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback(undefined);
};

const addFormRef = ref();
const editFormRef = ref();
const status = ref(false);
const rules = {
  name: [
    {
      required: true,
      message: '请输入产权方名称',
      trigger: 'blur',
    },
  ],
  contact_name: [
    {
      required: true,
      message: '请输入联系人',
      trigger: 'blur',
    },
  ],
  contact_mobile: [
    {
      required: true,
      message: '请输入联系人电话',
      trigger: 'blur',
    },
    {
      trigger: 'blur',
      validator: validateMobilePhone,
    },
  ],
};
const data = reactive<any>({
  form: {
    name: '',
    address: '',
    contact_name: '',
    contact_mobile: '',
  },
  updateForm: {},
});

interface IRowType {
  id: string;
  login_name: string;
  name: string;
  mobile: string;
  role_id: null | string;
  department_name: null | string;
  department_id: null | string;
  role_name: null | string;
  enabled: number;
  created_at: string;
}
/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'name',
      label: '产权方名称：',
      componentProps: {
        clearable: true,
        placeholder: '产权方名称',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'contact_name',
      label: '联系人：',
      componentProps: {
        clearable: true,
        placeholder: '联系人',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'contact_mobile',
      label: '联系人电话：',
      componentProps: {
        clearable: true,
        placeholder: '联系人电话',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

/**
 * 表格配置
 * @description 员工管理列表
 */
const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  columns: [
    // 勾选建议默认都配置
    // { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    { field: 'id', title: 'ID', minWidth: 100, width: 100 },
    { field: 'name', title: '产权方名称', minWidth: 150 },
    { field: 'address', title: '详细地址', minWidth: 150 },
    { field: 'contact_name', title: '联系人', minWidth: 150, width: 150 },
    { field: 'contact_mobile', title: '联系人电话', minWidth: 200 },
    {
      field: 'updated_at',
      title: '修改时间',
      minWidth: 200,
      width: 200,
      sortable: true,
    },

    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 160,
      width: 160,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } =
          await PropertyOwnerApi.PropertyOwnerApi.pagingPropertyOwnerApi({
            page: page.currentPage,
            limit: page.pageSize,
            ...formValues,
          });
        return {
          items: rows,
          total,
        };
      },
    },
  },
};
const [EmployeeTable, ETRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
onMounted(() => {
  status.value = true;
});

/**
 * 新建产权方
 */
const [ModalPropertyOwner, modalPropertyOwnerApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    addFormRef.value.validate().then(() => {
      PropertyOwnerApi.PropertyOwnerApi.createPropertyOwnerApi(data.form)
        .then(() => {
          ETRef.query();
          addFormRef.value.resetFields();
          modalPropertyOwnerApi.close();
        })
        .catch(() => {
          ETRef.query();
        });
    });
  },
  onCancel: () => {
    addFormRef.value.resetFields();
    modalPropertyOwnerApi.close();
  },
});
// 新建产权方
const handleAdd = (addFormRef: { clearValidate: () => void }) => {
  if (status.value === false) {
    nextTick(() => {
      addFormRef.clearValidate();
    });
  }
  data.form = {
    name: undefined,
    address: undefined,
    contact_name: undefined,
    contact_mobile: undefined,
  };
  modalPropertyOwnerApi.open();
  status.value = false;
};

// 删除产权方
const handleDel = (id: any) => {
  showConfirmModal({
    title: '产权方删除',
    description: '确定要删除吗？',
    onConfirm: async () => {
      PropertyOwnerApi.PropertyOwnerApi.deletePropertyOwnerApi(id)
        .then(() => {
          ETRef.query();
        })
        .catch(() => {
          ETRef.query();
        });
    },
  });
};

/**
 * 修改产权方
 */
const [ModalPropertyOwnerUpdate, modalPropertyOwnerUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    editFormRef.value.validate().then(() => {
      PropertyOwnerApi.PropertyOwnerApi.updatePropertyOwnerApi(data.updateForm)
        .then(() => {
          editFormRef.value.resetFields();
          ETRef.query();
          modalPropertyOwnerUpdateApi.close();
        })
        .catch(() => {
          ETRef.query();
        });
    });
  },
  onCancel: () => {
    editFormRef.value.resetFields();
    modalPropertyOwnerUpdateApi.close();
  },
});
// 修改产权方
const handleUpdate = (row: {
  address: any;
  contact_mobile: any;
  contact_name: any;
  id: any;
  name: any;
}) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    address: row.address,
    contact_name: row.contact_name,
    contact_mobile: row.contact_mobile,
  };
  modalPropertyOwnerUpdateApi.open();
};

defineExpose({});
</script>

<template>
  <Page auto-content-height>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <EmployeeTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElButton type="primary" @click="handleAdd(addFormRef)">
          添加产权方
        </ElButton>
      </template>
      <template #actions="{ row }">
        <ElButton link type="primary" @click="handleUpdate(row)">
          修改
        </ElButton>
        <ElButton link type="danger" @click="handleDel(row.id)">
          删除
        </ElButton>
      </template>
    </EmployeeTable>
    <ModalPropertyOwner
      :fullscreen-button="false"
      class="w-[650px]"
      title="添加产权方"
    >
      <ElForm
        ref="addFormRef"
        :model="data.form"
        :rules="rules"
        label-width="100px"
      >
        <ElFormItem label="产权方名称" prop="name">
          <ElInput v-model="data.form.name" />
        </ElFormItem>
        <ElFormItem label="详细地址" prop="address">
          <ElInput v-model="data.form.address" />
        </ElFormItem>
        <ElFormItem label="联系人" prop="contact_name">
          <ElInput v-model="data.form.contact_name" />
        </ElFormItem>
        <ElFormItem label="联系人电话" prop="contact_mobile">
          <ElInput v-model="data.form.contact_mobile" />
        </ElFormItem>
      </ElForm>
    </ModalPropertyOwner>

    <ModalPropertyOwnerUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="修改产权方"
    >
      <ElForm
        ref="editFormRef"
        :model="data.updateForm"
        :rules="rules"
        label-width="100px"
      >
        <ElFormItem label="产权方名称" prop="name">
          <ElInput v-model="data.updateForm.name" />
        </ElFormItem>
        <ElFormItem label="详细地址" prop="address">
          <ElInput v-model="data.updateForm.address" />
        </ElFormItem>
        <ElFormItem label="联系人" prop="contact_name">
          <ElInput v-model="data.updateForm.contact_name" />
        </ElFormItem>
        <ElFormItem label="联系人电话" prop="contact_mobile">
          <ElInput v-model="data.updateForm.contact_mobile" />
        </ElFormItem>
      </ElForm>
    </ModalPropertyOwnerUpdate>
  </Page>
</template>
<style lang="scss" scoped></style>
