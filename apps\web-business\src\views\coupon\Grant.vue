<script lang="ts" setup>
import type { NSPlatform } from '#/api';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElOption,
  ElSelect,
} from 'element-plus';

import { PlatformApi } from '#/api';

const useUseUserStore = useUserStore(); // 引入商家信息
const form = ref({
  plate_number: '',
  coupon_type: '',
  plateInfo: null as NSPlatform.IStopCarTimeResult | null,
  couponInfo: null as NSPlatform.ICouponOptions | null,
});

const couponOptionsList = ref<NSPlatform.ICouponOptions[]>([]);
const formRef = ref();

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};

/**
 * 表单验证规则
 */
const rules = ref({
  plate_number: [{ required: true, message: '请填写使用优免卷的车牌号码' }],
  coupon_type: [{ required: true, message: '请选择一个优免卷' }],
});

/**
 * 优免卷选择改变事件
 */
const handleCouponChange = (value: string) => {
  const option = couponOptionsList.value.find((item) => item.id === value);
  form.value.couponInfo = option || null;
};

/**
 * 车牌号码失去焦点事件
 */
const handleBlurPlateNumberAction = async () => {
  if (form.value.plate_number) {
    try {
      const res = await PlatformApi.getStopCarTimeApi({
        plate_no: form.value.plate_number,
      });
      form.value.plateInfo = res || null;
    } catch (error: any) {
      form.value.plateInfo = null;
      showConfirmModal({
        title: '温馨提示',
        description: error.message,
        onConfirm: () => {},
      });
    }
  }
};

/**
 * 重置表单
 */
const handleResetAction = () => {
  formRef.value.resetFields();
  form.value = {
    plate_number: '',
    coupon_type: '',
    plateInfo: null as NSPlatform.IStopCarTimeResult | null,
    couponInfo: null as NSPlatform.ICouponOptions | null,
  };
};

/**
 * 初始化数据
 * @description 获取优免卷下拉选项
 */
const initData = async () => {
  try {
    const res: NSPlatform.ICouponOptions[] | undefined =
      await PlatformApi.getListCouponMerchantsApi();
    couponOptionsList.value = res || [];
  } catch {
    couponOptionsList.value = [];
  }
};

/**
 * 发放优免卷
 */
const handleGrantAction = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!form.value.plateInfo) {
        showConfirmModal({
          title: '温馨提示',
          description: '没有获取到有效的入场时间，请检查车牌号码是否正确',
          onConfirm: () => {},
        });
        return;
      }
      const couponParamsData = JSON.parse(
        (form.value.couponInfo as NSPlatform.ICouponOptions).coupon_params,
      );
      if (
        (couponParamsData.couponType === 4 ||
          (couponParamsData.couponType === 3 &&
            couponParamsData.discountRatio > 0 &&
            couponParamsData.discountRatio <= 6)) &&
        (form.value.couponInfo as NSPlatform.ICouponOptions)
          .coupon_send_type === 2
      ) {
        return showConfirmModal({
          title: '温馨提示',
          description: '当前优免券折扣幅度较大，无法通过人工发放！',
          onConfirm: () => {},
        });
      }
      try {
        await PlatformApi.grantCouponApi({
          merchant_coupon_id: form.value.couponInfo?.id || '',
          plate_no: form.value.plate_number,
          merchant_id: useUseUserStore.userInfo?.userId as string,
          draw_type: 1,
        });

        // 重新获取优免卷下拉选项(刷新数据)
        showConfirmModal({
          title: '温馨提示',
          description: '发放优免卷成功',
          onConfirm: () => {},
        });
        initData();
        handleResetAction();
      } catch (error: any) {
        initData();
        if (error.code !== 'ok' && error.code !== '200') {
          showConfirmModal({
            title: '温馨提示',
            description: error.detail_message,
            onConfirm: () => {},
          });
        }
      }
    }
  });
};

onMounted(() => {
  initData();
});
</script>
<template>
  <Page
    auto-content-height
    title="优免卷人工发放"
    description="您需要先填写入场的车牌号码，选择优免卷类型，核实无误后最后点击发放按钮"
  >
    <!-- 二次确认弹窗 -->
    <Modal
      :fullscreen-button="false"
      content-class="min-h-[80px]"
      :show-cancel-button="false"
      confirm-text="我知道了"
    >
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <div class="card-box h-full">
      <div class="flex h-full flex-col justify-between">
        <ElForm
          :model="form"
          :rules="rules"
          label-width="110px"
          class="p-6"
          ref="formRef"
        >
          <div class="flex h-[70px] flex-row items-center justify-start">
            <ElFormItem
              label="车牌号码:"
              required
              class="!mb-0 w-1/3"
              prop="plate_number"
            >
              <ElInput
                v-model="form.plate_number"
                placeholder="请输入车牌号码"
                @blur="handleBlurPlateNumberAction"
              />
            </ElFormItem>
            <div class="flex flex-1 flex-row px-6" v-if="form.plateInfo">
              <div class="mr-12">
                <span class="mr-2">入场时间:</span>
                <span class="text-red-500">{{ form.plateInfo.in_time }}</span>
              </div>
              <div>
                <span class="mr-2">停车时长:</span>
                <span class="text-red-500">{{
                  form.plateInfo.stop_car_time
                }}</span>
              </div>
            </div>
          </div>
          <div class="flex h-[60px] flex-row items-center justify-start">
            <ElFormItem
              label="优免卷选择:"
              required
              class="!mb-0 w-1/3"
              prop="coupon_type"
            >
              <ElSelect v-model="form.coupon_type" @change="handleCouponChange">
                <ElOption
                  v-for="item in couponOptionsList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.coupon_meta_name"
                >
                  {{ item.coupon_meta_name }}
                </ElOption>
              </ElSelect>
            </ElFormItem>
            <div class="flex-1 px-6" v-if="form.couponInfo">
              <span class="mr-2">优免券还剩:</span>
              <span class="text-red-500">{{
                `${form.couponInfo?.remainder_count}张`
              }}</span>
            </div>
          </div>
        </ElForm>
        <div class="mt-4 flex flex-row justify-end border-t px-6 py-4">
          <ElButton type="primary" @click="handleGrantAction">
            发放优免卷
          </ElButton>
          <ElButton @click="handleResetAction">重置</ElButton>
        </div>
      </div>
    </div>
  </Page>
</template>
