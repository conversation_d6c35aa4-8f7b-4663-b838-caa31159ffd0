import { defineOverridesPreferences } from '@vben/preferences';

/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 * !!! 更改配置后请清空缓存，否则可能不生效
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    name: import.meta.env.VITE_APP_TITLE,
    // 访问模式 设置后端路由模式
    accessMode: 'backend',
    // 是否开启偏好设置
    enablePreferences: !import.meta.env.PROD,
  },
  // 主题设置默认明亮
  theme: {
    mode: 'light',
  },
  widget: {
    // 关闭国际化切换
    languageToggle: false,
  },
  copyright: {
    companyName: '北京惠达万安智慧城市科技有限公司版权所有',
    companySiteLink: 'https://www.huidawanan.com',
    date: '2022',
  },
});
