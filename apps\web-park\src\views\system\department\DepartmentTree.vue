<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { ElCard, ElTree } from 'element-plus';

import { DepartmentApi } from '#/api';

const emits = defineEmits(['initData']);
const queryDept = reactive({
  parent_department_id: 0,
  name: '',
});
const treeData = reactive({
  departmentTree: [],
});

const initDepartmentTree = () => {
  DepartmentApi.listDepartmentTreeApi().then((res) => {
    treeData.departmentTree = res;
  });
};
onMounted(() => {
  initDepartmentTree();
});
const filterNode = (value: any, data: any) => {
  if (!value) return true;
  return data.name.includes(value);
};
const nodeClick = (data: { id: number; label: string }) => {
  queryDept.parent_department_id = data.id;
  queryDept.name = data.label;
  emits('initData', queryDept);
};
const departmentTree = ref<any>(null);
defineExpose({ initDepartmentTree });
</script>
<template>
  <ElCard class="department-tree h-full" shadow="never">
    <template #header>
      <div class="card-header">
        <span>部门树</span>
      </div>
    </template>
    <ElTree
      ref="departmentTree"
      :data="treeData.departmentTree"
      :check-strictly="true"
      default-expand-all
      :expand-on-click-node="false"
      accordion
      node-key="id"
      highlight-current
      :filter-node-method="filterNode"
      @node-click="nodeClick"
      style="height: 100%; padding-bottom: 10px; overflow: scroll"
    />
  </ElCard>
</template>

<style lang="scss" scoped>
.department-tree {
  // height: calc(100vh - 123px);
}

:deep(.el-card__header) {
  padding: 10px;
}
</style>
