<script lang="ts" setup>
import { onBeforeMount, ref, useTemplateRef } from 'vue';

import { Page } from '@vben/common-ui';

import { ElSegmented } from 'element-plus';

import { CommonApi, CommonModule } from '#/api';

import AliChannel from './components/payChannelAdmin/aliChannel.vue';
import WechatChannel from './components/payChannelAdmin/wechatChannel.vue';

defineOptions({
  name: 'PayChannelAdmin', // 支付渠道
});

const activeName = ref('');
const selectOptions = [
  { label: '微信渠道', value: 'wechat' },
  { label: '支付宝渠道', value: 'ali' },
];
const wechatChannelRef = useTemplateRef('wechatChannelRef');
const aliChannelRef = useTemplateRef('aliChannelRef');

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = async () => {
  // 获取支付渠道状态 + 微信商户类型,微信应用类型
  const params = [
    { enum_key: 'states', enum_value: 'EnumPayChannelState' },
    { enum_key: 'types', enum_value: 'EnumWxAppType' },
    { enum_key: 'mchTypes', enum_value: 'EnumWxMchType' },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.CHANNEL,
      params,
    );
    wechatChannelRef.value!.channelData = res;
    aliChannelRef.value!.channelData = res;
    activeName.value = 'wechat';
  } catch (error) {
    console.error(error);
  }
};

onBeforeMount(async () => {
  await initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <template #title>
      <ElSegmented
        v-model="activeName"
        :options="selectOptions"
        size="default"
      />
    </template>
    <WechatChannel v-show="activeName === 'wechat'" ref="wechatChannelRef" />
    <AliChannel v-show="activeName === 'ali'" ref="aliChannelRef" />
  </Page>
</template>
