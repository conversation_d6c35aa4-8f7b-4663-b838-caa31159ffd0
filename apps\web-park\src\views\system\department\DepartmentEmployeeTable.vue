<script name="DepartmentEmployeeTable" setup lang="ts">
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { reactive } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import { Search } from '@element-plus/icons-vue';
import { EmployeeApi } from '#/api';

const data = reactive({
  queryParams: {
    name_or_mobile: '',
    mobile: '',
    department_id: '',
    page: 1,
    limit: 30,
  },
});
/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'name_or_mobile',
      label: '手机号：',
      componentProps: {
        clearable: true,
        placeholder: '手机号',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-3',
};

/**
 * 表格配置
 * @description 员工管理列表
 */
const gridOptions: VxeGridProps = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    // { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'name',
      title: '姓名',
    },
    {
      field: 'mobile',
      title: '手机号',
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } = await EmployeeApi.pagingEmployeesApi({
          page: page.currentPage,
          limit: page.pageSize,
          department_id: data.queryParams.department_id,
          ...formValues,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
};
const [phoneTable, phoneRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const getDataList = (params: any) => {
  data.queryParams.department_id = params;
  phoneRef.query();
};

defineExpose({ getDataList });
</script>
<template>
  <div class="opers">
    <div></div>
  </div>
  <!--表格内容栏-->
  <phoneTable />
</template>

<style lang="scss" scoped>
.page {
  padding: 6px;
  text-align: right;
  background: #ebebeb;
}

.opers {
  display: flex;
  justify-content: space-between;
  padding-bottom: 0;
}
</style>
