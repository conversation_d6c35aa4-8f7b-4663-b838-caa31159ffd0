<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import {
  dayjs,
  ElButton,
  ElCol,
  ElDatePicker,
  ElMessage,
  ElOption,
  ElRow,
  ElSelect,
} from 'element-plus';

const emits = defineEmits([
  'update:date',
  'update:unit',
  'change',
  'update:park_id',
]);
const userStore = useUserStore();
const parkList = ref<any>([]);
const parkId = ref('');
if (userStore && userStore?.userInfo?.userEntity?.park_ids) {
  parkList.value = userStore.userInfo.userEntity.park_ids.map(
    (item: any, index: number | string) => {
      return {
        key: userStore?.userInfo?.userEntity.park_names[index],
        value: item,
      };
    },
  );
  parkId.value = userStore.userInfo.userEntity.park_ids[0];
  emits('update:park_id', parkId.value);
}
const props = defineProps({
  // 时间单位
  unit: {
    type: String,
    default: 'date', // year month date quarter week
  },
  // 时间值
  date: {
    type: Array,
    default: () => {
      return [];
    },
  },
  // 禁用时间单位数组
  disabled: {
    type: Array,
    default: () => {
      return [];
    },
  },
  // 时间单位选择器数据
  typeOptions: {
    type: Array,
    default: () => {
      return [
        {
          value: '2',
          label: '按月',
          type: 'month',
        },
        {
          value: '5',
          label: '按周',
          type: 'week',
        },
        {
          value: '3',
          label: '按日',
          type: 'date',
        },
      ];
    },
  },
  // 禁用时间
  disabledDate: {
    type: Function,
    default: (time: { getTime: () => number }) => {
      return time.getTime() > Date.now();
    },
  },
  // 组件大小
  size: {
    type: String,
    default: '',
  },
  // 弹出框自定义类
  popperClass: {
    type: String,
    default: '',
  },
  // 是否展示时间类型选择器
  showSelect: {
    type: Boolean,
    default: true,
  },
  teleported: {
    type: Boolean,
    default: true,
  },
  // 时间类型(不展示选择器时使用)
  type: {
    type: String,
    default: '',
  },
  // 时间格式化
  formatter: {
    type: String,
    default: 'YYYY-MM-DD',
  },
  // 展示哪些选择器
  showType: {
    type: Array,
    default: () => {
      return ['year', 'month', 'date', 'quarter', 'week'];
    },
  },
  // 展示的选择器value使用key
  typeValue: {
    type: String,
    default: 'value',
  },
}) as any;

const shortcuts = [
  {
    text: '昨天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      return [start, end];
    },
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    },
  },
];
const selectedUnit = ref<any>(props.unit);
// 当前选中的时间选择器类型
const unitType = computed(() => {
  if (!props.showSelect) return props.type;
  if (!selectedUnit.value) return false;
  if (props.typeValue === 'type') return selectedUnit.value;
  if (props.typeValue === 'value')
    return props.typeOptions.find(
      (item: any) => item.value === selectedUnit.value,
    )?.type;
  return false;
});

const state = reactive<any>({
  start_date: undefined,
  end_date: undefined,
  start_year: undefined,
  end_year: undefined,
  start_quarter: undefined,
  end_quarter: undefined,
  date_range: [],
});
const dealDefault = (val: undefined[]) => {
  switch (unitType.value) {
    case 'date':
    case 'month': {
      state.date_range = val;
      break;
    }
    case 'quarter': {
      state.start_quarter = val[0];
      state.end_quarter = val[1];
      break;
    }
    case 'week': {
      state.start_date = val[0];
      state.end_date = val[1];
      break;
    }
    case 'year': {
      state.start_year = val[0];
      state.end_year = val[1];
      break;
    }
    default: {
      break;
    }
  }
};
watch(
  () => props.date,
  (val) => {
    dealDefault(val);
  },
);

onMounted(() => {
  dealDefault(props.date);
});

watch(
  () => props.unit,
  (val) => {
    selectedUnit.value = val;
  },
);
const emitsValueChange = () => {
  const useRange = state.date_range;
  if (selectedUnit.value !== 3) {
    useRange[1] = dayjs(useRange[1])
      .endOf(unitType.value)
      .format(props.formatter);
  }
  emits('update:park_id', parkId.value);
  emits('update:date', useRange);
  emits('update:unit', selectedUnit.value);
  emits('change', useRange);
};
const handleFilter = () => {
  emitsValueChange();
};
// 日
const handleChangeDay = () => {
  emitsValueChange();
};

// 周
const handleChangeWeek = () => {
  const start_date = state.start_date;
  const end_date = state.end_date;

  if (start_date && end_date) {
    if (start_date > end_date) {
      ElMessage({
        message: '结束周必须大于开始周',
        type: 'warning',
      });
    } else {
      state.date_range = [start_date, end_date];
      emitsValueChange();
    }
  } else {
    ElMessage.warning('请选择开始结束周');
  }
};

// 月
const handleChangeMonth = () => {
  emitsValueChange();
};

// 季
const handleChangeQuarter = () => {
  const start_year = state.start_year;
  const start_quarter = state.start_quarter;
  const end_year = state.end_year;
  const end_quarter = state.end_quarter;

  if (start_year && start_quarter && end_year && end_quarter) {
    if (start_year > end_year) {
      ElMessage({
        message: '结束年必须大于开始年',
        type: 'warning',
      });
    } else {
      if (start_year === end_year && start_quarter > end_quarter) {
        ElMessage({
          message: '结束季度必须大于开始季度',
          type: 'warning',
        });
      } else {
        state.date_range = [
          `${start_year}年${start_quarter}季度`,
          `${end_year}年${end_quarter}季度`,
        ];
        emitsValueChange();
      }
    }
  } else {
    ElMessage.warning('请选择开始结束季度');
  }
};

// 年
const handleChangeYear = () => {
  const start_date = state.start_date;
  const end_date = state.end_date;

  if (start_date && end_date) {
    if (start_date > end_date) {
      ElMessage({
        message: '结束年必须大于开始年',
        type: 'warning',
      });
    } else {
      state.date_range = [start_date, end_date];
      emitsValueChange();
    }
  } else {
    ElMessage.warning('请选择开始结束年');
  }
};

const getDefaultDate = () => {
  return [
    dayjs().startOf(unitType.value).format(props.formatter),
    dayjs().endOf(unitType.value).format(props.formatter),
  ];
};
const handleTypeChange = (val: any) => {
  state.date_range = getDefaultDate();
  selectedUnit.value = val;
  emitsValueChange();
};

// 重置
const resetQuery = () => {
  selectedUnit.value = '3';
  return (state.date_range = [
    dayjs().startOf('date').format('YYYY-MM-DD'),
    dayjs().endOf('date').format('YYYY-MM-DD'),
  ]);
};
</script>

<template>
  <div class="flex">
    <ElSelect
      v-model="parkId"
      size="small"
      placeholder="请选择车场"
      class="search-select"
      style="width: 240px; margin-right: 10px"
      @change="handleFilter"
    >
      <ElOption
        v-for="item in parkList"
        :key="item.value"
        :label="item.key"
        :value="item.value"
      />
    </ElSelect>
    <div class="time-range-warp">
      <ElSelect
        v-if="props.showSelect"
        v-model="selectedUnit"
        :popper-class="props.popperClass"
        :size="props.size"
        :teleported="props.teleported"
        class="search-select"
        placeholder="类型"
        style="max-width: 60px"
        @change="handleTypeChange"
      >
        <template v-for="item in props.typeOptions">
          <ElOption
            v-if="props.showType.includes(item.type)"
            :key="item.value"
            :disabled="props.disabled.includes(item.type)"
            :label="item.label"
            :value="item[props.typeValue]"
          />
        </template>
      </ElSelect>
      <div class="time-range-container">
        <!-- 日 -->
        <template v-if="unitType === 'date'">
          <ElDatePicker
            v-model="state.date_range"
            :clearable="false"
            :disabled-date="props.disabledDate"
            :popper-class="props.popperClass"
            :shortcuts="shortcuts"
            :size="props.size"
            :teleported="props.teleported"
            :value-format="props.formatter"
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            style="width: 100%"
            type="daterange"
            @change="handleChangeDay"
          />
        </template>
        <!-- 周 -->
        <template v-if="unitType === 'week'">
          <ElRow>
            <ElCol :span="11">
              <ElDatePicker
                v-model="state.start_date"
                :clearable="false"
                :disabled-date="props.disabledDate"
                :popper-class="props.popperClass"
                :size="props.size"
                :teleported="props.teleported"
                :value-format="props.formatter"
                format="YYYY[年]ww[周]"
                placeholder="开始周"
                style="width: 100%"
                type="week"
                @change="handleChangeWeek"
              />
            </ElCol>
            <ElCol :span="2" class="line text-center"> 至 </ElCol>
            <ElCol :span="11">
              <ElDatePicker
                v-model="state.end_date"
                :clearable="false"
                :disabled-date="props.disabledDate"
                :popper-class="props.popperClass"
                :size="props.size"
                :teleported="props.teleported"
                :value-format="props.formatter"
                format="YYYY[年]ww[周]"
                placeholder="结束周"
                style="width: 100%"
                type="week"
                @change="handleChangeWeek"
              />
            </ElCol>
          </ElRow>
        </template>
        <!-- 月 -->
        <template v-if="unitType === 'month'">
          <ElDatePicker
            v-model="state.date_range"
            :clearable="false"
            :disabled-date="props.disabledDate"
            :popper-class="props.popperClass"
            :size="props.size"
            :teleported="props.teleported"
            :value-format="props.formatter"
            end-placeholder="结束月"
            format="YYYY[年]MM[月]"
            range-separator="至"
            start-placeholder="开始月"
            style="width: 100%"
            type="monthrange"
            @change="handleChangeMonth"
          />
        </template>
        <!-- 季 -->
        <template v-if="unitType === 'quarter'">
          <ElRow :gutter="5">
            <ElCol :span="11">
              <ElRow :gutter="5">
                <ElCol :span="12">
                  <ElDatePicker
                    v-model="state.start_year"
                    :clearable="false"
                    :disabled-date="props.disabledDate"
                    :popper-class="props.popperClass"
                    :size="props.size"
                    :teleported="props.teleported"
                    :value-format="props.formatter"
                    format="YYYY年"
                    placeholder="开始年"
                    style="width: 100%"
                    type="year"
                    @change="handleChangeQuarter"
                  />
                </ElCol>
                <ElCol :span="12">
                  <ElSelect
                    v-model="state.start_quarter"
                    :size="props.size"
                    :teleported="props.teleported"
                    clearable
                    placeholder="开始季度"
                    style="width: 100%"
                    @change="handleChangeQuarter"
                  >
                    <ElOption label="一季度" value="1" />
                    <ElOption label="二季度" value="2" />
                    <ElOption label="三季度" value="3" />
                    <ElOption label="四季度" value="4" />
                  </ElSelect>
                </ElCol>
              </ElRow>
            </ElCol>
            <ElCol
              :span="2"
              align="center"
              style="padding-top: 5px; font-size: 14px"
            >
              至
            </ElCol>
            <ElCol :span="11">
              <ElRow :gutter="5">
                <ElCol :span="12">
                  <ElDatePicker
                    v-model="state.end_year"
                    :disabled-date="props.disabledDate"
                    :popper-class="props.popperClass"
                    :size="props.size"
                    :teleported="props.teleported"
                    :value-format="props.formatter"
                    format="YYYY年"
                    placeholder="结束年"
                    style="width: 100%"
                    type="year"
                    @change="handleChangeQuarter"
                  />
                </ElCol>
                <ElCol :span="12">
                  <ElSelect
                    v-model="state.end_quarter"
                    :popper-class="props.popperClass"
                    :size="props.size"
                    :teleported="props.teleported"
                    placeholder="结束季度"
                    style="width: 100%"
                    @change="handleChangeQuarter"
                  >
                    <ElOption label="一季度" value="1" />
                    <ElOption label="二季度" value="2" />
                    <ElOption label="三季度" value="3" />
                    <ElOption label="四季度" value="4" />
                  </ElSelect>
                </ElCol>
              </ElRow>
            </ElCol>
          </ElRow>
        </template>
        <!-- 年 -->
        <template v-if="unitType === 'year'">
          <ElRow>
            <ElCol :span="11">
              <ElDatePicker
                v-model="state.start_date"
                :clearable="false"
                :disabled-date="props.disabledDate"
                :popper-class="props.popperClass"
                :size="props.size"
                :teleported="props.teleported"
                :value-format="props.formatter"
                format="YYYY年"
                placeholder="开始年"
                style="width: 100%"
                type="year"
                @change="handleChangeYear"
              />
            </ElCol>
            <ElCol :span="2" class="text-center">至</ElCol>
            <ElCol :span="11">
              <ElDatePicker
                v-model="state.end_date"
                :clearable="false"
                :disabled-date="props.disabledDate"
                :popper-class="props.popperClass"
                :size="props.size"
                :teleported="props.teleported"
                :value-format="props.formatter"
                format="YYYY年"
                placeholder="结束年"
                style="width: 100%"
                type="year"
                @change="handleChangeYear"
              />
            </ElCol>
          </ElRow>
        </template>
      </div>
    </div>
    <ElButton class="ml-[10px]" type="primary" size="small" @click="resetQuery">
      重置
    </ElButton>
  </div>
</template>

<style scoped lang="scss">
.time-range-warp {
  display: flex;
}

.search-select {
  margin-right: 10px;
}

.text-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
