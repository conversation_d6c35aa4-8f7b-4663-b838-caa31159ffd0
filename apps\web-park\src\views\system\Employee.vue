<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';

import { computed, onMounted, reactive, ref, toRaw } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useAccessStore } from '@vben/stores';

import dayjs from 'dayjs';
import {
  ElButton,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElMessage,
  ElTag,
  ElText,
  ElTooltip,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { DepartmentApi, EmployeeApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

import employeeAddModal from './employeeComponents/employeeAdd.vue';

// 查询参数类型
interface IFormValues {
  id: string;
  created_at: string[];
  department_id: string[];
  enabled: string;
}

// 员工列表行数据类型
interface IRowType {
  id: string;
  login_name: string;
  name: string;
  mobile: string;
  role_id: null | string;
  department_name: null | string;
  department_id: null | string;
  role_name: null | string;
  enabled: number;
  created_at: string;
}

const accessStore = useAccessStore();
// 部门列表
const searchDepartmentOptions = ref<any[]>([]);
// 计算是否禁用添加员工按钮
const isDisabledAddEmployeeBtn = computed(() => {
  return accessStore.accessIamToken === '';
});

/**
 * 注册添加员工弹窗
 * @description 配置connectedComponent
 */
const [EmployeeAddModal, EAddModalRef] = useVbenModal({
  connectedComponent: employeeAddModal,
});

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};

const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

const openParkSelectModal = async (id: string) => {
  const res = await EmployeeApi.getEmployeeParkListApi(Number(id));
  PSModalRef.setState({ title: '分管车场授权' });
  PSModalRef.setData({
    isMultiple: true,
    selectArray: res || [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      // 更新员工的车场授权
      await EmployeeApi.updateEmployeeParkingAuthorityApi({
        employee_id: id,
        park_id: newSelectArray.map((item) => item.park_id) || [],
        park_name: newSelectArray.map((item) => item.park_name) || [],
      });
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'id',
      label: '用户：',
      componentProps: {
        clearable: true,
        placeholder: '用户ID/姓名/手机号',
      },
    },
    {
      component: 'DatePicker',
      defaultValue: null,
      fieldName: 'created_at',
      label: '创建时间：',
      componentProps: {
        clearable: true,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
    {
      component: 'Cascader',
      fieldName: 'department_id',
      label: '所在部门：',
      componentProps: {
        clearable: true,
        props: {
          checkStrictly: true,
          value: 'id',
        },
        showAllLevels: false,
        options: searchDepartmentOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态：',
      componentProps: {
        clearable: true,
        options: [
          { label: '启用', value: '1' },
          { label: '禁用', value: '0' },
        ],
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    // { title: '序号', type: 'seq', minWidth: 60, width: 60 },
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    { field: 'id', title: '用户ID', minWidth: 100, width: 100 },
    { field: 'login_name', title: '万信号', minWidth: 150, width: 150 },
    { field: 'name', title: '姓名', minWidth: 150, width: 150 },
    { field: 'mobile', title: '手机号', minWidth: 150, width: 150 },
    { field: 'role_name', title: '系统角色', minWidth: 200 },
    { field: 'department_name', title: '所在部门', minWidth: 200, width: 200 },
    {
      field: 'enabled',
      title: '启用状态',
      slots: { default: 'enabled' },
      minWidth: 100,
      width: 100,
    },
    {
      field: 'created_at',
      title: '创建时间',
      sortable: true,
      minWidth: 150,
      width: 150,
    },
    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 200,
      width: 200,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: getEmployeeList,
    },
  },
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = () => {
  DepartmentApi.listDepartmentTreeApi()
    .then((res) => {
      searchDepartmentOptions.value = res;
    })
    .catch(() => {
      searchDepartmentOptions.value = [];
    });
};

/**
 * 获取员工列表数据
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 * @returns {Promise<{items: IRowType[], total: number}>} 员工列表和总数
 */
async function getEmployeeList({ page }: any, formValues: IFormValues) {
  // 查询参数格式化
  const params = {
    name_or_mobile: formValues.id,
    enabled: formValues.enabled,
    department_id:
      formValues.department_id?.[formValues.department_id.length - 1],
    created_at_start: formValues.created_at?.[0]
      ? dayjs(formValues.created_at?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    created_at_end: formValues.created_at?.[1]
      ? dayjs(formValues.created_at?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  try {
    const res = await EmployeeApi.pagingEmployeesApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 注册员工管理表格
 * @description 启用表格 配置formOptions与gridOptions
 */
const [EmployeeTable, ETRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 表格按钮 - 添加员工
 * @description 打开添加员工弹窗
 * @param userType 用户类型 非万员工 万员工 默认万员工
 */
const addEmployeeAction = (userType: 'non-wanda' | 'wanda' = 'wanda') => {
  EAddModalRef.setData({
    userType,
    departmentList: toRaw(searchDepartmentOptions),
    query: () => {
      ETRef.query();
    },
    type: 'add',
  });
  EAddModalRef.open();
};

/**
 * 表格按钮 - 批量删除
 * @description 获取勾选的员工 批量删除
 */
const batDeleteAction = () => {
  const selectedRows = ETRef.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    return ElMessage.warning('请先选择要删除的员工');
  }
  // 获取勾选的员工ID
  const ids = selectedRows.map((row) => Number(row.id));
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '批量删除',
    description: '确定要删除选中的员工吗?',
    onConfirm: async () => {
      await EmployeeApi.deleteEmployeesApi({ id: ids, role_id: [] });
      ETRef.query();
    },
  });
};

/**
 * 列按钮 - 编辑员工
 * @description 对单条员工进行编辑
 */
const editAction = (row: IRowType) => {
  EAddModalRef.setData({
    userType: /^\d+$/.test(row.login_name) ? 'non-wanda' : 'wanda',
    type: 'edit',
    departmentList: toRaw(searchDepartmentOptions),
    query: () => {
      ETRef.query();
    },
    id: row.id,
  });
  EAddModalRef.open();
};

/**
 * 列按钮 - 删除员工
 * @description 对单条员工进行删除
 */
const deleteAction = (row: IRowType) => {
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '删除员工',
    description: '确定要删除该员工吗?',
    onConfirm: async () => {
      await EmployeeApi.deleteEmployeesApi({
        id: [Number(row.id)],
        role_id: [],
      });
      ETRef.query();
    },
  });
};

/**
 * 列按钮 - 禁用员工
 * @description 对单条员工进行禁用
 */
const disableAction = (row: IRowType) => {
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '员工禁用',
    description: '确定要禁用该员工吗？',
    onConfirm: async () => {
      await EmployeeApi.disableEmployeeApi(Number(row.id));
      ETRef.query();
    },
  });
};

/**
 * 列按钮 - 启用员工
 * @description 对单条员工进行启用
 */
const enableAction = (row: IRowType) => {
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '员工启用',
    description: '确定要启用该员工吗？',
    onConfirm: async () => {
      await EmployeeApi.enableEmployeeApi(Number(row.id));
      ETRef.query();
    },
  });
};

/**
 * 列按钮 - 重置密码
 * @description 对单条员工进行重置密码
 */
const resetPasswordAction = (row: IRowType) => {
  // 调用本页面中的 统一的二次确认弹窗
  showConfirmModal({
    title: '重置密码',
    description: '确定要重置该员工的密码吗?',
    onConfirm: async () => {
      await EmployeeApi.resetPasswordApi(Number(row.id));
      ETRef.query();
    },
  });
};

onMounted(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <!-- 车场授权 弹窗 -->
    <ParkSelectModal />
    <!-- 添加员工 -->
    <EmployeeAddModal :is-wanda-employee="!isDisabledAddEmployeeBtn" />
    <!-- 二次确认弹窗 -->
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <!-- 员工管理表格 -->
    <EmployeeTable>
      <template #toolbar-actions>
        <ElButton
          v-if="!isDisabledAddEmployeeBtn"
          type="primary"
          @click="addEmployeeAction()"
        >
          添加员工
        </ElButton>
        <ElTooltip v-else content="非万人员无法添加万达员工(人员)">
          <ElButton disabled type="primary">添加员工</ElButton>
        </ElTooltip>
        <ElButton type="primary" @click="addEmployeeAction('non-wanda')">
          添加非万员工
        </ElButton>
        <ElButton type="danger" @click="batDeleteAction">批量删除</ElButton>
      </template>
      <template #enabled="{ row }">
        <ElTag :type="row.enabled === 1 ? 'success' : 'danger'">
          {{ row.enabled === 1 ? '已启用' : '已禁用' }}
        </ElTag>
      </template>
      <template #actions="{ row }">
        <div class="flex items-center justify-center">
          <ElButton link type="primary" @click="editAction(row)">编辑</ElButton>
          <ElButton
            v-if="row.enabled === 0"
            link
            type="danger"
            @click="deleteAction(row)"
          >
            删除
          </ElButton>
          <ElButton
            v-if="row.enabled === 1"
            link
            type="danger"
            @click="disableAction(row)"
          >
            禁用
          </ElButton>
          <ElButton v-else link type="primary" @click="enableAction(row)">
            启用
          </ElButton>
          <ElDropdown class="ml-3" placement="bottom-end">
            <ElText type="primary">更多</ElText>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem @click="resetPasswordAction(row)">
                  重置密码
                </ElDropdownItem>
                <ElDropdownItem @click="openParkSelectModal(row.id)">
                  分管车场授权
                </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
        </div>
      </template>
    </EmployeeTable>
  </Page>
</template>
