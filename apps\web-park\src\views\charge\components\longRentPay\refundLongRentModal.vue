<script lang="ts" setup>
import type { NSParkFee } from '#/api/park/fee';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElDatePicker, ElMessage } from 'element-plus';

import { useVbenForm, z } from '#/adapter/form';
import { CommonModule, ParkFeeApi } from '#/api';

defineOptions({
  name: 'RefundLongRentModal', // 长租缴费-申请退款弹窗
});

// const time = ref([]);
const curRecordRow = ref<NSParkFee.IPagingRentSpaceRecordsRow>();
const payWayOptions = ref<CommonModule.IEnumItem[]>([]);
const [RefundModalForm, RMFRef] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    colon: true,
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false,
  layout: 'horizontal',
  schema: [
    {
      component: 'DateTimePicker',
      fieldName: 'time',
      label: '长租截止日期',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'refund_money',
      label: '退款金额',
      rules: z
        .string()
        .refine((value) => value, {
          message: '请输入退款金额',
        })
        .refine((value) => Number(value) <= curRecordRow.value!.payed_money, {
          message: '退款金额不能大于产品金额',
        }),
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: payWayOptions,
      },
      fieldName: 'refund_channel',
      label: '退款渠道',
      rules: 'selectRequired',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'refund_account',
      label: '退款账号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        type: 'textarea',
      },
      fieldName: 'refund_reason',
      label: '退款原因',
      rules: 'required',
    },
  ],
  wrapperClass: 'grid-cols-1',
});

/**
 * 弹窗配置
 */
const [RefundModal, refundModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = refundModalApi.getData<Record<string, any>>();
      curRecordRow.value = data.row;
      payWayOptions.value = data.options;
    } else {
      RMFRef.resetForm();
    }
  },
  onConfirm: () => {
    RMFRef.validateAndSubmitForm().then(async (res) => {
      if (!res) return;
      const {
        id,
        member_name,
        member_mobile,
        park_id,
        payed_money,
        valid_end_time,
        valid_start_time,
      } = curRecordRow.value!;
      const params = {
        ...res,
        refund_money: Number(res.refund_money),
        id,
        member_name,
        mobile: member_mobile,
        park_id,
        payed_money,
        valid_end_time,
        valid_start_time,
      };
      await ParkFeeApi.rentSpaceRecordsRefundApplyApi(params);
      ElMessage.success('申请成功');
      const shareData = refundModalApi.getData<Record<string, any>>();
      if (shareData.confirmFn) {
        shareData.confirmFn();
      }
      refundModalApi.close();
    });
  },
});

/**
 * 禁用日期方法
 */
const disabledDate = (date: Date) => {
  const endTime = curRecordRow.value!.valid_end_time;
  const condition1 = date.getTime() > dayjs(endTime).valueOf();
  const condition2 = date.getTime() < dayjs().valueOf();
  return condition1 || condition2;
};
</script>

<template>
  <!-- 申请退款弹窗 -->
  <RefundModal :close-on-click-modal="false" class="w-[650px]" title="申请退款">
    <RefundModalForm>
      <template #time="slotProps">
        <ElDatePicker
          v-bind="slotProps"
          :disabled-date="disabledDate"
          format="YYYY-MM-DD HH:mm:ss"
          placeholder="长租截止日期"
          style="width: 100%"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </template>
    </RefundModalForm>
  </RefundModal>
</template>
