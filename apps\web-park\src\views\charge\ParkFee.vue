<script lang="ts" setup>
import type { TagProps } from 'element-plus';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSParkFee } from '#/api/park/fee';
import type { NSParkRegion } from '#/api/park/region';

import { onBeforeMount, onMounted, ref, toRaw } from 'vue';

import { Page, useVbenModal, VbenCountToAnimator } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElButton, ElMessage, ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  CommonApi,
  CommonModule,
  ParkFeeApi,
  ParkGatewayApi,
  ParkRegionApi,
} from '#/api';
import ParkImage from '#/components/park-image.vue';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

import RefundParkFeeModal from './components/parkFee/refundParkFeeModal.vue';

defineOptions({
  name: 'ParkFee', // 停车缴费列表
});

interface IFormValues {
  car_types?: number[];
  in_time?: string[];
  out_time?: string[];
  order_states?: number[];
  out_start_time?: string;
  out_end_time?: string;
  park_name: string;
  park_region_id: string;
  pay_channels?: number[];
  pay_methods?: number[];
  pay_type?: number[];
  refund_states?: number[];
}

type Item = { label: number; type: TagProps['type'] };

/**
 * 业务变量
 */
const carTypeDescMap = ref<Array<Item>>([
  { type: 'danger', label: 0 },
  { type: 'primary', label: 1 },
  { type: 'warning', label: 2 },
  { type: 'success', label: 3 },
  { type: 'info', label: 4 },
]);
const carTypeOptions = ref<CommonModule.IEnumItem[]>([]);
const stopCarTypeOptions = ref<CommonModule.IEnumItem[]>([]);
const parkRegionOptions = ref<NSParkRegion.IListParkRegionItem[]>([]);
const gatewayOptions = ref<CommonModule.IEnumItem[]>([]);
const orderStatesOptions = ref<CommonModule.IEnumItem[]>([]);
const payMethodsOptions = ref<CommonModule.IEnumItem[]>([]);
const payChannelsOptions = ref<CommonModule.IEnumItem[]>([]);
const refundStatesOptions = ref<CommonModule.IEnumItem[]>([]);
const payWayOptions = ref<CommonModule.IEnumItem[]>([]);
const payCountList = ref([
  { label: '应缴金额', value: 0 },
  { label: '优惠金额', value: 0 },
  { label: '实缴金额', value: 0 },
]);
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);
const chooseDay = ref(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

const [RPFModal, rpfModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: RefundParkFeeModal,
});

const getListParkGateway = async () => {
  // eslint-disable-next-line no-use-before-define
  PFRef.formApi.getValues().then(async (res) => {
    const parkRegionId = toRaw(res).park_region_id;
    gatewayOptions.value =
      await ParkGatewayApi.getListParkGatewayApi(parkRegionId);
  });
};

/**
 * 禁用日期方法
 */
const disabledDate = (
  date: Date | dayjs.Dayjs | null | number | string | undefined,
) => {
  if (!chooseDay.value) {
    return false;
  }
  // 超过31天后禁用
  const after31Days = dayjs(date).isAfter(
    dayjs(chooseDay.value).add(31, 'day'),
  );
  // 超过31天后禁用
  const before31Days = dayjs(date).isBefore(
    dayjs(chooseDay.value).subtract(31, 'day'),
  );
  return after31Days || before31Days;
};

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      if (!selectParkCheck.value) {
        // eslint-disable-next-line no-use-before-define
        PFRef.formApi.setFieldValue('park_id', '');
        parkRegionOptions.value = [];
        // eslint-disable-next-line no-use-before-define
        PFRef.formApi.setFieldValue('park_region_id', '');
        PSModalRef.close();
        return;
      }
      // eslint-disable-next-line no-use-before-define
      PFRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      parkRegionOptions.value = await ParkRegionApi.getListParkRegionApi(
        selectParkCheck.value?.park_id,
      );
      // eslint-disable-next-line no-use-before-define
      PFRef.formApi.setFieldValue(
        'park_region_id',
        parkRegionOptions.value[0]?.value,
      );
      getListParkGateway();
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  PFRef.formApi.resetForm();
  selectParkCheck.value = null;
  parkRegionOptions.value = [];
  gatewayOptions.value = [];
  chooseDay.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  PFRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入车牌号',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'order_no',
      label: '订单号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入订单号',
      },
    },
    {
      component: 'Select',
      defaultValue: [1, 2],
      fieldName: 'order_states',
      label: '订单状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: orderStatesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'park_region_id',
      label: '子场名称：',
      componentProps: {
        multiple: false,
        clearable: false,
        options: parkRegionOptions,
        onChange: () => {
          getListParkGateway();
        },
      },
    },
    {
      component: 'Select',
      fieldName: 'in_gateway_id',
      label: '入场通道：',
      componentProps: {
        multiple: false,
        clearable: true,
        options: gatewayOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'out_gateway_id',
      label: '出场通道：',
      componentProps: {
        multiple: false,
        clearable: true,
        options: gatewayOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'car_types',
      label: '车辆类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: carTypeOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'stop_car_type',
      label: '停车类型：',
      componentProps: {
        multiple: false,
        clearable: true,
        options: stopCarTypeOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'pay_methods',
      label: '支付方式：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: payMethodsOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'pay_channels',
      label: '支付渠道：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: payChannelsOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'refund_states',
      label: '退款状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: refundStatesOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: null,
      fieldName: 'in_time',
      label: '入场日期：',
      componentProps: {
        clearable: true,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '入场开始日期',
        endPlaceholder: '入场结束日期',
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [
        `${dayjs().startOf('date').format('YYYY-MM-DD')} 00:00:00`,
        `${dayjs().endOf('date').format('YYYY-MM-DD')} 23:59:59`,
      ],
      fieldName: 'out_time',
      label: '出场日期：',
      componentProps: {
        clearable: false,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '出场开始日期',
        endPlaceholder: '出场结束日期',
        disabledDate,
        onCalendarChange: (event: any) => {
          chooseDay.value = event[0];
        },
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
  // 自定义重置表单方法
  handleReset: onReset,
};

/**
 * 表格配置
 * @description停车缴费管理列表
 */
const gridOptions: VxeGridProps<NSParkFee.IPagingParkPayRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    { field: 'order_no', title: '订单号', minWidth: 200 },
    { field: 'park_name', title: '停车场名称', minWidth: 100 },
    { field: 'park_region_name', title: '子场名称', minWidth: 100, width: 100 },
    { field: 'in_time', title: '入场时间', sortable: true, minWidth: 200 },
    { field: 'in_gateway_name', title: '入场通道', minWidth: 100 },
    {
      field: 'in_car_photo_url',
      title: '入场图片',
      slots: { default: 'in_car_photo_url' },
      minWidth: 100,
    },
    {
      field: 'out_car_photo_url',
      title: '出场图片',
      slots: { default: 'out_car_photo_url' },
      minWidth: 100,
    },
    { field: 'to_time', title: '出场时间', sortable: true, minWidth: 200 },
    { field: 'out_gateway_name', title: '出场通道', minWidth: 100 },
    { field: 'time', title: '停车时长', sortable: true, minWidth: 100 },
    {
      field: 'car_type_desc',
      title: '车辆类型',
      minWidth: 120,
      width: 120,
      slots: { default: 'car_type_desc' },
    },
    { field: 'stop_car_type_desc', title: '停车类型', minWidth: 100 },
    { field: 'plate_no', title: '车牌号', minWidth: 100 },
    { field: 'order_state_desc', title: '订单状态', minWidth: 100 },
    { field: 'pay_method_desc', title: '支付方式', minWidth: 100 },
    { field: 'pay_channel_desc', title: '支付渠道', minWidth: 100 },
    {
      field: 'should_pay_money',
      title: '应交金额',
      sortable: true,
      minWidth: 100,
    },
    { field: 'debate_money', title: '优惠金额', sortable: true, minWidth: 100 },
    { field: 'order_money', title: '实缴金额', sortable: true, minWidth: 100 },
    { field: 'refund_state_desc', title: '退款状态', minWidth: 100 },
    { field: 'charge_name', title: '收费员', minWidth: 100 },
    { field: 'invoice_state_desc', title: '发票状态', minWidth: 100 },
    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
      width: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: getPagingParkPayRecords,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [ParkFeeTable, PFRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 *  获取停车缴费金额数目信息
 */
const getCountParkPayRecord = async (
  { page }: any,
  formValues: IFormValues,
) => {
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    out_start_time: formValues!.out_time?.[0]
      ? dayjs(formValues!.in_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    out_end_time: formValues!.out_time?.[1]
      ? dayjs(formValues!.in_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_start_time: formValues!.in_time?.[0]
      ? dayjs(formValues!.in_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_end_time: formValues!.in_time?.[1]
      ? dayjs(formValues!.in_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.in_time;
  delete params.out_time;
  try {
    const res = await ParkFeeApi.getCountParkPayRecordApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    const data = [
      res.count_order_money,
      res.count_debate_money,
      res.count_should_pay_money,
    ];
    payCountList.value = payCountList.value.map((item, index) => {
      return {
        ...item,
        value: data[index]!,
      };
    });
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
};

/**
 * 获取停车缴费列表数据
 */
async function getPagingParkPayRecords({ page }: any, formValues: IFormValues) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  getCountParkPayRecord({ page }, formValues!);
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    out_start_time: formValues!.out_time?.[0]
      ? dayjs(formValues!.in_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    out_end_time: formValues!.out_time?.[1]
      ? dayjs(formValues!.in_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_start_time: formValues!.in_time?.[0]
      ? dayjs(formValues!.in_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    in_end_time: formValues!.in_time?.[1]
      ? dayjs(formValues!.in_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.in_time;
  delete params.out_time;
  try {
    const res = await ParkFeeApi.getPagingParkPayRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 获取车型类型标签颜色
 */
const getCarTypeDescColor = (type: number) => {
  return carTypeDescMap.value.find((item) => item.label === type)?.type;
};

/**
 * 是否展示申请退款按钮
 */
const showRefund = (row: NSParkFee.IPagingParkPayRecordsRow) => {
  return (
    row.order_state === 2 &&
    [3, 4].includes(row.refund_state) &&
    row.should_pay_money !== 0 &&
    row.pay_method !== 3
  );
};

/**
 * 点击申请退款按钮
 */
const applyRefund = (row: NSParkFee.IPagingParkPayRecordsRow) => {
  rpfModalApi
    .setData({
      row,
      options: payWayOptions.value,
      confirmFn: () => {
        // eslint-disable-next-line no-use-before-define
        loadData();
      },
    })
    .open();
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSelectOptionsData = async () => {
  // 获取车辆类型,停车类型 + 获取订单状态,支付渠道,支付方式,退款状态
  const params1 = [
    { enum_key: 'carTypeList', enum_value: 'EnumCarType' },
    { enum_key: 'orderStateList', enum_value: 'EnumOrderState' },
    { enum_key: 'inGatewayList', enum_value: 'EnumInGateway' },
    { enum_key: 'outGatewayList', enum_value: 'EnumoutGateway' },
    { enum_key: 'payTypeList', enum_value: 'EnumPayMethod' },
    { enum_key: 'payChannelList', enum_value: 'EnumPayChannel' },
    { enum_key: 'refundStateList', enum_value: 'EnumRefundState' },
    { enum_key: 'stopCarTypeList', enum_value: 'EnumStopCarType' },
  ];
  // 获取退款渠道
  const params2 = [
    { enum_key: 'refundWayList', enum_value: 'EnumRefundChannelType' },
  ];
  try {
    const res = await Promise.all([
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, params1),
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.ORDER, params1),
      CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, params2),
    ]);
    carTypeOptions.value = res[0]!.carTypeList;
    stopCarTypeOptions.value = res[0]!.stopCarTypeList;
    orderStatesOptions.value = res[1]!.orderStateList;
    payMethodsOptions.value = res[1]!.payTypeList;
    payChannelsOptions.value = res[1]!.payChannelList;
    refundStatesOptions.value = res[1]!.refundStateList;
    payWayOptions.value = res[2]!.refundWayList;
  } catch {
    carTypeOptions.value = [];
    stopCarTypeOptions.value = [];
    orderStatesOptions.value = [];
    payMethodsOptions.value = [];
    payChannelsOptions.value = [];
    refundStatesOptions.value = [];
    payWayOptions.value = [];
  }
};

const loadData = () => {
  PFRef.setLoading(true);
  setTimeout(() => {
    PFRef.setLoading(false);
    PFRef.query();
  }, 200);
};

onBeforeMount(() => {
  initSelectOptionsData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    PFRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--停车缴费管理表格 -->
    <ParkFeeTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <div class="flex flex-wrap gap-4 text-center font-bold">
          <div v-for="(item, index) in payCountList" :key="index">
            <span class="pr-1 font-normal">{{ item.label }}:</span>
            <VbenCountToAnimator
              :decimals="2"
              :duration="1000"
              :end-val="item.value"
              :start-val="0"
              color="#F56C6C"
              suffix="元"
            />
          </div>
        </div>
      </template>
      <!-- 自定义-车辆类型列 -->
      <template #car_type_desc="{ row }">
        <ElTag :type="getCarTypeDescColor(row.car_type)">
          {{ row.car_type_desc }}
        </ElTag>
      </template>
      <!-- 自定义-入场图片列 -->
      <template #in_car_photo_url="{ row }">
        <ParkImage :src="row.in_car_photo_url" />
      </template>
      <!-- 自定义-出场图片列 -->
      <template #out_car_photo_url="{ row }">
        <ParkImage :src="row.out_car_photo_url" />
      </template>
      <!-- 自定义-操作项列 -->
      <template #actions="{ row }">
        <ElButton
          v-if="showRefund(row)"
          link
          type="danger"
          @click="applyRefund(row)"
        >
          申请退款
        </ElButton>
      </template>
    </ParkFeeTable>
    <RPFModal />
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
