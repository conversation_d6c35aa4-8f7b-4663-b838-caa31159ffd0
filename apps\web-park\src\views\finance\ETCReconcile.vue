<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSAcc, NSEmployee } from '#/api';

import { onMounted, reactive, ref, toRaw } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { ElButton, ElMessage } from 'element-plus';
import printJS from 'print-js';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccApi, FileApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';
import { saveToFile } from '#/utils';

defineOptions({
  name: 'ETCReconcile', // ETC对账列表
});

interface IFormValues {
  park_id?: string;
  park_name?: string;
  stl_month?: string;
}

/**
 * 业务变量
 */
const userStore = useUserStore();
const summaryData = ref<Array<NSAcc.IParkTempTotalByDayRow>>([]);
let totalInfoData = reactive<NSAcc.IParkTempTotalByDayResult>({});
//   start_time: undefined, // 统计开始时间
//   end_time: undefined, // 统计结束时间
//   need_total_money_sum: undefined, // 应收金额合计
//   etc_tolal_cnt_sum: undefined, // ETC交易成功笔数合计
//   etc_tolal_money_sum: undefined, // ETC交易成功金额合计
//   coupon_tolal_money_sum: undefined, // 优免券抵扣总额合计
//   coupon_tolal_cnt_sum: undefined, // 优免券使用数量合计
//   etc_need_total_money_sum: undefined, // 对账-应收总金额合计
//   etc_real_total_money_sum: undefined, // 对账-实收总金额合计
//   etc_total_cnt_sum: undefined, // 对账-交易成功笔数合计
//   etc_refund_total_cnt_sum: undefined, // 对账-退款总比数合计
//   etc_refund_total_money_sum: undefined, // 对账-退款总金额合计
//   etc_jf_refund_total_cnt_sum: undefined, // 对账-拒付总比数合计
// });

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      queryFormApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onSubmit = (values: Record<string, any>) => {
  // eslint-disable-next-line no-use-before-define
  getParkTempTotalByDay(cloneDeep(values));
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  queryFormApi.resetForm();
  selectParkCheck.value = null;
  // eslint-disable-next-line no-use-before-define
  ETCRSRef.setGridOptions({
    data: [],
  });
  // eslint-disable-next-line no-use-before-define
  ETCRTRef.setGridOptions({
    data: [],
  });
  // eslint-disable-next-line no-use-before-define
  ETCRRRef.setGridOptions({
    data: [],
  });
};

/**
 * 搜索表单配置
 */
const [QueryForm, queryFormApi] = useVbenForm({
  // 默认展开
  collapsed: false,
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    formItemClass: 'pb-2',
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 水平布局
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [],
      fieldName: 'stl_month',
      label: '月份：',
      componentProps: {
        clearable: true,
        type: 'month',
        'value-format': 'YYYY-MM',
        style: {
          width: 'auto',
        },
        placeholder: '请选择月份',
      },
    },
  ],
  // 是否可展开
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  submitButtonOptions: {
    content: '搜索',
  },
  actionWrapperClass: 'pb-2',
  wrapperClass:
    'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:flex flex-row items-center',
});

const commonGridOptions = {
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  pagerConfig: {
    enabled: false,
  },
};

/**
 * 表格配置
 * @descriptionETC对账管理列表
 */
const summaryGridOptions: VxeGridProps<
  NSAcc.IParkTempTotalByDayResult['day_infos']
> = {
  ...commonGridOptions,
  // 表格高度 自动
  height: '100%',
  // 正常配置列
  columns: [
    {
      field: 'stl_date',
      minWidth: 150,
      slots: { header: 'date_header' },
      sortable: true,
    },
    { field: 'park_name', title: '停车场名称', minWidth: 150 },
    {
      field: 'need_total_money',
      title: '应收金额(元)',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'etc_tolal_cnt',
      title: 'ETC交易成功笔数',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'etc_tolal_money',
      title: 'ETC交易成功(元)',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'coupon_tolal_money',
      title: '优免券抵扣总额(元)',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'coupon_tolal_cnt',
      title: '优免券使用数量',
      sortable: true,
      minWidth: 150,
    },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 100,
    },
  ],
};

const transactionGridOptions: VxeGridProps<
  NSAcc.IEtcAccInfosResult['trades_info']
> = {
  ...commonGridOptions,
  minHeight: 80,
  // 正常配置列
  columns: [
    { field: 'stl_date', minWidth: 200, slots: { header: 'date_header' } },
    { field: 'park_name', title: '停车场名称', minWidth: 150 },
    {
      field: 'etc_need_total_money',
      title: '应收总金额(元)',
      sortable: true,
      minWidth: 100,
    },
    {
      field: 'etc_real_total_money',
      title: '实收总金额(元)',
      sortable: true,
      minWidth: 100,
    },
    {
      field: 'etc_total_cnt',
      title: '交易成功笔数',
      sortable: true,
      minWidth: 100,
    },
  ],
};

const refundGridOptions: VxeGridProps<NSAcc.IEtcAccInfosResult['refund_info']> =
  {
    ...commonGridOptions,
    minHeight: 80,
    // 正常配置列
    columns: [
      { field: 'stl_date', minWidth: 200, slots: { header: 'date_header' } },
      { field: 'park_name', title: '停车场名称', minWidth: 150 },
      {
        field: 'etc_refund_total_cnt',
        title: '退款总比数',
        sortable: true,
        minWidth: 100,
      },
      {
        field: 'etc_refund_total_money',
        title: '退款总金额(元)',
        sortable: true,
        minWidth: 100,
      },
      {
        field: 'etc_jf_refund_total_cnt',
        title: '拒付总比数',
        sortable: true,
        minWidth: 100,
      },
      {
        field: 'etc_jf_refund_total_money',
        title: '拒付总金额(元)',
        sortable: true,
        minWidth: 100,
      },
    ],
  };

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  async cellClick({ row }) {
    if (row.stl_date === '月合计') return;
    const params = {
      park_id: selectParkCheck.value?.park_id!,
      stl_date: row.stl_date,
    };
    // eslint-disable-next-line no-use-before-define
    ETCRTRef.setLoading(true);
    // eslint-disable-next-line no-use-before-define
    ETCRRRef.setLoading(true);
    try {
      const res = await AccApi.getEtcAccInfosApi(params);
      res.trades_info.stl_date = res.trades_info.stl_dat;
      res.refund_info.stl_date = res.refund_info.stl_dat;
      // eslint-disable-next-line no-use-before-define
      setRecordsData(res);
    } catch (error) {
      console.error(error);
    } finally {
      // eslint-disable-next-line no-use-before-define
      ETCRTRef.setLoading(false);
      // eslint-disable-next-line no-use-before-define
      ETCRRRef.setLoading(false);
    }
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [ETCRSummarizeTable, ETCRSRef] = useVbenVxeGrid({
  gridOptions: summaryGridOptions,
  gridEvents,
});

const [ETCRTransactionTable, ETCRTRef] = useVbenVxeGrid({
  gridOptions: transactionGridOptions,
});

const [ETCRRefundTable, ETCRRRef] = useVbenVxeGrid({
  gridOptions: refundGridOptions,
});

const setRecordsData = (originData?: NSAcc.IEtcAccInfosResult) => {
  const transactionData = [
    {
      stl_date: '月合计',
      park_name: '-',
      etc_need_total_money: totalInfoData.etc_need_total_money_sum,
      etc_real_total_money: totalInfoData.etc_real_total_money_sum,
      etc_total_cnt: totalInfoData.etc_total_cnt_sum,
    },
  ];
  const refundData = [
    {
      stl_date: '月合计',
      park_name: '-',
      etc_refund_total_cnt: totalInfoData.etc_refund_total_cnt_sum,
      etc_refund_total_money: totalInfoData.etc_refund_total_money_sum,
      etc_jf_refund_total_cnt: totalInfoData.etc_jf_refund_total_cnt_sum,
      etc_jf_refund_total_money: totalInfoData.etc_jf_refund_total_money_sum,
    },
  ];
  if (originData?.trades_info) {
    transactionData.push(originData.trades_info!);
  }
  if (originData?.refund_info) {
    refundData.push(originData?.refund_info!);
  }
  ETCRTRef.setGridOptions({
    data: transactionData,
  });
  ETCRRRef.setGridOptions({
    data: refundData,
  });
};

/**
 * 获取ETC对账列表数据
 */
const getParkTempTotalByDay = async (formValues: IFormValues) => {
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
  };
  ETCRSRef.setLoading(true);
  try {
    const res = await AccApi.getParkTempTotalByDayApi(params);
    totalInfoData = res;
    if (!res.day_infos || res.day_infos.length === 0) return;
    const data = res.day_infos.map((item) => {
      return {
        ...item,
        stl_date: item.stl_dat,
      };
    });
    const firstData = [
      {
        stl_date: '月合计',
        park_name: '-',
        need_total_money: totalInfoData.need_total_money_sum,
        etc_tolal_cnt: totalInfoData.etc_tolal_cnt_sum,
        etc_tolal_money: totalInfoData.etc_tolal_money_sum,
        coupon_tolal_money: totalInfoData.coupon_tolal_money_sum,
        coupon_tolal_cnt: totalInfoData.coupon_tolal_cnt_sum,
      },
    ];
    summaryData.value = [...firstData, ...data];
    ETCRSRef.setGridOptions({
      data: summaryData.value,
    });
    setRecordsData();
  } catch (error) {
    console.error(error);
  } finally {
    ETCRSRef.setLoading(false);
  }
};

/**
 * 下载ETC数据对账单
 */
const downloadSettleFile = async (row: NSAcc.IParkTempTotalByDayRow) => {
  const params = {
    park_id: selectParkCheck.value?.park_id!,
    stl_date: row.stl_date,
  };
  row.exportLoading = true;
  try {
    const exportRes = await AccApi.exportAccFileApi(params);
    if (exportRes) {
      const downloadRes = await FileApi.downloadFileApi(exportRes);
      const fileName = downloadRes.disposition
        .split(';')[2]
        .split('filename=')[1]
        .replaceAll('"', '');
      saveToFile(downloadRes.data, decodeURIComponent(fileName));
    }
  } catch (error) {
    console.error(error);
  } finally {
    row.exportLoading = false;
  }
};

/**
 * 打印易宝交易汇总数据
 */
const printData = () => {
  printJS({
    printable: cloneDeep(summaryData.value),
    properties: [
      { field: 'stl_date', displayName: '日期' },
      { field: 'park_name', displayName: '停车场名称' },
      { field: 'need_total_money', displayName: '应收金额(元)' },
      { field: 'etc_tolal_cnt', displayName: 'ETC交易成功笔数' },
      { field: 'etc_tolal_money', displayName: 'ETC交易成功(元)' },
      { field: 'coupon_tolal_money', displayName: '优免券抵扣总额(元)' },
      { field: 'coupon_tolal_cnt', displayName: '优免券使用数量' },
    ],
    type: 'json',
    header: '停车场临停汇总（ETC数据）',
    gridHeaderStyle:
      ';padding: 10px 16px;font-size: 14px;font-weight: 400;border: 0.5px solid #333333;',
    gridStyle:
      'padding: 10px 16px;border: 0.5px solid #333333;text-align: center;',
  });
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    queryFormApi.setFieldValue('park_id', user.park_names[0]);
    queryFormApi.getValues().then(async (res) => {
      if (toRaw(res).month) {
        getParkTempTotalByDay({ stl_month: toRaw(res).month });
      }
    });
  }
});
</script>
<template>
  <!--ETC对账管理表格 -->
  <Page>
    <div>
      <QueryForm class="bg-white p-2 pt-5" />
    </div>
    <div class="flex h-[calc(100vh-14.5rem)] flex-row pt-3">
      <div class="h-full w-[60%] bg-white">
        <ETCRSummarizeTable>
          <template #date_header>
            <div class="first-col-main">
              <div class="first-col-top">类别</div>
              <div class="first-col-bottom">日期</div>
            </div>
          </template>
          <!-- 表格顶部左侧按钮栏 -->
          <template #toolbar-actions>
            <span class="text-[1rem]">
              停车场临停汇总（ETC数据）
              <ElButton
                type="primary"
                :disabled="summaryData.length === 0"
                @click="printData"
              >
                打印当前页
              </ElButton>
            </span>
          </template>
          <!-- 自定义-操作项列 -->
          <template #actions="{ row }">
            <span v-if="row.stl_date === '月合计'">-</span>
            <ElButton
              v-else
              link
              type="primary"
              :loading="row.exportLoading"
              @click.stop="downloadSettleFile(row)"
            >
              导出明细
            </ElButton>
          </template>
        </ETCRSummarizeTable>
      </div>
      <div class="ml-3 h-full w-[40%] bg-white">
        <div>
          <ETCRTransactionTable table-title="ETC交易汇总（惠达平台数据）">
            <template #date_header>
              <div class="first-col">
                <div class="first-col-top">类别</div>
                <div class="first-col-bottom">日期</div>
              </div>
            </template>
          </ETCRTransactionTable>
        </div>
        <div>
          <ETCRRefundTable table-title="ETC退款/拒付汇总">
            <template #date_header>
              <div class="first-col">
                <div class="first-col-top">类别</div>
                <div class="first-col-bottom">日期</div>
              </div>
            </template>
          </ETCRRefundTable>
        </div>
        <div class="p-2 text-[14px]">
          <div class="flex">
            <span class="icon-[ep--bell-filled] mr-1"></span>操作提示
          </div>
          <div class="mb-[14px] indent-4">
            1.选择车场和月份，点击查询，停车场临停汇总显示选择月份的信息。
          </div>
          <div class="mb-[14px] indent-4">
            2.若选择的是当前月，停车场临停汇总显示当前月当日之前的数据，如当日是11号，则显示1至10号的10条数据和1条汇总数据；
          </div>
          <div class="mb-[14px] indent-4">
            若选择的是非前月，停车场临停汇总显示选择月整月的数据，即月初至月末的整月逐日数据和1条汇总数据。
          </div>
          <div class="mb-[14px] indent-4">
            3.当鼠标在停车场临停汇总浏览未点击某日数据时，ETC交易、退款、拒付表单首行显示汇总合计信息
            ；
          </div>
          <div class="indent-4">
            当鼠标在停车场临停汇总浏览点击某日数据时，ETC交易、退款、拒付表单在首行汇总合计信息基础上，增加1行对应日的信息。
          </div>
        </div>
      </div>
    </div>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>

<style lang="scss" scoped>
.first-col-main {
  position: relative;
  min-width: 150px;
  height: 40px;

  &::before {
    position: absolute;
    top: 20px;
    left: 25px;
    width: 100px;
    height: 1px;
    content: '';
    background: rgb(195 195 195 / 50%);
    transform: rotate(25deg);
  }

  .first-col-top {
    position: absolute;
    top: 2px;
    right: 20px;
  }

  .first-col-bottom {
    position: absolute;
    bottom: 2px;
    left: 20px;
  }
}

.first-col {
  position: relative;
  min-width: 200px;
  height: 40px;

  &::before {
    position: absolute;
    top: 20px;
    left: 50px;
    width: 100px;
    height: 1px;
    content: '';
    background: rgb(195 195 195 / 50%);
    transform: rotate(25deg);
  }

  .first-col-top {
    position: absolute;
    top: 2px;
    right: 20px;
  }

  .first-col-bottom {
    position: absolute;
    bottom: 2px;
    left: 20px;
  }
}
</style>
