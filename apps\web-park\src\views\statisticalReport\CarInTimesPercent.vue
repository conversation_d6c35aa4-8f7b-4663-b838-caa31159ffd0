<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee, NSStatisticalReport } from '#/api';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CarInTimesPercentApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'CarInTimesPercent', // 车辆进场次数占比报表列表
});

interface IFormValues {
  month: string | undefined;
}

/**
 * 业务变量
 */
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      EPPRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  EPPRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  loadData();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: undefined,
      fieldName: 'organization_ids',
      label: '组织架构：',
      componentProps: {
        clearable: true,
        placeholder: '请输入组织架构',
      },
    },
    {
      component: 'DatePicker',
      defaultValue: undefined,
      fieldName: 'month',
      label: '选择月份：',
      componentProps: {
        clearable: true,
        type: 'month',
        style: {
          width: 'auto',
        },
        placeholder: '请选择月份',
        format: 'YYYY-MM',
        valueFormat: 'YYYY-MM',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description车辆进场次数占比报表管理列表
 */
const gridOptions: VxeGridProps<NSStatisticalReport.IListSpaceUseRatiosRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    enabled: false,
  },
  // 正常配置列
  columns: [
    {
      field: 'statistics_date',
      title: '日期',
      showOverflow: false,
      width: 180,
    },
    {
      field: 'park_name',
      title: '车场名称',
      minWidth: 100,
    },
    {
      field: 'organizational_structure',
      title: '组织架构',
      minWidth: 100,
    },
    {
      title: '省市区',
      minWidth: 220,
      slots: { default: 'province_city_district' },
    },
    {
      field: 'total_car_in',
      title: '单月进场车牌数量',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'total_car_in_one',
      title: '单月进场1次车牌数量',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'car_in_one_proportion',
      title: '单月进场1次车牌占比',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'total_car_in_two',
      title: '单月进场2次车牌数量',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'car_in_two_proportion',
      title: '单月进场2次车牌占比',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'total_car_in_three',
      title: '单月进场3次及以上车牌数量',
      sortable: true,
      minWidth: 180,
    },
    {
      field: 'car_in_three_proportion',
      title: '单月进场3次及以上车牌占比',
      sortable: true,
      minWidth: 180,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getListSpaceUseRatiosApi,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [CarInTimesPercentTable, EPPRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取车辆进场次数占比报表列表数据
 */
async function getListSpaceUseRatiosApi(_: any, formValues: IFormValues) {
  let params;
  if (isReset.value) {
    params = {
      park_id: undefined,
      park_name: undefined,
      organization_ids: undefined,
      department_name: undefined,
      month: undefined,
    };
    isReset.value = false;
  } else {
    params = {
      ...formValues,
      park_id: selectParkCheck.value?.park_id,
      park_name: selectParkCheck.value?.park_name,
    };
  }
  try {
    const res = await CarInTimesPercentApi.getListCarInNumberProportionApi({
      organization_ids: undefined,
      department_name: undefined,
      ...params,
    });
    return {
      items: res,
    };
  } catch {
    return {
      items: [],
    };
  }
}

const loadData = () => {
  EPPRef.setLoading(true);
  setTimeout(() => {
    EPPRef.setLoading(false);
    EPPRef.query();
  }, 200);
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--车辆进场次数占比报表管理表格 -->
    <CarInTimesPercentTable table-title="">
      <!-- 自定义-省市区列 -->
      <template #province_city_district="{ row }">
        {{ row?.province_name || '-' }} / {{ row?.city_name || '-' }} /
        {{ row?.district_name || '-' }}
      </template>
    </CarInTimesPercentTable>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
