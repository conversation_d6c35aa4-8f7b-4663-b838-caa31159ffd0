<script name="Menu" setup lang="ts">
import { onBeforeMount, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import MessagePushCard from './messagePush/MessagePushCard.vue';
import MessagePushTab from './messagePush/MessagePushTab.vue';

const route = useRoute();

const radio = ref<any>(1);
const routeKey = route.query.key;
const radioChange = (value: any) => {
  radio.value = value;
};
onBeforeMount(() => {
  if (routeKey) {
    radio.value = routeKey;
  }
});
</script>
<template>
  <Page class="flex h-full flex-col">
    <MessagePushTab :radio="radio" @radio-change="radioChange" />
    <MessagePushCard :radio-type="radio" style="height: 87.6%" />
  </Page>
</template>

<style lang="scss" scoped></style>
