[{"id": "10100", "name": "coupon", "title": "优免券管理", "icon": "<PERSON>mp<PERSON>", "type": "menu", "children": [{"id": "10101", "title": "优免劵列表", "name": "couponList", "path": "/coupon/couponList", "icon": null, "type": "page", "visible": 1, "component": "/src/views/coupon/CouponList.vue"}, {"id": "10102", "title": "人工发放", "name": "grant", "path": "/coupon/grant", "icon": null, "type": "page", "visible": 1, "component": "/src/views/coupon/Grant.vue"}, {"id": "10103", "title": "核销记录", "name": "cancel", "path": "/coupon/cancel", "icon": null, "type": "page", "visible": 1, "component": "/src/views/coupon/Cancel.vue"}]}, {"id": "10200", "name": "owner", "title": "车主自助领券", "icon": "Setting", "type": "menu", "children": [{"id": "10201", "title": "领券码管理", "name": "couponAdmin", "path": "/owner/couponAdmin", "icon": null, "type": "page", "visible": 1, "component": "/src/views/owner/CouponAdmin.vue"}, {"id": "10202", "title": "领券审核", "name": "couponAudit", "path": "/owner/couponAudit", "icon": null, "type": "page", "visible": 1, "component": "/src/views/owner/CouponAudit.vue"}]}]