<script name="AppAdminTable" setup lang="ts">
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onActivated, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import dayjs from 'dayjs';
// import { activeRouteTab } from '@/utils/tabKit';
import { ElButton, ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AppApi } from '#/api';
import { router } from '#/router';

import AppFindBack from './AppFindBack.vue';

const tableLoading = ref(false);
const data = reactive<any>({
  selectRows: [],
  queryParams: {
    page: 1,
    limit: 30,
  },
});
const parkDialogVisible = ref(false);
const appId = ref('');

interface IRowType {
  id: string;
  login_name: string;
  name: string;
  mobile: string;
  role_id: null | string;
  department_name: null | string;
  department_id: null | string;
  role_name: null | string;
  enabled: number;
  created_at: string;
  updator: null | string;
}
/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'name',
      label: '应用名称：',
      componentProps: {
        clearable: true,
        placeholder: '应用名称',
      },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'dateType',
    //   label: '时间类型：',
    //   componentProps: {
    //     clearable: true,
    //     options: [{ label: '更新时间', value: 1 }],
    //   },
    // },
    {
      component: 'DatePicker',
      defaultValue: null,
      fieldName: 'timeRanger',
      label: '更新时间',
      componentProps: {
        clearable: true,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '开始时间',
        endPlaceholder: '结束时间',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-3',
};

/**
 * 表格配置
 * @description 员工管理列表
 */
const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    // { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'name',
      title: '应用名称',
      minWidth: 300,
      width: 300,
      slots: { default: 'name' },
    },
    { field: 'app_type_desc', title: '应用标识', minWidth: 100 },
    {
      field: 'current_version',
      title: '当前版本',
      minWidth: 120,
    },
    {
      field: 'build_version',
      title: '构建的版本',
      minWidth: 150,
      width: 150,
    },
    { field: 'app_key', title: 'app_key', minWidth: 150 },
    { field: 'updator', title: '更新人', minWidth: 100 },
    {
      field: 'updated_at',
      title: '更新时间',
      minWidth: 200,
      width: 200,
      sortable: true,
    },

    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 160,
      width: 160,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const params = {
          name: formValues.name,
          dateType: 1,
          from_date: formValues.timeRanger
            ? dayjs(formValues.timeRanger[0]).format('YYYY-MM-DD HH:mm:ss')
            : '',
          to_date: formValues.timeRanger
            ? dayjs(formValues.timeRanger[1]).format('YYYY-MM-DD HH:mm:ss')
            : '',
        };
        const { rows, total } = await AppApi.AppApi.pagingAppApi({
          page: page.currentPage,
          limit: page.pageSize,
          ...params,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
};
const [AdminTable, ETRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const getList = (params: { limit: number; page: number }) => {
  tableLoading.value = true;
  params.page === undefined
    ? (params.page = 1)
    : (params.page = data.queryParams.page);
  params.limit === undefined
    ? (params.limit = 30)
    : (params.limit = data.queryParams.limit);
  data.queryParams = params;
};
onActivated(() => {
  // getList(data.queryParams);
  ETRef.query();
});

const setAppCreate = () => {
  // activeRouteTab({
  //   path: '/system/appCreate',
  // });
  router.push({
    path: '/system/appCreate',
  });
};

const handleDetail = (row: {
  app_key: string;
  buildVersion: string;
  current_version: string;
  features: string;
  id: string;
  memo: string;
  name: string;
}) => {
  router.push({
    path: '/system/appDetail',
    query: {
      id: row.id,
      name: row.name,
      currentVersion: row.current_version,
      buildVersion: row.buildVersion,
      app_key: row.app_key,
      features: row.features,
      memo: row.memo,
    },
  });
};

const handleUpdate = (row: { id: any }) => {
  router.push({
    path: '/system/appUpdate',
    query: {
      id: row.id,
    },
  });
};

const handlePublish = (row: {
  app_key: string;
  features: string;
  id: string;
  memo: string;
  name: string;
}) => {
  router.push({
    path: '/system/appPublish',
    query: {
      id: row.id,
      name: row.name,
      app_key: row.app_key,
      features: row.features,
      memo: row.memo,
      active: 1,
    },
  });
};

/**
 * 修改产权方
 */
const appFindBackRef = ref<any>(null);
const [ModalPropertyOwnerUpdate, modalPropertyOwnerUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    appFindBackRef.value.submitTableData();
  },
  onCancel: () => {
    modalPropertyOwnerUpdateApi.close();
  },
});
const renderTableInput = (val: any[]) => {
  const park_id = ref<any>([]);
  const park_name = ref<any>([]);
  val.forEach((item) => {
    park_id.value.push(item.park_id);
    park_name.value.push(item.park_name);
  });
  const form = {
    app_id: appId.value,
    park_id: park_id.value,
    park_name: park_name.value,
  };
  // 分管车场授权
  AppApi.AppApi.parkingAuthorityApi(form).then(() => {
    getList(data.queryParams);
    modalPropertyOwnerUpdateApi.close();
  });
};
const authCharge = (row: any, visible: boolean) => {
  if (visible === false) {
    parkDialogVisible.value = false;
  } else {
    appId.value = row.id;
    // parkDialogVisible.value = true;
    modalPropertyOwnerUpdateApi.open();
    setTimeout(() => {
      appFindBackRef.value.loadSiteList(row.id);
    }, 200);
  }
};
// const handleClose = () => {
//   parkDialogVisible.value = false;
// };
</script>

<template>
  <Page auto-content-height>
    <AdminTable>
      <!-- 自定义应用名称列 -->
      <template #name="{ row }">
        <ElButton link type="primary" @click="handleDetail(row)">
          <span class="">{{ row.name }}</span>
        </ElButton>
      </template>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <ElButton type="primary" @click="setAppCreate()"> 创建应用 </ElButton>
      </template>
      <!-- 自定义-启用状态列 明确且少量的枚举类型使用标签 -->
      <template #enabled="{ row }">
        <ElTag :type="row.enabled === 1 ? 'success' : 'danger'">
          {{ row.enabled === 1 ? '启用' : '禁用' }}
        </ElTag>
      </template>
      <!-- 自定义-操作项列 
        1.一律使用link type="primary" 
        危险操作使用ElPopconfirm二次确认
      -->
      <template #actions="{ row }">
        <div class="flex items-center justify-center">
          <ElButton link size="small" type="primary" @click="handleUpdate(row)">
            编辑
          </ElButton>
          <ElButton
            link
            size="small"
            type="primary"
            @click="handlePublish(row)"
          >
            发布
          </ElButton>
          <ElButton
            link
            size="small"
            type="primary"
            @click="authCharge(row, true)"
          >
            授权
          </ElButton>
        </div>
      </template>
    </AdminTable>
    <!-- 分管车场授权 -->
    <ModalPropertyOwnerUpdate
      :fullscreen-button="false"
      class="w-[1150px]"
      title="分管车场授权"
    >
      <AppFindBack
        :app_id="appId"
        ref="appFindBackRef"
        @auth-charge="authCharge(undefined, false)"
        @render-table-input="renderTableInput"
      />
    </ModalPropertyOwnerUpdate>
  </Page>
</template>
<style lang="scss" scoped></style>
