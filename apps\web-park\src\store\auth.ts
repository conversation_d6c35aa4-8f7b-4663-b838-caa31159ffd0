import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { ElMessage, ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

import {
  getUserInfoByTokenApi,
  loginApi,
  logoutApi,
  RoleApi,
  validateCASTokenApi,
} from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);
  const captchaKey = ref('');

  /**
   * 账号密码+图形验证码 异步校验登录登录
   * Account Password + graphic verification code Asynchronous login Login
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const {
        token: accessToken,
        user_detail: userEntity,
        message,
      } = await loginApi({ ...params, key: captchaKey.value });
      if (accessToken) {
        // 将 iamToken 存储道 accessIamToken
        accessStore.setAccessIamToken('');
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(accessToken);

        userInfo = {
          avatar: '',
          desc: '',
          homePath: '',
          token: accessToken,
          realName: userEntity.name,
          roles: [],
          userId: userEntity.user_id,
          username: userEntity.username,
          userEntity,
        };

        userStore.setUserInfo(userInfo);
        // accessStore.setAccessCodes(accessCodes);

        // 获取用户相关的附属信息如角色列表字典、权限Code等
        await getRoleListByUserId();

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(userInfo.homePath || DEFAULT_HOME_PATH);
        }

        if (userInfo?.realName) {
          ElNotification({
            message: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            title: $t('authentication.loginSuccess'),
            type: 'success',
          });
        }
      } else {
        ElMessage({
          message: message || '登录失败',
          type: 'warning',
        });
      }
    } finally {
      loginLoading.value = false;
    }
    return {
      userInfo,
    };
  }

  /**
   * 跳转CAS登录
   * @param type
   */
  async function iamLogin(type: 'CM' | 'WANDA') {
    const WZT_ORIGIN_URL = import.meta.env.VITE_WZT_ORIGIN_URL;
    const CAS_PID = type === 'CM' ? 'parkingcm' : 'parkingwanda';
    return location.replace(
      `${WZT_ORIGIN_URL}/cas/authorize?pid=${CAS_PID}&redirect_uri=${encodeURIComponent(
        location.href,
      )}`,
    );
  }

  /**
   * 验证IAM过来的Token 并返回用户信息
   * @param iamToken
   */
  async function validateCASToken(iamToken: string) {
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const { token: accessToken, user_detail: userEntity } =
        await validateCASTokenApi(iamToken);
      if (accessToken) {
        // 将 iamToken 存储道 accessIamToken
        accessStore.setAccessIamToken(iamToken);
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(accessToken);

        userInfo = {
          avatar: '',
          desc: '',
          homePath: '',
          token: accessToken,
          realName: userEntity.name,
          roles: [],
          userId: userEntity.user_id,
          username: userEntity.username,
          userEntity,
        };

        userStore.setUserInfo(userInfo);
        // accessStore.setAccessCodes(accessCodes);

        // 获取用户相关的附属信息如角色列表字典、权限Code等
        await getRoleListByUserId();

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          await router.push(userInfo?.homePath || DEFAULT_HOME_PATH);
        }
        accessStore.setIsAccessChecked(true);
        if (userInfo?.realName) {
          ElNotification({
            message: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            title: $t('authentication.loginSuccess'),
            type: 'success',
          });
        }
      }
    } catch (error) {
      console.error('validateCASToken error', error);
    } finally {
      loginLoading.value = false;
    }
  }

  /**
   * 获取用户角色列表字典
   */
  async function getRoleListByUserId() {
    const res = await RoleApi.getRoleListByUserIdApi();
    userStore.setUserRoleList(res || []);
    return res;
  }

  /**
   * 根据Token获取用户信息
   * 每次进入系统(包括刷新页面)都会重新请求 用于保证Token的有效和用户的数据有效
   */
  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    const res = await getUserInfoByTokenApi();
    const { name, user_id, username } = res;
    userInfo = {
      avatar: '',
      desc: '',
      homePath: '',
      token: accessStore.accessToken || '',
      realName: name,
      roles: [],
      userId: user_id,
      username,
      userEntity: res,
    };
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    iamLogin,
    loginLoading,
    logout,
    validateCASToken,
    captchaKey,
    getRoleListByUserId,
  };
});
