<script name="MenuConfigModule" lang="ts" setup>
import type { categoriesEs, dataObjEs, menuTypesEs } from './types';

import { onActivated, reactive, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElCard,
  ElCol,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElRow,
  ElSelect,
  ElSwitch,
  ElText,
} from 'element-plus';

import { CommonApi, CommonModule, MenuApi, PageApi } from '#/api';

const props = defineProps({
  menu: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      name: '',
    }),
  },
});
const emits = defineEmits(['getMenuTree']);
const rootFormRef = ref<any>(null);
const rootUpFormRef = ref<any>(null);
const createChildFormRef = ref<any>(null);
const updateChildFormRef = ref<any>(null);
const createPageFormRef = ref<any>(null);
const updatePageFormRef = ref<any>(null);
const categoriesList = ref<categoriesEs[]>([{ name: '', id: '' }]);
const menuTypes = ref<menuTypesEs[]>([]);

const rootRules = {
  name: [
    {
      required: true,
      message: '请输入菜单名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入菜单编码',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择菜单类型',
      trigger: 'blur',
    },
  ],
  menu_category_id: [
    {
      required: true,
      message: '请选择菜单分类',
      trigger: 'change',
    },
  ],
  weight: [
    {
      required: true,
      message: '请输入排序',
      trigger: 'blur',
      // type: 'number',
    },
  ],
};
const ChildAddRules = {
  name: [
    {
      required: true,
      message: '请输入菜单名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入菜单编码',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择菜单类型',
      trigger: 'blur',
    },
  ],
  weight: [
    {
      required: true,
      message: '请输入排序',
      trigger: 'blur',
      // type: 'number',
    },
  ],
};
const childUpdateRules = {
  name: [
    {
      required: true,
      message: '请输入菜单名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入菜单编码',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择菜单类型',
      trigger: 'blur',
    },
  ],
  weight: [
    {
      required: true,
      message: '请输入排序',
      trigger: 'blur',
      // type: 'number',
    },
  ],
};
const pageRules = {
  name: [
    {
      required: true,
      message: '请输入页面名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入页面编码',
      trigger: 'blur',
    },
  ],
  weight: [
    {
      required: true,
      message: '请输入页面权重',
      trigger: 'blur',
    },
  ],
  url: [
    {
      required: true,
      message: '请输入页面URL',
      trigger: 'blur',
    },
  ],
};
const data = reactive<dataObjEs>({
  menuConfig: {
    id: '',
    name: '',
    code: '',
    type: '',
    type_display: '',
    weight: undefined,
    enabled: undefined,
    url: '',
    icon: '',
    menu_category_id: '',
    parent_menu_id: 0,
    enabled_display: '',
  },
  rootCreateForm: {
    name: '',
    code: '',
    menu_category_id: '',
    parent_menu_id: 0,
    icon: '',
    type: '',
    url: '',
    weight: undefined,
    enabled: undefined,
  },
  rootUpdateForm: {
    id: '',
    name: '',
    code: '',
    menu_category_id: '',
    parent_menu_id: 0,
    icon: '',
    type: '',
    url: '',
    weight: undefined,
    enabled: undefined,
  },
  childCreateForm: {
    name: '',
    code: '',
    menu_category_id: '',
    parent_menu_id: undefined,
    parent_menu_name: '',
    type: '',
    url: '',
    icon: '',
    weight: undefined,
    enabled: undefined,
  },
  childUpdateForm: {
    id: '',
    name: '',
    code: '',
    menu_category_id: '',
    parent_menu_id: undefined,
    parent_menu_name: '',
    icon: '',
    url: '',
    weight: undefined,
    enabled: undefined,
  },
  pageCreateForm: {
    menu_id: '',
    parent_menu_name: '',
    name: '',
    code: '',
    url: '',
    weight: undefined,
    config_info: '',
    memo: '',
  },
  pageUpdateForm: {
    menu_id: '',
    parent_menu_name: '',
    id: '',
    name: '',
    code: '',
    url: '',
    weight: undefined,
    config_info: '',
    memo: '',
  },
});
/**
 * 注册添加弹窗 根菜单
 */
const [ModalGen, modalApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    rootFormRef.value.validate().then(() => {
      MenuApi.createMenuApi(data.rootCreateForm).then(() => {
        rootFormRef.value.resetFields();
        emits('getMenuTree');
        modalApi.close();
      });
    });
  },
  onCancel: () => {
    rootFormRef.value.resetFields();
    modalApi.close();
  },
});

/**
 * 注册添加弹窗 子菜单
 */
const [ModalSon, modalSonApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    createChildFormRef.value.validate().then(() => {
      MenuApi.createMenuApi(data.childCreateForm).then(() => {
        createChildFormRef.value.resetFields();
        emits('getMenuTree');
        modalSonApi.close();
      });
    });
  },
  onCancel: () => {
    createChildFormRef.value.resetFields();
    modalSonApi.close();
  },
});
// 页面初始化查询菜单分类和枚举
const initSelects = () => {
  MenuApi.listMenuCategoriesApi().then((response) => {
    if (response) {
      categoriesList.value = response;
    }
  });
  const param = [
    {
      enum_key: 'menuTypes',
      enum_value: 'EnumMenuType',
    },
  ];
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.SYSTEM, param).then(
    (response) => {
      menuTypes.value = response.menuTypes;
    },
  );
};
onActivated(() => {
  initSelects();
});

watch(
  () => props.menu,
  (newVal) => {
    if (newVal.type === 'menu') {
      // 是菜单
      if (newVal.id === '') {
        data.menuConfig = {
          id: '',
          name: '',
          code: '',
          type: '',
          type_display: '',
          weight: 0,
          enabled: 0,
          url: '',
          icon: '',
          menu_category_id: '',
          parent_menu_id: 0,
        };
      } else {
        // 去掉后台拼接的多余的部分,拿到真正的id
        let menuVal = '';
        if (
          newVal.id.slice(0, 3) === '100' ||
          newVal.id.slice(0, 3) === '200'
        ) {
          menuVal = newVal.id.slice(3);
        }
        const param = {
          id: menuVal,
          menu_category_id: newVal.menu_category_id,
        };
        MenuApi.getMenuByIdApi(param).then((response) => {
          if (response) {
            data.menuConfig = response;
            let i = 0;
            for (i = 0; i < menuTypes.value.length; i++) {
              if (
                menuTypes.value[i]?.value === `${data.menuConfig.type_display}`
              ) {
                data.menuConfig.type_display = menuTypes.value[i]?.key || '';
              }
            }
            data.menuConfig.id = data.menuConfig.id?.slice(3);
          }
        });
      }
    } else if (newVal.type === 'page') {
      // 是页面
      PageApi.getPageByIdApi(newVal.id).then((response) => {
        if (response) {
          data.menuConfig = response;
        }
      });
    }
  },
);
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};
// 新建根菜单
const addRootMenu = () => {
  data.rootCreateForm.menu_category_id =
    (props.menu.id !== '' &&
    props.menu.type === 'menu' &&
    props.menu.menu_category_id !== ''
      ? `${props.menu.menu_category_id}`
      : categoriesList.value[0]?.id) || '';
  modalApi.open();
};

// 新建子菜单
const addChildMenu = () => {
  if (props.menu.type !== 'menu') {
    ElMessage({
      message: '请选择一个菜单，且类型不能为页面',
      type: 'warning',
    });
    return;
  }
  data.childCreateForm.menu_category_id =
    (props.menu.menu_category_id === ''
      ? categoriesList.value[0]?.id
      : `${props.menu.menu_category_id}`) || '';
  data.childCreateForm.parent_menu_id = props.menu.id;
  data.childCreateForm.parent_menu_name = props.menu.name;
  modalSonApi.open();
};

/**
 * 注册添加弹窗 页面
 */
const [ModalPage, modalPageApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    if (
      data.pageCreateForm.menu_id.slice(0, 3) === '100' ||
      data.pageCreateForm.menu_id.slice(0, 3) === '200'
    ) {
      data.pageCreateForm.menu_id = data.pageCreateForm.menu_id.slice(3);
    }
    createPageFormRef.value.validate().then(() => {
      PageApi.createPageApi(data.pageCreateForm).then(() => {
        modalPageApi.close();
        createPageFormRef.value.resetFields();
        emits('getMenuTree');
      });
    });
  },
  onCancel: () => {
    createPageFormRef.value.resetFields();
    modalPageApi.close();
  },
});

/**
 * 注册添加弹窗 修改根菜单
 */
const [ModalGenUpdate, modalGenUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    rootUpFormRef.value.validate().then(() => {
      MenuApi.updateMenuApi(data.rootUpdateForm).then(() => {
        modalGenUpdateApi.close();
        rootUpFormRef.value.resetFields();
        emits('getMenuTree');
      });
    });
  },
  onCancel: () => {
    rootUpFormRef.value.resetFields();
    modalGenUpdateApi.close();
  },
});

/**
 * 注册添加弹窗 修改子菜单
 */
const [ModalSonUpdate, modalSonUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    updateChildFormRef.value.validate().then(() => {
      MenuApi.updateMenuApi(data.childUpdateForm).then(() => {
        modalSonUpdateApi.close();
        updateChildFormRef.value.resetFields();
        emits('getMenuTree');
      });
    });
  },
  onCancel: () => {
    updateChildFormRef.value.resetFields();
    modalSonUpdateApi.close();
  },
});
// 修改菜单
const updateMenu = () => {
  const _menu = data.menuConfig;
  if (props.menu.parent_menu_id === '0') {
    data.rootUpdateForm = {
      id: _menu.id,
      name: _menu.name,
      code: _menu.code,
      type: _menu.type,
      url: _menu.url,
      menu_category_id: _menu.menu_category_id,
      parent_menu_id: _menu.parent_menu_id,
      icon: _menu.icon,
      weight: _menu.weight,
      enabled: _menu.enabled,
    };
    modalGenUpdateApi.open();
  } else {
    data.childUpdateForm = {
      id: _menu.id,
      name: _menu.name,
      code: _menu.code,
      type: _menu.type,
      url: _menu.url,
      menu_category_id: _menu.menu_category_id,
      parent_menu_id: _menu.parent_menu_id,
      icon: _menu.icon,
      weight: _menu.weight,
      enabled: _menu.enabled,
    };
    // updateChildMenuDialogVisible.value = true;
    modalSonUpdateApi.open();
  }
};

// 新建页面
const addPage = () => {
  if (props.menu.type !== 'menu') {
    ElMessage({
      message: '请选择一个菜单，且类型不能为页面',
      type: 'warning',
    });
    return;
  }
  data.pageCreateForm.menu_id = props.menu.id;
  data.pageCreateForm.parent_menu_name = props.menu.name;
  modalPageApi.open();
};

/**
 * 注册添加弹窗 修改页面
 */
const [ModalPageUpdate, modalPageUpdateApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    updatePageFormRef.value.validate().then(() => {
      PageApi.updatePageApi(data.pageUpdateForm).then(() => {
        modalPageUpdateApi.close();
        updatePageFormRef.value.resetFields();
        emits('getMenuTree');
      });
    });
  },
  onCancel: () => {
    updatePageFormRef.value.resetFields();
    modalPageUpdateApi.close();
  },
});
// 修改页面
const updatePage = () => {
  const _page = data.menuConfig;
  data.pageUpdateForm = {
    id: _page.id,
    name: _page.name,
    code: _page.code,
    url: _page.url,
    weight: _page.weight,
    enabled: _page.enabled,
    config_info: _page.config_info || '',
    memo: _page.memo || '',
  };
  // updatePageDialogVisible.value = true;
  modalPageUpdateApi.open();
};

// 删除菜单
const deleteMenu = () => {
  if (props.menu.id === '') {
    ElMessage({
      message: '请选择一个菜单',
      type: 'warning',
    });
    return;
  }
  showConfirmModal({
    title: '菜单删除',
    description: '确定删除此菜单吗?',
    onConfirm: async () => {
      MenuApi.deleteMenuApi(props.menu.id).then(() => {
        emits('getMenuTree');
      });
    },
  });
};
// 删除页面
const deletePage = () => {
  if (props.menu.id === '') {
    ElMessage({
      message: '请选择一个页面',
      type: 'warning',
    });
    return;
  }

  showConfirmModal({
    title: '页面删除',
    description: '确定删除此页面吗?',
    onConfirm: async () => {
      PageApi.deletePageApi(props.menu.id).then(() => {
        emits('getMenuTree');
      });
    },
  });
};
</script>
<template>
  <div>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <ElCard class="h-[242px]" shadow="never">
      <template #header>
        <div class="card-header">
          <span>菜单配置</span>
          <div>
            <ElButton type="primary" @click="addRootMenu">
              添加根菜单
            </ElButton>
            <ElButton type="primary" @click="addChildMenu">
              添加子菜单
            </ElButton>
            <ElButton type="primary" @click="addPage"> 添加页面 </ElButton>
          </div>
        </div>
      </template>
      <div>
        <ElRow :gutter="5">
          <ElCol :span="6">
            <ElRow
              v-if="
                data.menuConfig.type === 'menu' ||
                data.menuConfig.type === null ||
                data.menuConfig.type === ''
              "
              :gutter="5"
            >
              <ElCol :span="7" class="grid-content-right"> 菜单ID： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.id" readonly />
              </ElCol>
            </ElRow>
            <ElRow v-if="data.menuConfig.type === 'page'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 页面ID： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.id" readonly />
              </ElCol>
            </ElRow>
          </ElCol>
          <ElCol :span="6">
            <ElRow
              v-if="
                data.menuConfig.type === 'menu' ||
                data.menuConfig.type === null ||
                data.menuConfig.type === ''
              "
              :gutter="5"
            >
              <ElCol :span="7" class="grid-content-right"> 菜单名称： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.name" readonly />
              </ElCol>
            </ElRow>
            <ElRow v-if="data.menuConfig.type === 'page'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 页面名称： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.name" readonly />
              </ElCol>
            </ElRow>
          </ElCol>
          <ElCol :span="6">
            <ElRow
              v-if="
                data.menuConfig.type === 'menu' ||
                data.menuConfig.type === null ||
                data.menuConfig.type === ''
              "
              :gutter="5"
            >
              <ElCol :span="7" class="grid-content-right"> 菜单编码： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.code" readonly />
              </ElCol>
            </ElRow>
            <ElRow v-if="data.menuConfig.type === 'page'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 页面编码： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.code" readonly />
              </ElCol>
            </ElRow>
          </ElCol>
          <ElCol :span="6">
            <ElRow
              v-if="
                data.menuConfig.type === 'menu' ||
                data.menuConfig.type === null ||
                data.menuConfig.type === ''
              "
              :gutter="5"
            >
              <ElCol :span="7" class="grid-content-right"> 菜单类型： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.type_display" readonly />
              </ElCol>
            </ElRow>
            <ElRow v-if="data.menuConfig.type === 'page'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 页面类型： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.type" readonly />
              </ElCol>
            </ElRow>
          </ElCol>
        </ElRow>
        <ElRow :gutter="5" style="margin-top: 16px">
          <ElCol :span="6">
            <ElRow
              v-if="
                data.menuConfig.type === 'menu' ||
                data.menuConfig.type === null ||
                data.menuConfig.type === ''
              "
              :gutter="5"
            >
              <ElCol :span="7" class="grid-content-right"> 菜单权重： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.weight" readonly />
              </ElCol>
            </ElRow>
            <ElRow v-if="data.menuConfig.type === 'page'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 页面权重： </ElCol>
              <ElCol :span="17">
                <div style="line-height: 32px">
                  <ElInput :value="data.menuConfig.weight" readonly />
                </div>
              </ElCol>
            </ElRow>
          </ElCol>
          <ElCol :span="6">
            <ElRow :gutter="5">
              <ElCol :span="7" class="grid-content-right"> URL： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.url" readonly />
              </ElCol>
            </ElRow>
          </ElCol>
          <ElCol :span="6">
            <ElRow :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 菜单图标： </ElCol>
              <ElCol :span="17">
                <div style="line-height: 32px">
                  <ElIcon
                    v-if="
                      data.menuConfig.icon !== '' &&
                      data.menuConfig.icon !== null
                    "
                    size="14"
                  >
                    <component :is="data.menuConfig.icon" />
                  </ElIcon>
                  <span v-else>--</span>
                </div>
              </ElCol>
            </ElRow>
          </ElCol>
          <ElCol :span="6">
            <ElRow v-if="data.menuConfig.type === 'menu'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 是否启用： </ElCol>
              <ElCol :span="17">
                <ElInput :value="data.menuConfig.enabled_display" readonly />
              </ElCol>
            </ElRow>
            <ElRow v-if="data.menuConfig.type === 'page'" :gutter="5">
              <ElCol :span="7" class="grid-content-right"> 是否启用： </ElCol>
              <ElCol :span="17">
                <span>--</span>
              </ElCol>
            </ElRow>
          </ElCol>
        </ElRow>
        <ElRow v-if="data.menuConfig.id" :gutter="5" style="margin-top: 16px">
          <ElCol :span="24" style="line-height: 30px; text-align: right">
            <ElButton
              v-if="data.menuConfig.type === 'menu'"
              type="success"
              @click="updateMenu"
            >
              修改菜单
            </ElButton>
            <ElButton
              v-if="data.menuConfig.type === 'page'"
              type="success"
              @click="updatePage"
            >
              修改页面
            </ElButton>
            <ElButton
              v-if="data.menuConfig.type === 'menu'"
              type="danger"
              @click="deleteMenu"
            >
              删除菜单
            </ElButton>
            <ElButton
              v-if="data.menuConfig.type === 'page'"
              type="danger"
              @click="deletePage"
            >
              删除页面
            </ElButton>
          </ElCol>
        </ElRow>
      </div>
    </ElCard>
    <!-- 添加根菜单 -->
    <ModalGen :fullscreen-button="false" class="w-[650px]" title="添加根菜单">
      <ElForm
        ref="rootFormRef"
        :model="data.rootCreateForm"
        :rules="rootRules"
        label-width="110px"
      >
        <ElFormItem label="菜单名称" prop="name">
          <ElInput v-model="data.rootCreateForm.name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="菜单编码" prop="code">
          <ElInput v-model="data.rootCreateForm.code" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="菜单分类" prop="menu_category_id">
          <ElSelect
            v-model="data.rootCreateForm.menu_category_id"
            style="width: 100%"
          >
            <ElOption
              v-for="item in categoriesList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="菜单类型" prop="type">
          <ElSelect v-model="data.rootCreateForm.type" style="width: 100%">
            <ElOption
              v-for="item in menuTypes"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="跳转地址" prop="icon">
          <ElInput v-model="data.rootCreateForm.url" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="图标" prop="icon">
          <ElInput v-model="data.rootCreateForm.icon" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="排序" prop="weight">
          <ElInputNumber
            v-model="data.rootCreateForm.weight"
            :controls="false"
            :min="0"
            :precision="0"
            placeholder="请输入"
            style="width: 100%"
          />
        </ElFormItem>
        <ElFormItem label="是否启用" prop="enabled">
          <ElSwitch
            v-model="data.rootCreateForm.enabled"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="停用"
          />
        </ElFormItem>
      </ElForm>
    </ModalGen>

    <!-- 添加子菜单 -->
    <ModalSon :fullscreen-button="false" class="w-[650px]" title="添加子菜单">
      <ElForm
        ref="createChildFormRef"
        :model="data.childCreateForm"
        :rules="ChildAddRules"
        label-width="110px"
      >
        <ElFormItem label="父级菜单" prop="parent_menu_name">
          <ElInput :value="data.childCreateForm.parent_menu_name" readonly />
        </ElFormItem>
        <ElFormItem label="菜单名称" prop="name">
          <ElInput v-model="data.childCreateForm.name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="菜单编码" prop="code">
          <ElInput v-model="data.childCreateForm.code" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="图标" prop="icon">
          <ElInput v-model="data.childCreateForm.icon" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="菜单类型" prop="type">
          <ElSelect v-model="data.childCreateForm.type" style="width: 100%">
            <ElOption
              v-for="item in menuTypes"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="跳转地址" prop="url">
          <ElInput v-model="data.childCreateForm.url" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="排序" prop="weight">
          <ElInputNumber
            v-model="data.childCreateForm.weight"
            :controls="false"
            :min="0"
            :precision="0"
            placeholder="请输入"
            style="width: 100%"
          />
        </ElFormItem>
        <ElFormItem label="是否启用" prop="enabled">
          <ElSwitch
            v-model="data.childCreateForm.enabled"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="停用"
          />
        </ElFormItem>
      </ElForm>
    </ModalSon>

    <!-- 添加页面 -->
    <ModalPage :fullscreen-button="false" class="w-[650px]" title="添加页面">
      <ElForm
        ref="createPageFormRef"
        :model="data.pageCreateForm"
        :rules="pageRules"
        label-width="100px"
      >
        <ElFormItem label="父级菜单" prop="parent_menu_name">
          <ElInput v-model="data.pageCreateForm.parent_menu_name" readonly />
        </ElFormItem>
        <ElFormItem label="页面名称" prop="name">
          <ElInput v-model="data.pageCreateForm.name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="页面编码" prop="code">
          <ElInput v-model="data.pageCreateForm.code" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="页面权重" prop="weight">
          <ElInput v-model="data.pageCreateForm.weight" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="页面URL" prop="url">
          <ElInput v-model="data.pageCreateForm.url" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="配置信息" prop="config_info">
          <ElInput
            v-model="data.pageCreateForm.config_info"
            :rows="6"
            placeholder="请输入"
            style="width: 100%"
            type="textarea"
          />
          <div style="line-height: 22px; color: rgb(0 0 0 / 60%)">
            配置信息为JSON格式，可配置项包括：
            <div>&emsp;visible: 页面可见(1-显示; 0-隐藏;)</div>
            <div>&emsp;cached: 页面缓存(1-缓存; 0-不缓存;)</div>
            <div>&emsp;component: 页面组件名称</div>
          </div>
        </ElFormItem>
        <ElFormItem label="备注" prop="memo">
          <ElInput
            v-model="data.pageCreateForm.memo"
            :rows="3"
            maxlength="100"
            placeholder="请输入"
            type="textarea"
          />
        </ElFormItem>
      </ElForm>
    </ModalPage>
    <!-- 修改页面 -->
    <ModalPageUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="修改页面"
    >
      <ElForm
        ref="updatePageFormRef"
        :model="data.pageUpdateForm"
        :rules="pageRules"
        label-width="100px"
      >
        <ElFormItem label="页面名称" prop="name">
          <ElInput v-model="data.pageUpdateForm.name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="页面编码" prop="code">
          <ElInput v-model="data.pageUpdateForm.code" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="页面权重" prop="weight">
          <ElInput v-model="data.pageUpdateForm.weight" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="页面URL" prop="url">
          <ElInput v-model="data.pageUpdateForm.url" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="配置信息" prop="config_info">
          <ElInput
            v-model="data.pageUpdateForm.config_info"
            :rows="6"
            placeholder="请输入"
            style="width: 100%"
            type="textarea"
          />
          <div style="line-height: 22px; color: rgb(0 0 0 / 60%)">
            配置信息为JSON格式，可配置项包括：
            <div>&emsp;visible: 页面可见(1-显示; 0-隐藏;)</div>
            <div>&emsp;cached: 页面缓存(1-缓存; 0-不缓存;)</div>
            <div>&emsp;component: 页面组件名称</div>
          </div>
        </ElFormItem>
        <ElFormItem label="备注" prop="memo">
          <ElInput
            v-model="data.pageUpdateForm.memo"
            :rows="3"
            maxlength="100"
            placeholder="请输入"
            type="textarea"
          />
        </ElFormItem>
      </ElForm>
    </ModalPageUpdate>
    <!-- 修改根菜单 -->
    <ModalGenUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="修改根菜单"
    >
      <ElForm
        ref="rootUpFormRef"
        :model="data.rootUpdateForm"
        :rules="rootRules"
        label-width="110px"
      >
        <ElFormItem label="菜单名称" prop="name">
          <ElInput v-model="data.rootUpdateForm.name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="菜单编码" prop="code">
          <ElInput v-model="data.rootUpdateForm.code" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="跳转地址" prop="url">
          <ElInput v-model="data.rootUpdateForm.url" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="图标" prop="icon">
          <ElInput v-model="data.rootUpdateForm.icon" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="排序" prop="weight">
          <ElInputNumber
            v-model="data.rootUpdateForm.weight"
            :controls="false"
            :min="0"
            :precision="0"
            placeholder="请输入"
            style="width: 100%"
          />
        </ElFormItem>
        <ElFormItem label="是否启用" prop="enabled">
          <ElSwitch
            v-model="data.rootUpdateForm.enabled"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="停用"
          />
        </ElFormItem>
      </ElForm>
    </ModalGenUpdate>

    <!-- 修改子菜单 -->
    <ModalSonUpdate
      :fullscreen-button="false"
      class="w-[650px]"
      title="修改子菜单"
    >
      <ElForm
        ref="updateChildFormRef"
        :model="data.childUpdateForm"
        :rules="childUpdateRules"
        label-width="110px"
      >
        <ElFormItem label="菜单名称" prop="name">
          <ElInput v-model="data.childUpdateForm.name" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="菜单编码" prop="code">
          <ElInput v-model="data.childUpdateForm.code" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="图标" prop="icon">
          <ElInput v-model="data.childUpdateForm.icon" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="跳转地址" prop="url">
          <ElInput v-model="data.childUpdateForm.url" placeholder="请输入" />
        </ElFormItem>
        <ElFormItem label="排序" prop="weight">
          <ElInputNumber
            v-model="data.childUpdateForm.weight"
            :controls="false"
            :min="0"
            :precision="0"
            placeholder="请输入"
            style="width: 100%"
          />
        </ElFormItem>
        <ElFormItem label="是否启用" prop="enabled">
          <ElSwitch
            v-model="data.childUpdateForm.enabled"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="停用"
          />
        </ElFormItem>
      </ElForm>
      <!-- <div style="text-align: right">
        <ElButton @click="cancelUpdateChildMenu(updateChildFormRef)">
          取 消
        </ElButton>
        <ElButton
          type="primary"
          @click="submitUpdateChildMenu(updateChildFormRef)"
        >
          确 定
        </ElButton>
      </div> -->
    </ModalSonUpdate>
  </div>
</template>

<style lang="scss" scoped>
:deep(.ElCard__header) {
  padding: 6px 10px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.grid-content-right {
  min-height: 32px;
  font-size: 10pt;
  line-height: 32px;
  text-align: right;
}
</style>
