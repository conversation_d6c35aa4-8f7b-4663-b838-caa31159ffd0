<script lang="ts" setup>
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElSegmented } from 'element-plus';

import SentryBoxOperationChildRecord from './components/sentryBoxOperationRecord/sentryBoxOperationChildRecord.vue';

defineOptions({
  name: 'SentryBoxOperationRecord', // 岗亭操作记录列表
});

const activeName = ref('specialRelease');
const selectOptions = [
  { label: '特殊放行', value: 'specialRelease' },
  { label: '取消放行', value: 'cancelRelease' },
  { label: '车牌号矫正', value: 'plateNoCorrect' },
  { label: '入口抬杆', value: 'entranceRangePole' },
  { label: '出口抬杆', value: 'exitRangePole' },
  { label: '手动匹配出场', value: 'manualMatchingExit' },
  { label: '重复入场', value: 'repeatEntrance' },
  { label: '被冲车辆', value: 'rushedCar' },
  { label: '切换费率', value: 'changeRate' },
];
</script>
<template>
  <Page auto-content-height>
    <template #title>
      <ElSegmented
        v-model="activeName"
        :options="selectOptions"
        size="default"
      />
    </template>
    <template v-for="option in selectOptions" :key="option.value">
      <SentryBoxOperationChildRecord
        v-show="activeName === option.value"
        :active-name="activeName"
        :select-options="selectOptions"
      />
    </template>
  </Page>
</template>
