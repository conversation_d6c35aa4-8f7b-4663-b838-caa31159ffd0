<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, ref, toRaw } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, ParkSpaceApi } from '#/api';
import ParkImage from '#/components/park-image.vue';
import parkSpaceAddModalComponent from '#/views/park/parkSpaceComponents/parkSpaceAddModal.vue';
// 查询参数类型
interface IFormValues {
  park_name: string;
  park_region_name: string;
  code: string;
  type: number[];
  property: number[];
}

// 表格行数据类型
interface IRowType {
  id: string;
  park_name: string;
  park_region_name: string;
  code: string;
  type_desc: string;
  property_desc: string;
  plate_no: string;
  picture: string;
}

const propertyOptions = ref<CommonModule.IEnumItem[]>([]);
const typeOptions = ref<CommonModule.IEnumItem[]>([]);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'park_name',
      label: '车场名称：',
      componentProps: {
        clearable: true,
        placeholder: '按车场名称搜索',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'park_region_name',
      label: '子场名称：',
      componentProps: {
        clearable: true,
        placeholder: '按子场名称搜索',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'code',
      label: '车位编号：',
      componentProps: {
        clearable: true,
        placeholder: '按车位编号搜索',
      },
    },
    {
      component: 'Select',
      fieldName: 'type',
      label: '车位类型：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: typeOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'property',
      label: '车位属性：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: propertyOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 30,
  },
  columns: [
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    { field: 'park_name', title: '车场名称' },
    { field: 'park_region_name', title: '子场名称' },
    { field: 'code', title: '车位编号' },
    { field: 'type_desc', title: '车位类型' },
    { field: 'property_desc', title: '车位属性' },
    {
      field: 'plate_no',
      title: '关联车牌号',
    },
    {
      field: 'picture_url',
      title: '车位图',
      slots: { default: 'picture_url' },
    },
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
      minWidth: 200,
      width: 200,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: getParkSpaceList,
    },
  },
};

/**
 * 获取停车场车位列表
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 */
async function getParkSpaceList({ page }: any, formValues: IFormValues) {
  try {
    const params = {
      park_name: formValues.park_name,
      park_region_name: formValues.park_region_name,
      code: formValues.code,
      properties: formValues.property || [],
      types: formValues.type || [],
    };
    const res = await ParkSpaceApi.getParkSpaceListApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 注册停车场车位列表
 * @description 启用表格 配置formOptions与gridOptions
 */
const [ParkSpaceTable, PSTRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 初始化数据
 */
function initDataForOptions() {
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.PARK, [
    { enum_key: 'propertyList', enum_value: 'EnumParkSpaceProperty' },
    { enum_key: 'typeList', enum_value: 'EnumParkSpaceType' },
  ]).then((res) => {
    propertyOptions.value = res.propertyList || [];
    typeOptions.value = res.typeList || [];
  });
}

/**
 * 注册添加员工弹窗
 * @description 配置connectedComponent
 */
const [ParkSpaceAddModal, PSAddModalRef] = useVbenModal({
  connectedComponent: parkSpaceAddModalComponent,
});

/**
 * 添加车位
 * @description 打开添加车位弹窗
 * @param {string} type 添加车位类型
 */
const addParkSpaceAction = (type: 'add' | 'edit' = 'add') => {
  PSAddModalRef.setData({
    type,
    propertyList: toRaw(propertyOptions),
    typeList: toRaw(typeOptions),
    query: () => {
      PSTRef.query();
    },
  });
  PSAddModalRef.open();
};

onMounted(() => {
  initDataForOptions();
});
</script>
<template>
  <Page auto-content-height>
    <!-- 添加车位弹窗 -->
    <ParkSpaceAddModal />
    <!-- 车位管理表格 -->
    <ParkSpaceTable>
      <template #toolbar-actions>
        <ElButton type="primary" @click="addParkSpaceAction">添加车位</ElButton>
      </template>
      <template #picture_url="{ row }">
        <ParkImage :src="row.picture_url" />
      </template>
      <template #actions>
        <div class="flex items-center justify-center">
          <ElButton link type="primary">编辑</ElButton>
          <ElButton link type="danger">删除</ElButton>
        </div>
      </template>
    </ParkSpaceTable>
  </Page>
</template>
