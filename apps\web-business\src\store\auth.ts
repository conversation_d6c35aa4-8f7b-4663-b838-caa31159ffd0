import type { Recordable, UserEntity, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { ElMessage, ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

import { getUserInfoByTokenApi, loginApi, logoutApi } from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);
  const captchaKey = ref('');

  /**
   * 账号密码+图形验证码 异步校验登录登录
   * Account Password + graphic verification code Asynchronous login Login
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const {
        token: accessToken,
        user_detail: userEntity,
        message,
      } = await loginApi({ ...params, key: captchaKey.value });
      if (accessToken) {
        // 将 iamToken 存储道 accessIamToken
        accessStore.setAccessIamToken('');
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(accessToken);

        userInfo = {
          avatar: userEntity.logo || '',
          desc: userEntity.memo || '',
          homePath: '/coupon',
          token: accessToken,
          realName: `${userEntity.contact}`,
          roles: [],
          userId: userEntity.user_id,
          username: userEntity.username,
          userEntity,
        };

        userStore.setUserInfo(userInfo);
        // accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(userInfo.homePath || DEFAULT_HOME_PATH);
        }

        if (userInfo?.realName) {
          ElNotification({
            message: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            title: $t('authentication.loginSuccess'),
            type: 'success',
          });
        }
      } else {
        ElMessage({
          message: message || '登录失败',
          type: 'warning',
        });
      }
    } finally {
      loginLoading.value = false;
    }
    return {
      userInfo,
    };
  }

  /**
   * 根据Token获取用户信息
   * 每次进入系统(包括刷新页面)都会重新请求 用于保证Token的有效和用户的数据有效
   */
  async function fetchUserInfo() {
    let userInfo: null | UserEntity | UserInfo = null;
    const res = await getUserInfoByTokenApi();
    const { user_id, username, logo, memo, contact } = res;
    userInfo = {
      avatar: logo || '',
      desc: memo || '',
      homePath: '/coupon',
      token: accessStore.accessToken || '',
      realName: contact,
      roles: [],
      userId: user_id,
      username,
      userEntity: res,
    };
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
    captchaKey,
  };
});
