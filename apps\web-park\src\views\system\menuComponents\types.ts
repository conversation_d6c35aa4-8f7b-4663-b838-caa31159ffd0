export interface menuConfig {
  id: string;
  name: string;
  code: string;
  type: string;
  type_display: string;
  weight: number | undefined;
  enabled: number | undefined;
  url: string;
  icon: string;
  menu_category_id: string;
  parent_menu_id: number | undefined;
  enabled_display?: string;
  config_info?: string;
  memo?: string;
}
export interface rootCreateForm {
  name: string;
  code: string;
  menu_category_id: string;
  parent_menu_id: number | undefined;
  icon: string;
  type: string;
  url: string;
  weight: number | undefined;
  enabled: number | undefined;
}
export interface rootUpdateForm {
  id: string;
  name: string;
  code: string;
  menu_category_id: string;
  parent_menu_id: number | undefined;
  icon: string;
  type: string;
  url: string;
  weight: number | undefined;
  enabled: number | undefined;
}
export interface childCreateForm {
  name: string;
  code: string;
  menu_category_id: string;
  parent_menu_id: number | undefined;
  parent_menu_name: string;
  type: string;
  url: string;
  icon: string;
  weight: number | undefined;
  enabled: number | undefined;
}
export interface childUpdateForm {
  id: string;
  name: string;
  code: string;
  menu_category_id: string;
  parent_menu_id: number | undefined;
  parent_menu_name?: string;
  icon: string;
  weight: number | undefined;
  enabled: number | undefined;
  url: string;
  type?: string;
}
export interface pageCreateForm {
  menu_id: string;
  parent_menu_name: string;
  name: string;
  code: string;
  url: string;
  weight: number | undefined;
  config_info: string;
  memo: string;
}
export interface pageUpdateForm {
  menu_id?: string;
  parent_menu_name?: string;
  id: string;
  name: string;
  code: string;
  url: string;
  weight: number | undefined;
  config_info: string;
  memo: string;
  enabled?: number;
}
export interface rootRulesItem {
  required: boolean;
  message: string;
  trigger: string;
  type?: string;
}
// export interface rootRules {
//   name?: rootRulesItem[];
//   code?: rootRulesItem[];
//   type?: rootRulesItem[];
//   menu_category_id?: rootRulesItem[];
//   weight?: rootRulesItem[];
//   url?: rootRulesItem[];
// }
export interface dataObjEs {
  menuConfig: menuConfig;
  rootCreateForm: rootCreateForm;
  rootUpdateForm: rootUpdateForm;
  childCreateForm: childCreateForm;
  childUpdateForm: childUpdateForm;
  pageCreateForm: pageCreateForm;
  pageUpdateForm: pageUpdateForm;
}
export interface categoriesEs {
  name: string;
  id: string;
}
export interface menuTypesEs {
  value: string;
  key: string;
}
export interface findEnumsEs {
  enum_key: string;
  enum_value: string;
}
