import { requestClient } from '#/api/request';

export namespace NSPlatform {
  /** 登录接口参数 */
  export interface IPagingMerchantCouponsParams {
    // 优免券名称
    coupon_meta_name?: string;
    // 每页条数
    limit: number;
    // 页码
    page: number;
    // 优惠卷状态
    states?: number[];
    // 优惠卷类型
    types?: number[];
  }

  /** 修改密码参数 */
  export interface IChangePasswdParams {
    old_passwd: string;
    new_passwd: string;
    repeat_passwd: string;
  }

  /** 优免卷下拉选项 */
  export interface ICouponOptions {
    id: string;
    coupon_meta_name: string;
    remainder_count: number;
    valid_end_time: string;
    valid_start_time: string;
    plaza_audit: number;
    coupon_params: string;
    coupon_send_type: number;
  }

  /** 获取停车时长参数 */
  export interface IStopCarTimeParams {
    plate_no: string;
  }

  /**
   * 获取停车时长
   */
  export interface IStopCarTimeResult {
    car_in_biz_no: string;
    in_time: string;
    stop_car_time: string;
  }

  /** 发放优免卷参数 */
  export interface IGrantCouponParams {
    merchant_coupon_id: string;
    plate_no: string;
    merchant_id: string;
    draw_type: number;
  }

  /** 分页获取优免卷核销记录参数 */
  export interface IPagingCouponRecordsParams {
    limit: number;
    page: number;
    coupon_meta_name: string;
    member_name: string;
    mobile: string;
    plate_no: string;
    types: number[];
    use_states: number[];
    draw_types: number[];
  }

  /** 分页获取领卷码参数 */
  export interface IPagingDrawCodesParams {
    limit: number;
    page: number;
    name: string;
    coupon_meta_name: string;
    types: number[];
    coupon_states: number[];
    draw_states: number[];
    audit_modes: number[];
  }

  /** 分页获取领卷码参数 */
  export interface ICreateDrawCodeParams {
    name: string;
    merchant_coupon_id: string;
    valid_end_time: string;
    valid_start_time: string;
    audit_mode: number;
    total_count: number;
    validDays: any;
    plaza_audit: any;
  }

  /** 分页获取领卷审核列表参数 */
  export interface IPagingMerchantDrawCodeAuditsParams {
    limit: number;
    page: number;
    coupon_meta_name: string;
    member_name: string;
    member_mobile: string;
    plate_no: string;
    types: number[];
    audit_state: number[];
  }

  /** 驳回领卷码参数 */
  export interface IDrawCodeAuditRejectParams {
    id: string;
    memo: string;
  }
}

/**
 * 获取全量优免券列表
 * @description 分页获取优免券列表
 */
export async function setChangePasswdApi(data: NSPlatform.IChangePasswdParams) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/changePasswd',
    data,
  );
}

/**
 * 获取全量优免券列表
 * @description 分页获取优免券列表
 */
export async function getPagingMerchantCouponsApi(
  data: NSPlatform.IPagingMerchantCouponsParams,
) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/pagingMerchantCoupons',
    data,
  );
}

/**
 * 获取可用的优免卷下拉选项（包含剩余数量）
 * @description 获取优免券列表
 */
export async function getListCouponMerchantsApi() {
  return requestClient.post<NSPlatform.ICouponOptions[]>(
    '/merchant/platform/listCouponMerchants',
  );
}

/**
 * 通过车牌号码获取停车时长
 * @param data 获取停车时长参数
 * @returns 停车时长结果
 */
export async function getStopCarTimeApi(data: NSPlatform.IStopCarTimeParams) {
  return requestClient.post<NSPlatform.IStopCarTimeResult>(
    '/merchant/platform/getStopCarTime',
    data,
    {
      headers: {
        hideErrorMessage: true,
      },
    },
  );
}

/**
 * 发放优免卷
 * @param data 发放优免卷参数
 * @returns 发放优免卷结果
 */
export async function grantCouponApi(data: NSPlatform.IGrantCouponParams) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/grantCoupon',
    data,
    {
      headers: {
        hideErrorMessage: true,
      },
    },
  );
}

/**
 * 分页获取优免卷核销记录
 * @param data 分页获取优免卷核销记录参数
 * @returns 分页获取优免卷核销记录结果
 */
export async function getPagingCouponRecordsApi(
  data: NSPlatform.IPagingCouponRecordsParams,
) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/pagingCouponRecords',
    data,
  );
}

/**
 * 分页获取领卷码
 * @param data 分页获取领卷码参数
 */
export async function getPagingDrawCodesApi(
  data: NSPlatform.IPagingDrawCodesParams,
) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/pagingDrawCodes',
    data,
  );
}

/**
 * 停用领卷码
 * @param id 领卷码ID
 */
export async function setDisableDrawCodeApi(id: number) {
  return requestClient.post<Record<string, any>>(
    `/merchant/platform/disableDrawCode/${id}`,
    {},
    {
      headers: {
        showMessage: true,
      },
    },
  );
}

/**
 * 启用领卷码
 * @param id 领卷码ID
 */
export async function setEnableDrawCodeApi(id: number) {
  return requestClient.post<Record<string, any>>(
    `/merchant/platform/enableDrawCode/${id}`,
    {},
    {
      headers: {
        showMessage: true,
      },
    },
  );
}

/**
 * 新建领卷码
 * @param data 领卷码实体
 */
export async function createDrawCodeApi(
  data: NSPlatform.ICreateDrawCodeParams,
) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/createDrawCode',
    data,
    {
      headers: {
        showMessage: true,
      },
    },
  );
}

/**
 * 分页获取领卷审核列表
 * @param data 分页获取领卷审核列表参数
 */
export async function getPagingMerchantDrawCodeAuditsApi(
  data: NSPlatform.IPagingMerchantDrawCodeAuditsParams,
) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/pagingMerchantDrawCodeAudits',
    data,
  );
}

/**
 * 通过领卷码/批量通过领卷码
 * @param ids 领卷码ID列表
 */
export async function setDrawCodeAuditPassApi(ids: number[]) {
  return requestClient.post<Record<string, any>>(
    `/merchant/platform/drawCodeAuditPass`,
    { ids },
    {
      headers: {
        showMessage: true,
      },
    },
  );
}

/**
 * 驳回领卷码
 * @param data 驳回领卷码参数
 */
export async function setDrawCodeAuditRejectApi(
  data: NSPlatform.IDrawCodeAuditRejectParams,
) {
  return requestClient.post<Record<string, any>>(
    '/merchant/platform/drawCodeAuditReject',
    data,
    {
      headers: {
        showMessage: true,
      },
    },
  );
}
// 导出优免券核销记录
const exportCancel = (data: any) => {
  return requestClient.post('/merchant/platform/exportCancel', data, {
    responseType: 'arraybuffer',
  });
};

export const PlatformApi = {
  setChangePasswdApi,
  getPagingMerchantCouponsApi,
  getListCouponMerchantsApi,
  getStopCarTimeApi,
  grantCouponApi,
  getPagingCouponRecordsApi,
  getPagingDrawCodesApi,
  setDisableDrawCodeApi,
  setEnableDrawCodeApi,
  createDrawCodeApi,
  getPagingMerchantDrawCodeAuditsApi,
  setDrawCodeAuditPassApi,
  setDrawCodeAuditRejectApi,
  exportCancel,
};
