<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSParkFee } from '#/api/park/fee';

import { onBeforeMount, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonApi, CommonModule, ParkFeeApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'AppointmentRecord', // 预约记录列表
});

interface IFormValues {
  states?: number[];
  in_time?: string[];
  park_id: string;
  park_name: string;
  end_time: string;
  start_time: string;
  plate_no: string;
}

/**
 * 业务变量
 */
const isReset = ref(false);
const statesOptions = ref<CommonModule.IEnumItem[]>([]);
const userStore = useUserStore();

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      ARRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  ARRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  ARRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
        placeholder: '请输入车牌号',
      },
    },
    {
      component: 'Select',
      defaultValue: [],
      fieldName: 'states',
      label: '预约状态：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: statesOptions,
      },
    },
    {
      component: 'DatePicker',
      defaultValue: [],
      fieldName: 'in_time',
      label: '入场日期：',
      componentProps: {
        clearable: true,
        type: 'datetimerange',
        style: {
          width: 'auto',
        },
        startPlaceholder: '入场开始日期',
        endPlaceholder: '入场结束日期',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 自定义重置表单方法
  handleReset: onReset,
  // 表单布局
  // wrapperClass: 'md:grid-cols-4',
};

/**
 * 表格配置
 * @description预约记录管理列表
 */
const gridOptions: VxeGridProps<NSParkFee.IPagingReserveRecordsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    { field: 'park_code', title: '停车场编号', minWidth: 150 },
    { field: 'park_name', title: '停车场名称', minWidth: 150 },
    { field: 'park_region_name', title: '子场名称', minWidth: 100, width: 100 },
    { field: 'plate_no', title: '车牌号', minWidth: 100 },
    { field: 'payed_money', title: '预约费', sortable: true, minWidth: 100 },
    {
      field: 'plan_start_time',
      title: '入场时间',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'plan_end_time',
      title: '出场时间',
      sortable: true,
      minWidth: 200,
    },
    { field: 'state_desc', title: '预约状态', minWidth: 100 },
    { field: 'mbr_member_name', title: '会员昵称', minWidth: 100 },
    { field: 'mbr_member_mobile', title: '手机号', minWidth: 100 },
    { field: 'invoice_state_desc', title: '发票状态', minWidth: 100 },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: getPagingReserveRecords,
    },
  },
};

/**
 * 表格事件
 */
const gridEvents: VxeGridListeners = {
  checkboxChange({ row }) {
    console.error(row);
  },
};

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [AppointmentRecordTable, ARRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});

/**
 * 获取预约记录列表数据
 */
async function getPagingReserveRecords({ page }: any, formValues: IFormValues) {
  if (isReset.value) {
    return (isReset.value = false);
  }
  // 查询参数格式化
  if (!selectParkCheck.value?.park_id) {
    return ElMessage.warning('请先选择车场进行查询');
  }
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
    start_time: formValues!.in_time?.[0]
      ? dayjs(formValues!.in_time?.[0]).format('YYYY-MM-DD HH:mm:ss')
      : '',
    end_time: formValues!.in_time?.[1]
      ? dayjs(formValues!.in_time?.[1]).format('YYYY-MM-DD HH:mm:ss')
      : '',
  };
  delete params.in_time;
  try {
    const res = await ParkFeeApi.getPagingReserveRecordsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

/**
 * 初始化搜索条件
 */
const initSelectOptionsData = async () => {
  // 获取预约状态
  const params = [
    { enum_key: 'statesList', enum_value: 'EnumMemberSpaceState' },
  ];
  try {
    const res = await CommonApi.findEnumsApi(
      CommonModule.EnumModuleType.PARK,
      params,
    );
    statesOptions.value = res.statesList;
  } catch {
    statesOptions.value = [];
  }
};

const loadData = () => {
  ARRef.setLoading(true);
  setTimeout(() => {
    ARRef.setLoading(false);
    ARRef.query();
  }, 200);
};

onBeforeMount(() => {
  initSelectOptionsData();
});

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    ARRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});
</script>
<template>
  <Page auto-content-height>
    <!--预约记录管理表格 -->
    <AppointmentRecordTable table-title="预约记录列表" />
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </Page>
</template>
