import { requestClient } from '#/api/request';

export namespace NSAcc {
  export interface IParkTempTotalByDayParams {
    park_id?: string;
    park_name?: string;
    stl_month?: string;
  }

  export interface IParkTempTotalByDayRow {
    coupon_tolal_cnt?: number;
    coupon_tolal_money?: number;
    etc_tolal_cnt?: number;
    etc_tolal_money?: number;
    need_total_money?: number;
    park_id?: string;
    park_name?: string;
    stl_dat?: string;
    stl_date?: string;
    exportLoading?: boolean;
  }

  export interface IParkTempTotalByDayResult {
    coupon_tolal_cnt_sum?: number;
    coupon_tolal_money_sum?: number;
    start_time?: string;
    end_time?: string;
    etc_jf_refund_total_cnt_sum?: number;
    etc_jf_refund_total_money_sum?: number;
    etc_need_total_money_sum?: number;
    etc_real_total_money_sum?: number;
    etc_refund_total_cnt_sum?: number;
    etc_refund_total_money_sum?: number;
    etc_tolal_cnt_sum?: number;
    etc_tolal_money_sum?: number;
    etc_total_cnt_sum?: number;
    need_total_money_sum?: number;
    day_infos?: IParkTempTotalByDayRow[];
  }

  export interface IEtcAccInfosParams {
    park_id?: string;
    stl_date?: string;
  }

  export interface IEtcAccInfosResult {
    refund_info: {
      etc_jf_refund_total_cnt: number;
      etc_jf_refund_total_money: number;
      etc_refund_total_cnt: number;
      etc_refund_total_money: number;
      park_id: string;
      park_name: string;
      stl_dat: string;
      stl_date: string;
    };
    trades_info: {
      etc_need_total_money: number;
      etc_real_total_money: number;
      etc_total_cnt: number;
      park_id: string;
      park_name: string;
      stl_dat: string;
      stl_date: string;
    };
  }
}

/**
 * 获取ETC数据交易月汇总
 * @param params 获取ETC数据交易月汇总
 */
async function getParkTempTotalByDayApi(
  params: NSAcc.IParkTempTotalByDayParams,
) {
  return requestClient.post<NSAcc.IParkTempTotalByDayResult>(
    '/console/acc/etc/parkTempTotalByDay',
    params,
  );
}

/**
 * 获取ETC数据交易日汇总
 * @param params 获取ETC数据交易日汇总
 */
async function getEtcAccInfosApi(params: NSAcc.IEtcAccInfosParams) {
  return requestClient.post<NSAcc.IEtcAccInfosResult>(
    '/console/acc/etc/getEtcAccInfos',
    params,
  );
}

/**
 * 导出ETC数据交易日明细
 * @param params 导出ETC数据交易日明细
 */
async function exportAccFileApi(params: NSAcc.IEtcAccInfosParams) {
  return requestClient.post<string>('/console/acc/etc/exportAccFile', params);
}

export const AccApi = {
  getParkTempTotalByDayApi,
  getEtcAccInfosApi,
  exportAccFileApi,
};
