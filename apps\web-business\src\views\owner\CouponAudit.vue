<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import { ElButton, ElMessage, ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PlatformApi } from '#/api';
import { CommonApi, CommonModule } from '#/api/common';

import CouponAuditReject from './CouponAuditComponents/CouponAdminReject.vue';

// 查询参数类型
interface IFormValues {
  coupon_meta_name: string;
  member_name: string;
  member_mobile: string;
  plate_no: string;
  types: number[];
  audit_state: number[];
}

// 领卷审核列表行数据类型
interface IRowType {
  audit_result: number;
  audit_result_desc: string;
  coupon_meta_name: string;
  id: string;
  mbr_member_mobile: string;
  mbr_member_name: string;
  memo: string;
  plate_no: string;
  type: number;
  type_desc: string;
}

// 优免卷类型 Options
const searchTypesOptions = ref<{ label: string; value: number }[]>([]);
// 审核状态 Options
const searchAuditStatesOptions = ref<{ label: string; value: number }[]>([]);

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'coupon_meta_name',
      label: '优免卷名称：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'member_name',
      label: '会员昵称：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'member_mobile',
      label: '手机号：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'plate_no',
      label: '车牌号：',
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'types',
      label: '优免卷类型：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchTypesOptions,
      },
    },
    {
      component: 'Select',
      fieldName: 'audit_state',
      label: '审核状态：',
      componentProps: {
        clearable: true,
        multiple: true,
        options: searchAuditStatesOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
};

const gridOptions: VxeGridProps<IRowType> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'center', type: 'checkbox', minWidth: 60, width: 60 },
    // 序号列 建议默认都配置
    { title: '序号', type: 'seq', minWidth: 60, width: 60 },
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'plate_no',
      title: '车牌号',
      minWidth: 100,
    },
    {
      field: 'member_name',
      title: '会员昵称',
      minWidth: 100,
    },
    {
      field: 'member_mobile',
      title: '手机号',
      minWidth: 100,
    },
    {
      field: 'coupon_meta_name',
      title: '优免卷名称',
      minWidth: 100,
    },
    {
      field: 'type_desc',
      title: '优免卷类型',
      minWidth: 100,
    },
    {
      field: 'audit_result_desc',
      title: '审核状态',
      slots: { default: 'audit_result_desc' },
      minWidth: 100,
    },
    {
      field: 'memo',
      title: '驳回原因',
      minWidth: 300,
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
      minWidth: 100,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: getCouponAuditList,
    },
  },
};

/**
 * 注册添加领卷码弹窗
 * @description 配置connectedComponent
 */
const [CouponAuditRejectModal, CARModalRef] = useVbenModal({
  connectedComponent: CouponAuditReject,
});

/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};

/**
 * 初始化搜索条件
 * @description 初始化搜索条件 启用状态 部门列表
 */
const initSearchData = () => {
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.COUPON, [
    { enum_key: 'types', enum_value: 'EnumCouponMetaType' },
  ]).then((res) => {
    searchTypesOptions.value = res.types;
  });

  CommonApi.findEnumsApi(CommonModule.EnumModuleType.PLATFORM, [
    { enum_key: 'states', enum_value: 'EnumDrawCodeAuditState' },
  ]).then((res) => {
    searchAuditStatesOptions.value = res.states;
  });
};

/**
 * 获取领卷审核列表数据
 * @param {any} page 分页参数
 * @param {IFormValues} formValues 查询参数
 * @returns {Promise<{items: IRowType[], total: number}>} 领卷审核列表和总数
 */
async function getCouponAuditList({ page }: any, formValues: IFormValues) {
  // 查询参数格式化
  const params = {
    coupon_meta_name: formValues.coupon_meta_name,
    member_name: formValues.member_name,
    member_mobile: formValues.member_mobile,
    plate_no: formValues.plate_no,
    types: formValues.types,
    audit_state: formValues.audit_state,
  };
  try {
    const res = await PlatformApi.getPagingMerchantDrawCodeAuditsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}
/**
 * 装填颜色
 * @param {number} state 状态
 * @returns {string} 颜色
 */
const getStateColor = (state: number) => {
  switch (state) {
    case 0: {
      return 'primary';
    }
    case 1: {
      return 'info';
    }
    case 2: {
      return 'danger';
    }
  }
};

/**
 * 注册优免卷列表表格
 * @description 启用表格 配置formOptions与gridOptions
 */
const [CouponAuditTable, CATRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

/**
 * 驳回
 * @description 驳回
 * @param {IRowType} row 行数据
 */
const handleReject = (row: IRowType) => {
  CARModalRef.setData({
    id: row.id,
    query: () => {
      CATRef.query();
    },
  });
  CARModalRef.open();
};

/**
 * 通过
 * @description 审批通过
 * @param {IRowType} row 行数据
 */
const handlePass = (row: IRowType) => {
  showConfirmModal({
    title: '领卷审核',
    description: '你确定要审批通过么？',
    onConfirm: async () => {
      await PlatformApi.setDrawCodeAuditPassApi([Number(row.id)]);
      CATRef.query();
    },
  });
};

/**
 * 批量通过
 * @description 获取勾选的领卷审核数据 批量审批通过
 */
const handleBatchPass = () => {
  const selectedRows = CATRef.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    return ElMessage.warning('请先选择要审批通过的数据');
  }
  const idsTotal = selectedRows.filter((row) => row.audit_result === 0);
  if (idsTotal.length === 0) {
    return ElMessage.warning(
      '你选择的数据必须全部是待审核的数据才能进行批量审批',
    );
  }
  const ids = idsTotal.map((row) => Number(row.id));
  showConfirmModal({
    title: '批量领卷审核',
    description: '你确定要对已勾选的数据批量审批通过么？',
    onConfirm: async () => {
      await PlatformApi.setDrawCodeAuditPassApi(ids);
      CATRef.query();
    },
  });
};

onMounted(() => {
  initSearchData();
});
</script>
<template>
  <Page auto-content-height>
    <!-- 驳回领卷审批弹窗 -->
    <CouponAuditRejectModal />
    <!-- 二次确认弹窗 -->
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <!-- 领卷审核列表表格 -->
    <CouponAuditTable>
      <template #audit_result_desc="{ row }">
        <ElTag :type="getStateColor(row.audit_result)">
          {{ row.audit_result_desc }}
        </ElTag>
      </template>
      <template #toolbar-actions>
        <ElButton type="primary" @click="handleBatchPass">
          批量审批通过
        </ElButton>
      </template>
      <template #action="{ row }">
        <div
          class="flex items-center justify-center gap-2"
          v-if="row.audit_result === 0"
        >
          <ElButton link type="danger" @click="handleReject(row)">
            驳回
          </ElButton>
          <ElButton link type="primary" @click="handlePass(row)">通过</ElButton>
        </div>
      </template>
    </CouponAuditTable>
  </Page>
</template>
