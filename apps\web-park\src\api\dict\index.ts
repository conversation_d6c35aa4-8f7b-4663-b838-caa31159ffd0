import { requestClient } from '#/api/request';

const config = {
  headers: {
    showMessage: true,
  },
};
export const DictApi = {
  /**
   * 分页查找字典分类
   */
  pagingDictTypeApi(data: any) {
    return requestClient.post<any>('/console/dict/pagingDictTypes', data);
  },

  /**
   * 新建字典分类
   */
  createDictTypeApi(data: any) {
    return requestClient.post<any>(
      '/console/dict/createDictType',
      data,
      config,
    );
  },

  /**
   * 修改字典分类
   */
  updateDictTypeApi(data: any) {
    return requestClient.post<any>(
      '/console/dict/updateDictType',
      data,
      config,
    );
  },

  /**
   * 删除字典分类
   */
  deleteDictTypeApi(id: any) {
    return requestClient.post<any>(
      `/console/dict/deleteDictType/${id}`,
      {},
      config,
    );
  },

  /**
   * 分页查找字典
   */
  pagingDictApi(data: any) {
    return requestClient.post<any>('/console/dict/pagingDicts', data);
  },

  /**
   * 新建字典
   */
  createDictApi(data: any) {
    return requestClient.post<any>(
      '/console/dict/createDictData',
      data,
      config,
    );
  },

  /**
   * 修改字典
   */
  updateDictApi(data: any) {
    return requestClient.post<any>(
      '/console/dict/updateDictData',
      data,
      config,
    );
  },

  /**
   * 删除字典
   */
  deleteDictApi(data: any) {
    return requestClient.post<any>(
      '/console/dict/deleteDictData',
      data,
      config,
    );
  },

  /**
   * 查询单条字典
   */
  getDictByIdApi(id: any) {
    return requestClient.post<any>(`/console/dict/getDictById/${id}`);
  },

  /**
   * 查询字典分类
   */
  getDictTypeApi() {
    return requestClient.post<any>('/console/dict/getDictType');
  },

  /**
   * 查询到期时间字典
   */
  getExpirationTimeApi() {
    return requestClient.post<any>(
      '/console/park/rent/space/apply/getExpirationTime',
    );
  },

  /**
   * 通过code查询字典列表
   */
  getDictsListApi(id: any) {
    return requestClient.post<any>(`/console/dict/getDictsList/${id}`);
  },
};
