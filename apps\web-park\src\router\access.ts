import type {
  ComponentRecordType,
  GenerateMenuAndRoutesOptions,
} from '@vben/types';

import { generateAccessible } from '@vben/access';
import { preferences } from '@vben/preferences';

import { MenuApi } from '#/api';
import { BasicLayout, IFrameView } from '#/layouts';

const forbiddenComponent = () => import('#/views/_core/fallback/forbidden.vue');

/**
 * 将路径转换为组件路径
 * @param componentUrl
 * @returns string
 */
function formarMenuComponentUrl(componentUrl: string) {
  return componentUrl.replaceAll('/src/views/', '/');
}

function formatMenuItem(item: any) {
  const {
    title,
    name,
    path,
    icon,
    cached,
    children,
    id,
    type,
    component,
    visible,
  } = item;
  return {
    id,
    name,
    path: path || `/${name}`,
    component:
      type === 'menu' ? 'BasicLayout' : formarMenuComponentUrl(component),
    meta: {
      title:
        !import.meta.env.PROD && visible === 0 ? `[已隐藏]-${title}` : title,
      icon: icon ? `ep:${icon.toLowerCase()}` : '',
      keepAlive: cached === 1,
      type,
      hideInMenu: visible === 0 && import.meta.env.PROD,
    },
    redirect: children?.length > 0 ? children[0].path : '',
    children: children?.map(formatMenuItem) || [],
  };
}

async function generateAccess(options: GenerateMenuAndRoutesOptions) {
  const pageMap: ComponentRecordType = import.meta.glob('../views/**/*.vue');

  const layoutMap: ComponentRecordType = {
    BasicLayout,
    IFrameView,
  };

  return await generateAccessible(preferences.app.accessMode, {
    ...options,
    fetchMenuListAsync: async () => {
      const res = await MenuApi.findNavMenusApi();
      const menus = res.map((element) => formatMenuItem(element));
      return menus;
    },
    // 可以指定没有权限跳转403页面
    forbiddenComponent,
    // 如果 route.meta.menuVisibleWithForbidden = true
    layoutMap,
    pageMap,
  });
}

export { generateAccess };
