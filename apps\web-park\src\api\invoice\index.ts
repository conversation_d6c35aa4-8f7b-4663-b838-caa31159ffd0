import { requestClient } from '#/api/request';

export namespace NSInvoice {
  export interface IPagingInvoiceRecordParams {
    page: number;
    limit: number;
    park_id: string;
    park_name: string;
    start_time?: string;
    end_time?: string;
    company_name?: string;
    name_or_mobile?: string;
    invoice_states?: number[];
    invoice_types?: number[];
    title_types?: number[];
    fee_types?: number[];
  }

  export interface IPagingInvoiceRecordRow {
    id: string;
    prk_park_name: string;
    relative_orders: string;
    money: number;
    state: number;
    state_desc: string;
    company_name: string;
    name: string;
    mbr_mobile: string;
    contact_mobile: string;
    title_type: number;
    title_type_desc: string;
    fee_type: number;
    fee_type_desc: string;
    invoice_type: number;
    invoice_type_desc: string;
    title: string;
    attachment_path: string;
    attachment_name: string;
    created_at: string;
    taxpayer_identify_no: string;
    email: string;
    company_address?: string;
    company_bank_account?: string;
    account_no?: string;
    queryLoading?: boolean;
    sendLoading?: boolean;
    cancelLoading?: boolean;
  }

  export interface IPagingInvoiceRecordResult {
    current_page: number;
    page_count: number;
    rows: IPagingInvoiceRecordRow[];
    total: number;
  }

  export interface ISaveFileParams {
    id: string;
    email: string;
    attachment_path: string;
    attachment_name: string;
  }

  export interface IRelativeOrderPagingParams {
    page: number;
    limit: number;
    park_order_ids: string[];
    reserve_order_ids: string[];
    rent_order_ids?: string[];
  }

  export interface IRelativeParkOrderPagingRow {
    order_no: string;
    park_name: string;
    park_region_name: string;
    in_time: string;
    to_time: string;
    in_gateway_name: string;
    out_gateway_name: string;
    park_time: string;
    car_type_desc: string;
    park_type_desc: string;
    plate_no: string;
    order_state_desc: string;
    pay_method_desc: string;
    should_pay_money: number;
    current_coupon_money: number;
    payed_money: number;
    refund_state_desc: string;
    charge_name: string;
  }

  export interface IRelativeParkOrderPagingResult {
    current_page: number;
    page_count: number;
    rows: IRelativeParkOrderPagingRow[];
    total: number;
  }

  export interface IRelativeReserveOrderPagingRow {
    code: string;
    park_name: string;
    park_region_name: string;
    plate_no: string;
    payed_money: number;
    plan_start_time: string;
    plan_end_time: string;
    state_desc: string;
    mbr_member_name: string;
    mobile: string;
  }

  export interface IRelativeReserveOrderPagingResult {
    current_page: number;
    page_count: number;
    rows: IRelativeReserveOrderPagingRow[];
    total: number;
  }

  export interface IRelativeRentOrderPagingRow {
    park_name: string;
    code: string;
    rule_name: string;
    long_rent_type_desc: string;
    product_name: string;
    product_price: number;
    valid_start_time: string;
    valid_end_time: string;
    pay_state_desc: string;
    rent_state_desc: string;
    plate_no: string;
    mbr_member_nickname: string;
    mbr_member_mobile: string;
    renew_state_desc: string;
    refund_state_desc: string;
  }

  export interface IRelativeRentOrderPagingResult {
    current_page: number;
    page_count: number;
    rows: IRelativeRentOrderPagingRow[];
    total: number;
  }
}

/**
 * 获取电子发票记录列表数据
 * @param params 获取电子发票记录列表数据
 */
async function getPagingInvoiceRecordApi(
  params: NSInvoice.IPagingInvoiceRecordParams,
) {
  return requestClient.post<NSInvoice.IPagingInvoiceRecordResult>(
    '/console/invoice/pagingInvoiceRecord',
    params,
  );
}

/**
 * 保存电子发票
 * @param params 保存电子发票
 */
async function saveFileApi(params: NSInvoice.ISaveFileParams) {
  return requestClient.post('/console/invoice/saveFile', params);
}

/**
 * 查询电子发票结果
 * @param params 查询电子发票结果
 */
async function queryInvoiceApi(id: string) {
  return requestClient.post(`/console/invoice/queryEInvoice/${id}`);
}

/**
 * 发送电子邮件
 * @param params 发送电子邮件
 */
async function sendingInvoiceMailApi(id: string) {
  return requestClient.post(`/console/invoice/sendingEInvoiceMail/${id}`);
}

/**
 * 红冲-作废发票
 * @param params 红冲-作废发票
 */
async function cancelInvoiceApi(id: string) {
  return requestClient.post(`/console/invoice/cancelInvoice/${id}`);
}

/**
 * 获取临停费用类型-关联订单列表数据
 * @param params 获取临停费用类型-关联订单列表数据
 */
async function getRelativeParkOrderPagingApi(
  params: NSInvoice.IRelativeOrderPagingParams,
) {
  return requestClient.post<NSInvoice.IRelativeParkOrderPagingResult>(
    '/console/invoice/relativeParkOrderPaging',
    params,
  );
}

/**
 * 获取预约车位类型-关联订单列表数据
 * @param params 获取预约车位类型-关联订单列表数据
 */
async function getRelativeReserveOrderPagingApi(
  params: NSInvoice.IRelativeOrderPagingParams,
) {
  return requestClient.post<NSInvoice.IRelativeReserveOrderPagingResult>(
    '/console/invoice/relativeReserveOrderPaging',
    params,
  );
}

/**
 * 获取长租费用类型-关联订单列表数据
 * @param params 获取长租费用类型-关联订单列表数据
 */
async function getRelativeRentOrderPagingApi(
  params: NSInvoice.IRelativeOrderPagingParams,
) {
  return requestClient.post<NSInvoice.IRelativeRentOrderPagingResult>(
    '/console/invoice/relativeRentOrderPaging',
    params,
  );
}

export const InvoiceApi = {
  getPagingInvoiceRecordApi,
  saveFileApi,
  queryInvoiceApi,
  sendingInvoiceMailApi,
  cancelInvoiceApi,
  getRelativeParkOrderPagingApi,
  getRelativeReserveOrderPagingApi,
  getRelativeRentOrderPagingApi,
};
