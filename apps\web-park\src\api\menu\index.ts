import type { RouteRecordStringComponent } from '@vben/types';

import { requestClient } from '#/api/request';

interface IgetMenuByIdTypes {
  id: string;
  menu_category_id: string;
}
const config = {
  headers: {
    showMessage: true,
  },
};

export const MenuApi = {
  /**
   * 获取用户所有菜单
   */
  findNavMenusApi() {
    return requestClient.get<RouteRecordStringComponent[]>(
      '/console/menu/findNavMenus',
    );
  },

  /**
   * 查询菜单分类
   */
  listMenuCategoriesApi() {
    return requestClient.get<any>('/console/menu/listMenuCategories');
  },

  /**
   * 添加菜单
   */
  createMenuApi(data: any) {
    return requestClient.post<any>('/console/menu/createMenu', data, config);
  },

  /**
   * 删除菜单
   */
  deleteMenuApi(id: string) {
    return requestClient.post<any>(`/console/menu/deleteMenu/${id}`);
  },

  /**
   * 获取菜单
   */
  getMenuByIdApi(data: IgetMenuByIdTypes) {
    return requestClient.post<any>('/console/menu/getMenuById', data);
  },

  /**
   * 查询菜单树
   */
  getMenuTreeApi(category: string) {
    return requestClient.get<any>(`/console/menu/getMenuTree/${category}`);
  },

  /**
   * 修改菜单
   */
  updateMenuApi(data: any) {
    return requestClient.post<any>('/console/menu/updateMenu', data, config);
  },
};
