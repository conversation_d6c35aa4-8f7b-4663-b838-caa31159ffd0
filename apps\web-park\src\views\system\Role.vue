<script setup name="RoleTable" lang="ts">
import type { ComponentInternalInstance } from 'vue';

import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import {
  computed,
  getCurrentInstance,
  onActivated,
  reactive,
  ref,
  watch,
} from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';

import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElCol,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElRow,
  ElSelect,
  ElSwitch,
  ElTag,
  ElText,
  ElTree,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  ApiModuleApi,
  CommonApi,
  CommonModule,
  PermissionGroupApi,
  RoleApi,
} from '#/api';

const addForm = ref<any>(null);
const editForm = ref();
const permissionTree = ref(ElTree);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const roleId = ref('');
const types = ref<any>([]);
const permissionApiList = ref<any>([]);
const roleIds = ref<any>([]);
const checkList = ref<any>([]);
interface IcheckApiList {
  mychecked?: any;
  id?: any;
  name?: any;
}
const checkApiList = ref<IcheckApiList[]>([
  {
    mychecked: '',
    id: null,
  },
]);
const treeSelectList = ref<any>([]);
const treeSelectApiList = ref<any>([]);
const permissionApiTreeData = ref<any>([]);
const permissionTreeData = ref<any>([]);
interface Idata {
  checked: boolean;
  treeParam: {
    id: string;
  };
  defaultProps: {
    children: string;
    label: string;
  };
  defaultApiProps: {
    children: string;
    label: string;
  };
  form: {
    code: string;
    enable?: string;
    enabled?: any;
    id?: any;
    name: string;
    type: string;
  };
  updateForm: {
    code: string;
    enable?: string;
    enabled?: any;
    id?: string;
    name: string;
    type: string;
  };
}
const data = reactive<Idata>({
  checked: false,
  treeParam: {
    id: '',
  },
  defaultProps: {
    children: 'children',
    label: 'name',
  },
  defaultApiProps: {
    children: 'children',
    label: 'group_name',
  },
  form: {
    name: '',
    code: '',
    type: '',
    enable: '',
    enabled: '',
  },
  updateForm: {
    name: '',
    code: '',
    type: '',
    enable: '',
  },
});
const rules = {
  name: [
    {
      required: true,
      message: '请输入角色名称',
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: '请输入角色编码',
      trigger: 'blur',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择所在部门',
      trigger: 'blur',
    },
  ],
  enabled: [
    {
      required: true,
      message: '请选择是否启用',
      trigger: 'blur',
    },
  ],
};
/**
 * 二次确认弹窗状态
 * @description 配置onConfirm回调
 */
const modalState = reactive({
  title: '',
  description: '',
  confirmFn: null as (() => void) | null,
});

/**
 * 注册二次确认弹窗
 * @description 配置onConfirm回调
 */
const [Modal, ModalRef] = useVbenModal({
  onConfirm: () => modalState.confirmFn?.(),
});

/**
 * 显示二次确认弹窗
 * @param options 弹窗配置
 * @param options.title 弹窗标题
 * @param options.description 弹窗描述
 * @param options.onConfirm 确认回调
 */
const showConfirmModal = (options: {
  description: string;
  onConfirm: () => void;
  title: string;
}) => {
  const { title, description, onConfirm } = options;
  ModalRef.setState({ title });
  modalState.description = description;
  modalState.confirmFn = () => {
    onConfirm();
    ModalRef.close();
  };
  ModalRef.open();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'name',
      label: '角色名称：',
      componentProps: {
        clearable: true,
        placeholder: '角色名称',
      },
    },
    {
      component: 'Input',
      defaultValue: '',
      fieldName: 'code',
      label: '角色编码：',
      componentProps: {
        clearable: true,
        placeholder: '角色编码',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  // wrapperClass: 'grid-cols-1 md:grid-cols-3',
};

/**
 * 表格配置
 * @description 员工管理列表
 */
const gridOptions: VxeGridProps = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    // 勾选建议默认都配置
    { align: 'left', title: '', type: 'checkbox', width: 40 },
    // 序号列 建议默认都配置
    // 字段配置 必须要自己调试配置最小宽度 保证标题不出现省略号
    {
      field: 'name',
      title: '角色名称',
    },
    {
      field: 'code',
      title: '角色编码',
    },
    {
      field: 'type_display',
      title: '角色类型',
    },
    {
      field: 'enabled',
      title: '启用状态',
      slots: { default: 'enabled' },
    },

    // 操作列 固定在右侧 统一
    {
      field: 'actions',
      title: '操作',
      fixed: 'right',
      slots: { default: 'actions' },
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const { rows, total } = await RoleApi.pagingRolesApi({
          page: page.currentPage,
          limit: page.pageSize,
          // parent_department_id: data.form.parent_department_id,
          ...formValues,
        });
        return {
          items: rows,
          total,
        };
      },
    },
  },
};
const [RoleTable, ETRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const isAllChecked = computed(() => {
  const len = checkApiList.value.length;
  let total = 0;
  checkApiList.value.forEach((item) => {
    if (item.mychecked) {
      total++;
    }
  });
  return len === total && total !== 0;
});

watch(isAllChecked, (val) => {
  data.checked = val;
});

const checkAllOrCancel = (check: any) => {
  // console.log('check', check);
  if (check) {
    checkApiList.value.forEach((item) => {
      item.mychecked = true;
      if (!permissionApiList.value.includes(item.id)) {
        permissionApiList.value.push(item.id);
      }
    });
  } else {
    checkApiList.value.forEach((item) => {
      item.mychecked = false;
      if (permissionApiList.value.includes(item.id)) {
        permissionApiList.value.splice(
          permissionApiList.value.indexOf(item.id),
          1,
        );
      }
    });
  }
};

const initSelects = () => {
  const param = [{ enum_key: 'types', enum_value: 'EnumRoleType' }];
  CommonApi.findEnumsApi(CommonModule.EnumModuleType.SYSTEM, param).then(
    (response) => {
      types.value = response.types;
    },
  );
};
onActivated(() => {
  // 数据初始化
  ETRef.query();
  treeSelectList.value = [];
  checkList.value = [];
  checkApiList.value = [];
  treeSelectApiList.value = [];
  permissionApiList.value = [];
  permissionApiTreeData.value = [];
  initSelects();
});
// 获取菜单树并获取关联的资源权限
const findMenuTree = (id: string) => {
  data.treeParam.id = id;
  RoleApi.findMenuTreeApi(data.treeParam).then((response) => {
    permissionTreeData.value = response.roleMenuTreeList;
    if (permissionTreeData.value.length > 0) {
      treeSelectList.value = response.selectNodelist;
    }
  });
};
// 保存菜单权限关联
const save = () => {
  let halfCheckedKeys = '';
  let checkedKeys = '';
  if (proxy && proxy.$refs.permissionTree) {
    // @ts-ignore
    halfCheckedKeys = proxy.$refs.permissionTree?.getHalfCheckedKeys();
    // @ts-ignore
    checkedKeys = proxy.$refs.permissionTree?.getCheckedKeys();
  }
  const param = {
    role_id: roleId.value,
    // select_node_list: halfCheckedKeys.concat(checkedKeys),
    select_node_list: [...halfCheckedKeys, ...checkedKeys],
  };
  RoleApi.configPermissionsApi(param).then(() => {
    ETRef.query();
  });
};
const [PermissionModal, PermissionModalApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    save();
    PermissionModalApi.close();
  },
  onCancel: () => {
    PermissionModalApi.close();
  },
});
// 权限管理
const handlePression = (row: { id: string }) => {
  findMenuTree(row.id);
  roleId.value = row.id;
  PermissionModalApi.open();
};

// 获取菜单树并获取关联的资源权限
const getApiPermissionByGroupId = (id: any) => {
  checkApiList.value = [];
  ApiModuleApi.getApiPermissionByGroupIdApi(id).then((response) => {
    checkApiList.value = response;
    checkApiList.value.forEach((item) => {
      item.mychecked = !!permissionApiList.value.includes(item.id);
    });
  });
};
const checkApiClick = (data: { id: any }) => {
  getApiPermissionByGroupId(data.id);
};
const onChangeApi = (apiId: any, e: any) => {
  if (e === true) {
    permissionApiList.value.push(apiId);
  } else {
    permissionApiList.value.splice(permissionApiList.value.indexOf(apiId), 1);
  }
};

// 保存api权限
const configApiPermissions = () => {
  const newArr: any[] = [];
  permissionApiList.value.forEach((item: any) => {
    if (!newArr.includes(item)) {
      newArr.push(item);
    }
  });
  RoleApi.configApiPermissionsApi({
    role_id: roleId.value,
    permission_list: newArr,
  }).then(() => {
    ETRef.query();
  });
};

// 启用
const enable = (row: { id: any }) => {
  showConfirmModal({
    title: '温馨提示',
    description: '是否启用该角色？',
    onConfirm: async () => {
      RoleApi.enableRoleApi(row.id).then(() => {
        ETRef.query();
      });
    },
  });
};
// 禁用
const disable = (row: { id: any }) => {
  showConfirmModal({
    title: '温馨提示',
    description: '是否禁用该角色？',
    onConfirm: async () => {
      RoleApi.disableRoleApi(row.id).then(() => {
        ETRef.query();
      });
    },
  });
};
const createRole = () => {
  addForm.value.validate().then(() => {
    RoleApi.createRoleApi(data.form).then(() => {
      ETRef.query();
      addForm.value.resetFields();
    });
  });
};
const batchDelete = () => {
  roleIds.value = ETRef.grid.getCheckboxRecords();
  if (roleIds.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的角色',
      type: 'warning',
    });
  } else {
    showConfirmModal({
      title: '温馨提示',
      description: '确定要删除吗？',
      onConfirm: async () => {
        const pushIds = [];
        for (let i = 0; i < roleIds.value.length; i++) {
          pushIds.push(Number.parseInt(roleIds.value[i].id));
        }
        data.form.id = pushIds;
        RoleApi.deleteRolesApi(data.form)
          .then(() => {
            ETRef.query();
            // if (response.success === true) {
            //   ElMessage({
            //     message: response.message,
            //     type: 'success',
            //   });

            // } else {
            //   ElMessage({
            //     message:
            //       response.detail_message == ''
            //         ? response.message
            //         : response.detail_message,
            //     type: 'error',
            //   });
            // }
          })
          .catch(() => {
            ETRef.query();
          });
      },
    });
  }
};
const deleteRole = (val: any) => {
  roleIds.value[0] = val;
  showConfirmModal({
    title: '温馨提示',
    description: '确定要删除吗？',
    onConfirm: async () => {
      const pushIds = [];
      for (let i = 0; i < roleIds.value.length; i++) {
        pushIds.push(Number.parseInt(roleIds.value[i].id));
      }
      data.form.id = pushIds;
      RoleApi.deleteRolesApi(data.form)
        .then(() => {
          ETRef.query();
        })
        .catch(() => {
          ETRef.query();
        });
    },
  });
};

const updateRole = () => {
  editForm.value.validate().then(() => {
    RoleApi.updateRoleApi(data.updateForm)
      .then((response) => {
        ElMessage({
          message: response.message,
          type: 'success',
        });
        ETRef.query();
        editForm.value.resetFields();
      })
      .catch(() => {
        ETRef.query();
      });
  });
};
const getPremissionGroupList = () => {
  permissionApiTreeData.value = [];
  PermissionGroupApi.permissionGroupListApi().then((response) => {
    permissionApiTreeData.value = response;
  });
};
const getRoleApiPermissionTree = (roleId: any) => {
  checkApiList.value = [];
  permissionApiList.value = [];
  ApiModuleApi.getRoleApiPermissionTreeApi(roleId).then((response) => {
    checkApiList.value = response;
    response.forEach((element: { id: any }) => {
      permissionApiList.value.push(element.id);
    });
  });
};
const [ApiModalPermission, ApiModalPermissionApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    configApiPermissions();
    ApiModalPermissionApi.close();
  },
  onCancel: () => {
    ApiModalPermissionApi.close();
  },
});
// Api权限管理
const handleApiPression = (row: { id: string }) => {
  roleId.value = row.id;
  getPremissionGroupList();
  getRoleApiPermissionTree(row.id);
  ApiModalPermissionApi.open();
};
// 取消
const createCancel = () => {
  addForm.value.resetFields();
};
// 取消
const updateCancel = () => {
  editForm.value.resetFields();
};
const [AddModal, AddModalApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    createRole();
    AddModalApi.close();
  },
  onCancel: () => {
    createCancel();
    AddModalApi.close();
  },
});

const [EditModal, EditModalApi] = useVbenModal({
  draggable: true,
  onConfirm: () => {
    updateRole();
    EditModalApi.close();
  },
  onCancel: () => {
    updateCancel();
    EditModalApi.close();
  },
});
const handleEdit = (id: any) => {
  RoleApi.getRoleByIdApi(id).then((response) => {
    data.updateForm = {
      id: response.id,
      name: response.name,
      code: response.code,
      type: response.type,
      enabled: response.enabled,
    };
    EditModalApi.open();
  });
};
const handleCreate = () => {
  data.form = {
    name: '',
    code: '',
    enabled: '1',
    type: '',
  };
  AddModalApi.open();
};
</script>
<template>
  <Page auto-content-height>
    <Modal :fullscreen-button="false" content-class="min-h-[80px]">
      <div class="flex h-[80px] items-center px-4">
        <ElText size="large">{{ modalState.description }}</ElText>
      </div>
    </Modal>
    <RoleTable>
      <template #toolbar-actions>
        <ElButton type="primary" @click="handleCreate()"> 添加角色 </ElButton>
        <ElButton type="danger" @click="batchDelete()"> 批量删除 </ElButton>
      </template>
      <template #enabled="{ row }">
        <ElTag :type="row.enabled === 1 ? 'success' : 'danger'">
          {{ row.enabled === 1 ? '已启用' : '已禁用' }}
        </ElTag>
      </template>
      <template #actions="{ row }">
        <ElButton link type="primary" @click="handleEdit(row.id)">
          修改
        </ElButton>
        <ElButton link type="primary" @click="handlePression(row)">
          权限管理
        </ElButton>
        <ElButton link type="primary" @click="handleApiPression(row)">
          Api权限管理
        </ElButton>
        <ElButton
          link
          v-if="row.enabled === 0"
          type="primary"
          @click="enable(row)"
        >
          启用
        </ElButton>
        <ElButton
          link
          v-if="row.enabled === 1"
          type="danger"
          @click="disable(row)"
        >
          禁用
        </ElButton>
        <ElButton
          link
          type="danger"
          v-if="row.enabled === 0"
          @click="deleteRole(row)"
        >
          删除
        </ElButton>
      </template>
    </RoleTable>
    <AddModal :fullscreen-button="false" class="w-[650px]" title="添加角色">
      <ElForm
        ref="addForm"
        :model="data.form"
        label-width="100px"
        :rules="rules"
      >
        <ElFormItem label="角色名称" prop="name">
          <ElInput
            v-model="data.form.name"
            placeholder="请输入角色名称"
            show-word-limit
            maxlength="20"
          />
        </ElFormItem>
        <ElFormItem label="角色编码" prop="code">
          <ElInput
            v-model="data.form.code"
            placeholder="请输入角色编码"
            show-word-limit
            maxlength="20"
          />
        </ElFormItem>
        <ElFormItem prop="type" label="角色类型">
          <ElSelect v-model="data.form.type" style="width: 100%">
            <ElOption
              v-for="item in types"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem prop="enabled" label="启用状态">
          <ElSwitch
            v-model="data.form.enabled"
            active-value="1"
            inactive-value="0"
          />
        </ElFormItem>
      </ElForm>
    </AddModal>
    <EditModal :fullscreen-button="false" class="w-[650px]" title="修改角色">
      <ElForm
        ref="editForm"
        :model="data.updateForm"
        label-width="80px"
        :rules="rules"
      >
        <ElFormItem label="角色名称" prop="name">
          <ElInput
            v-model="data.updateForm.name"
            placeholder="请输入角色名称"
            show-word-limit
            maxlength="20"
          />
        </ElFormItem>
        <ElFormItem label="角色编码" prop="code">
          <ElInput
            v-model="data.updateForm.code"
            placeholder="请输入角色编码"
            show-word-limit
            maxlength="20"
          />
        </ElFormItem>
        <ElFormItem prop="type" label="角色类型">
          <ElSelect v-model="data.updateForm.type" style="width: 100%">
            <ElOption
              v-for="item in types"
              :key="item.value"
              :label="item.key"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem prop="enabled" label="启用状态">
          <ElSwitch
            v-model="data.updateForm.enabled"
            :active-value="1"
            :inactive-value="0"
          />
        </ElFormItem>
      </ElForm>
      <!-- </ElDialog> -->
    </EditModal>

    <!-- 权限配置 -->

    <PermissionModal
      :fullscreen-button="false"
      class="w-[650px]"
      title="权限配置"
    >
      <ElRow :gutter="10">
        <ElCol :span="24">
          <ElCard
            shadow="never"
            class="card-box"
            :body-style="{ height: '530px', overflow: 'auto' }"
          >
            <template #header>
              <div style="display: inline-block; line-height: 32px">菜单树</div>
            </template>
            <div>
              <ElTree
                ref="permissionTree"
                :data="permissionTreeData"
                :props="data.defaultProps"
                node-key="id"
                :default-expand-all="true"
                :default-checked-keys="treeSelectList"
                show-checkbox
              />
            </div>
          </ElCard>
        </ElCol>
      </ElRow>
    </PermissionModal>

    <!-- Api权限配置 -->
    <ApiModalPermission
      :fullscreen-button="false"
      class="w-[650px]"
      title="Api权限配置"
    >
      <ElRow :gutter="10">
        <ElCol :span="10">
          <ElCard
            shadow="never"
            class="card-box"
            :body-style="{ height: '550px', overflow: 'auto' }"
          >
            <template #header>
              <div style="display: inline-block; line-height: 32px">
                Api权限树
              </div>
            </template>
            <div>
              <ElTree
                :data="permissionApiTreeData"
                :props="data.defaultApiProps"
                node-key="id"
                :default-expand-all="true"
                @node-click="checkApiClick"
              />
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="14">
          <ElCard
            shadow="never"
            class="card-box"
            :body-style="{ height: '550px', overflow: 'auto' }"
          >
            <template #header>
              <div style="display: inline-block; line-height: 32px">
                关联权限
              </div>
              <ElCheckbox
                v-model="data.checked"
                true-label="1"
                false-label="0"
                @change="checkAllOrCancel(!isAllChecked)"
                size="large"
                style="display: inline; float: right; line-height: 32px"
              >
                全 选
              </ElCheckbox>
            </template>
            <div
              v-for="(item, index) in checkApiList"
              :key="index"
              :label="item.name"
            >
              <ElCheckbox
                :key="index"
                v-model="item.mychecked"
                style="white-space: nowrap"
                @change="onChangeApi(item.id, $event)"
              />
              <span style="position: relative; top: -2px">
                &nbsp;{{ item.name }}
              </span>
            </div>
          </ElCard>
        </ElCol>
      </ElRow>
    </ApiModalPermission>
  </Page>
</template>

<style lang="scss" scoped></style>
