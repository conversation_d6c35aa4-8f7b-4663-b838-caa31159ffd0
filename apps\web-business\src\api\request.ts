/**
 * 该文件可自行根据业务逻辑进行调整
 */
import type { HttpResponse, RequestClientOptions } from '@vben/request';

import { useAppConfig } from '@vben/hooks';
import { preferences } from '@vben/preferences';
import {
  authenticateResponseInterceptor,
  errorMessageResponseInterceptor,
  RequestClient,
} from '@vben/request';
import { useAccessStore } from '@vben/stores';

import { ElMessage, ElMessageBox } from 'element-plus';

import { useAuthStore } from '#/store';

import { refreshTokenApi } from './core';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

// 在文件顶部添加防抖变量
let isShowingLoginExpired = false;

function createRequestClient(baseURL: string, options?: RequestClientOptions) {
  const client = new RequestClient({
    ...options,
    baseURL,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'X-Requested-With': 'XMLHttpRequest',
    },
    withCredentials: true,
    responseType: 'json',
  });

  /**
   * 重新认证逻辑
   */
  async function doReAuthenticate() {
    console.warn('Access token or refresh token is invalid or expired. ');
    const accessStore = useAccessStore();
    const authStore = useAuthStore();
    accessStore.setAccessToken(null);
    if (
      preferences.app.loginExpiredMode === 'modal' &&
      accessStore.isAccessChecked
    ) {
      accessStore.setLoginExpired(true);
    } else {
      await authStore.logout();
    }
  }

  /**
   * 刷新token逻辑
   */
  async function doRefreshToken() {
    const accessStore = useAccessStore();
    const resp = await refreshTokenApi();
    const newToken = resp.data;
    accessStore.setAccessToken(newToken);
    return newToken;
  }

  /**
   * 格式化token
   * @param token
   * @description 如果token为空，则返回null，否则返回token
   * 这里我们暂时不使用Bearer前缀，因为Park的后端全局没有使用Bearer前缀
   */
  function formatToken(token: null | string) {
    return token ? `${token}` : null;
  }

  // 请求头处理
  client.addRequestInterceptor({
    fulfilled: async (config) => {
      const accessStore = useAccessStore();
      // 如果请求头已经携带了Authorization，则不进行处理 说明是IAM的渐进式登录
      const authorization = config.headers.Authorization || '';
      if (
        authorization &&
        (authorization as string).includes('Bearer') &&
        config.url?.includes('loginByIAM')
      ) {
        // 不做处理
      } else {
        config.headers.Authorization = formatToken(accessStore.accessToken);
      }

      config.headers['Accept-Language'] = preferences.app.locale;
      return config;
    },
  });

  // 处理返回的响应数据格式
  // client.addResponseInterceptor(
  //   defaultResponseInterceptor({
  //     codeField: 'code',
  //     dataField: 'data',
  //     successCode: 0,
  //   }),
  // );

  // response数据解构
  client.addResponseInterceptor<HttpResponse>({
    fulfilled: (response) => {
      const { data: responseData, status } = response;
      const { code, data, success, message } = responseData;
      if (
        status >= 200 &&
        status < 400 &&
        (code === 'ok' || code === '200') &&
        success
      ) {
        // 如果请求头中携带了showMessage，则显示message
        if (response.config.headers.showMessage) {
          ElMessage.success(message);
        }
        return data;
      } else if (
        status >= 200 &&
        status < 400 &&
        response.config.responseType === 'arraybuffer'
      ) {
        return response;
      }
      // 停车系统401时候Http状态码为200，需要特殊处理
      if (code === '401') {
        response.status = 401;
      }
      throw Object.assign({}, response, { response });
    },
  });

  // token过期的处理
  client.addResponseInterceptor(
    authenticateResponseInterceptor({
      client,
      doReAuthenticate,
      doRefreshToken,
      enableRefreshToken: preferences.app.enableRefreshToken,
      formatToken,
    }),
  );

  // 通用的错误处理,如果没有进入上面的错误处理逻辑，就会进入这里
  client.addResponseInterceptor(
    errorMessageResponseInterceptor((msg: string, error) => {
      // 这里可以根据业务进行定制,你可以拿到 error 内的信息进行定制化处理，根据不同的 code 做不同的提示，而不是直接使用 message.error 提示 msg
      // 当前mock接口返回的错误字段是 error 或者 message
      const responseData = error?.response?.data ?? {};

      const detailMessage = responseData?.detail_message ?? '';
      const errorMessage = `${
        responseData?.error ?? responseData?.message ?? error.data ?? ''
      }${detailMessage}`;
      // 如果没有错误信息，则会根据状态码进行提示
      if (responseData.code === '401' || error.response.status === 401) {
        // 添加防抖判断
        if (!isShowingLoginExpired) {
          isShowingLoginExpired = true;
          ElMessageBox.alert(errorMessage || msg, '登录过期', {
            autofocus: false,
            showClose: false,
            confirmButtonText: '我知道了',
            type: 'warning',
          }).finally(() => {
            // 对话框关闭后重置状态
            isShowingLoginExpired = false;
          });
        }
      } else {
        if (!error.config.headers.hideErrorMessage) {
          ElMessage.warning(errorMessage || msg);
        }
      }
    }),
  );

  return client;
}

export const requestClient = createRequestClient(apiURL, {
  responseReturn: 'data',
});

export const baseRequestClient = new RequestClient({ baseURL: apiURL });
