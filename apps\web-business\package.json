{"name": "@vben/web-business", "version": "5.5.3", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-business"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "if-env NODE_ENV=production && pnpm run build:prod || (if-env NODE_ENV=pre && pnpm run build:pre) || (if-env NODE_ENV=test && pnpm run build:test)", "build:prod": "pnpm vite build --mode production", "build:pre": "pnpm vite build --mode pre", "build:test": "pnpm vite build --mode test", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "dayjs": "catalog:", "element-plus": "catalog:", "pinia": "catalog:", "qrcode": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/qrcode": "catalog:", "unplugin-element-plus": "catalog:"}}