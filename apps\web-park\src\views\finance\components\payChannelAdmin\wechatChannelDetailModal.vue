<script lang="ts" setup>
import type { NSPay } from '#/api';

import { ref, toRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm, z } from '#/adapter/form';

defineOptions({
  name: 'WechatChannelDetailModal', // 微信支付渠道详情弹窗
});

interface option {
  key: string;
  label: string;
  value: number;
}

const formState = ref<NSPay.IPayChannelDetailResult>({});
const stateOptions = ref<option[]>([]);
const mchTypeOptions = ref<option[]>([]);
const typeOptions = ref<option[]>([]);
const editSchema = [
  {
    component: 'Input',
    fieldName: 'channel_name',
    componentProps: {
      placeholder: '请输入支付渠道名称',
      clearable: true,
    },
    label: '支付渠道名称',
    rules: z.string().refine((val) => val, {
      message: '请输入支付渠道名称',
    }),
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: stateOptions,
    },
    fieldName: 'pay_channel_state',
    label: '支付渠道状态',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'wx_mch_id',
    componentProps: {
      placeholder: '请输入微信商户ID',
      clearable: true,
    },
    label: '微信商户ID',
    rules: z.string().refine((val) => val, {
      message: '请输入微信商户ID',
    }),
  },
  {
    component: 'Select',
    componentProps: {
      options: mchTypeOptions,
      clearable: true,
    },
    fieldName: 'wx_mch_type',
    label: '微信商户类型',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'wx_mch_name',
    componentProps: {
      placeholder: '请输入微信商户名',
      clearable: true,
    },
    label: '微信商户名',
    rules: z.string().refine((val) => val, {
      message: '请输入微信商户名',
    }),
  },
  {
    component: 'Select',
    componentProps: {
      options: typeOptions,
      clearable: true,
    },
    fieldName: 'wx_app_type',
    label: '微信应用类型',
    rules: 'selectRequired',
  },
  {
    component: 'Input',
    fieldName: 'wx_app_id',
    componentProps: {
      placeholder: '请输入微信应用APPID',
      clearable: true,
    },
    label: '微信应用APPID',
    rules: z.string().refine((val) => val, {
      message: '请输入微信应用APPID',
    }),
  },
  {
    component: 'Input',
    fieldName: 'wx_app_name',
    componentProps: {
      placeholder: '请输入微信应用名',
      clearable: true,
    },
    label: '微信应用名',
    rules: z.string().refine((val) => val, {
      message: '请输入微信应用名',
    }),
  },
  {
    component: 'Input',
    fieldName: 'wx_api_v3_private_key',
    componentProps: {
      placeholder: '请输入APIv3密钥',
      clearable: true,
    },
    label: 'APIv3密钥',
    rules: z.string().refine((val) => val, {
      message: '请输入APIv3密钥',
    }),
  },
  {
    component: 'Input',
    fieldName: 'wx_apiclient_key',
    componentProps: {
      placeholder: '请输入商户私钥',
      clearable: true,
    },
    label: '商户私钥',
    rules: z.string().refine((val) => val, {
      message: '请输入商户私钥',
    }),
  },
  {
    component: 'Input',
    fieldName: 'wx_apiclient_cert',
    componentProps: {
      placeholder: '请输入商户证书',
      clearable: true,
    },
    label: '商户证书',
    rules: z.string().refine((val) => val, {
      message: '请输入商户证书',
    }),
  },
];
const viewSchema = [
  {
    component: 'Text',
    fieldName: 'pay_channel_detail',
    label: '支付渠道',
  },
  {
    component: 'Text',
    fieldName: 'channel_name_detail',
    label: '支付渠道名称',
  },
  {
    component: 'Text',
    fieldName: 'pay_channel_state_display_detail',
    label: '支付渠道状态',
  },
  {
    component: 'Text',
    fieldName: 'wx_mch_id_detail',
    label: '微信商户ID',
  },
  {
    component: 'Text',
    fieldName: 'wx_mch_name_detail',
    label: '微信商户名',
  },
  {
    component: 'Text',
    fieldName: 'wx_mch_type_display_detail',
    label: '微信商户类型',
  },
  {
    component: 'Text',
    fieldName: 'wx_app_type_display_detail',
    label: '微信应用类型',
  },
  {
    component: 'Text',
    fieldName: 'wx_app_id_detail',
    label: '微信应用APPID',
  },
  {
    component: 'Text',
    fieldName: 'wx_app_name_detail',
    label: '微信应用名',
  },
  {
    component: 'Text',
    fieldName: 'wx_api_v3_private_key_detail',
    label: 'APIv3密钥',
  },
  {
    component: 'Text',
    fieldName: 'wx_apiclient_key_detail',
    label: '商户私钥',
  },
  {
    component: 'Text',
    fieldName: 'wx_apiclient_cert_detail',
    label: '商户证书',
  },
];

const [WechatChannelDetailForm, WCDFRef] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    labelWidth: 120,
    colon: true,
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  showDefaultActions: false,
  schema: [],
  wrapperClass: 'grid-cols-1',
});

/**
 * 退款详情弹窗配置
 */
const [Modal, ModalApi] = useVbenModal({
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const shareData = ModalApi.getData<Record<string, any>>();
      const { type, formData, channelData } = shareData;
      const { mchTypes, states, types } = channelData;
      stateOptions.value = states;
      mchTypeOptions.value = mchTypes;
      typeOptions.value = types;
      formState.value = formData;
      if (type === 'view') {
        WCDFRef.setState({ schema: viewSchema });
      } else {
        WCDFRef.setState({ schema: editSchema });
        WCDFRef.setValues({
          ...formData,
        });
      }
    } else {
      WCDFRef.resetForm();
    }
  },
  onConfirm: async () => {
    const res = await WCDFRef.validate();
    if (!res.valid) return;
    const shareData = ModalApi.getData<Record<string, any>>();
    const data = await WCDFRef.getValues();
    if (shareData.confirmFn) {
      shareData.confirmFn(toRaw(data));
    }
  },
});
</script>

<template>
  <!-- 微信支付渠道详情弹窗 -->
  <Modal :close-on-click-modal="false" class="w-[1200px]">
    <WechatChannelDetailForm>
      <template #pay_channel_detail>
        <span>微信</span>
      </template>
      <template #channel_name_detail>
        {{ formState.channel_name }}
      </template>
      <template #pay_channel_state_display_detail>
        {{ formState.pay_channel_state_display }}
      </template>
      <template #wx_mch_id_detail>
        {{ formState.wx_mch_id }}
      </template>
      <template #wx_mch_name_detail>
        {{ formState.wx_mch_name }}
      </template>
      <template #wx_mch_type_display_detail>
        {{ formState.wx_mch_type_display }}
      </template>
      <template #wx_app_type_display_detail>
        {{ formState.wx_app_type_display }}
      </template>
      <template #wx_app_id_detail>
        {{ formState.wx_app_id }}
      </template>
      <template #wx_app_name_detail>
        {{ formState.wx_app_name }}
      </template>
      <template #wx_api_v3_private_key_detail>
        {{ formState.wx_api_v3_private_key }}
      </template>
      <template #wx_apiclient_key_detail>
        <span class="break-all">
          {{ formState.wx_apiclient_key }}
        </span>
      </template>
      <template #wx_apiclient_cert_detail>
        <span class="break-all">
          {{ formState.wx_apiclient_cert }}
        </span>
      </template>
    </WechatChannelDetailForm>
  </Modal>
</template>
