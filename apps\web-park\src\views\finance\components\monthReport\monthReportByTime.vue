<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { NSEmployee } from '#/api';
import type { NSFinance } from '#/api/park/finance';

import { onMounted, ref } from 'vue';

import { useVbenModal, VbenCountToAnimator } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';
import { ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { CommonModule, FinanceApi } from '#/api';
import ParkSelectModalComponent from '#/components/park-select-modal.vue';

defineOptions({
  name: 'MonthReportByTime', // 按次月报表
});

interface IFormValues {
  park_id: string;
  park_name: string;
  time: string;
  park_types?: number[];
}

/**
 * 业务变量
 */
const parkTypeOptions = ref<CommonModule.IEnumItem[]>([]);
const payCountList = ref([
  { label: '应收', value: 0 },
  { label: '实收', value: 0 },
  { label: 'ETC支付', value: 0 },
  { label: '支付宝支付', value: 0 },
  { label: '微信支付', value: 0 },
  { label: '第三方会员收入', value: 0 },
  { label: '现金支付', value: 0 },
  { label: '特殊处理', value: 0 },
  { label: '优免抵扣', value: 0 },
  { label: '特殊处理损失', value: 0 },
  { label: '被冲车辆损失', value: 0 },
  { label: '手动抬杆', value: 0 },
]);
const userStore = useUserStore();
const isReset = ref(false);

/**
 * 查询参数
 */
// 临时存储当前已经选择的车场数据
const selectParkCheck = ref<NSEmployee.IEmployeeParkListResult | null>(null);

/**
 * 注册车场选择弹窗
 * @description 使用关联方式关联车场选择公共组件
 */
const [ParkSelectModal, PSModalRef] = useVbenModal({
  connectedComponent: ParkSelectModalComponent,
});

/**
 * 打开车场选择弹窗
 */

const openParkSelectModalForm = () => {
  PSModalRef.setState({ title: '车场选择' });
  PSModalRef.setData({
    isMultiple: false,
    selectArray: selectParkCheck.value ? [selectParkCheck.value] : [],
    confirmFn: async (newSelectArray: NSEmployee.IEmployeeParkListResult[]) => {
      selectParkCheck.value = newSelectArray[0] || null;
      // eslint-disable-next-line no-use-before-define
      MRBTRef.formApi.setFieldValue('park_id', newSelectArray[0]?.park_name);
      PSModalRef.close();
    },
  });
  PSModalRef.open();
};

const onReset = () => {
  // eslint-disable-next-line no-use-before-define
  MRBTRef.formApi.resetForm();
  selectParkCheck.value = null;
  isReset.value = true;
  // eslint-disable-next-line no-use-before-define
  MRBTRef.reload();
};

/**
 * 搜索表单配置
 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'park_id',
      label: '车场：',
      componentProps: {
        clearable: true,
        placeholder: '请选择车场',
        readonly: true,
        onClick: () => {
          openParkSelectModalForm();
        },
      },
    },
    {
      component: 'DatePicker',
      defaultValue: dayjs().format('YYYY-MM'),
      fieldName: 'time',
      label: '月份：',
      componentProps: {
        clearable: false,
        type: 'month',
        style: {
          width: 'auto',
        },
        placeholder: '月份',
      },
    },
    {
      component: 'Select',
      fieldName: 'park_types',
      label: '车辆属性：',
      componentProps: {
        multiple: true,
        clearable: true,
        options: parkTypeOptions,
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  // 表单布局
  wrapperClass: 'md:grid-cols-4',
  // 自定义重置表单方法
  handleReset: onReset,
};

/**
 * 表格配置
 * @description停车缴费管理列表
 */
const gridOptions: VxeGridProps<NSFinance.IPagingDayReportsRow> = {
  // 表格开启勾选配置 开启高亮
  checkboxConfig: {
    highlight: true,
  },
  // 表格行配置 开启行高亮 开启行hover 开启行选中
  rowConfig: {
    height: 50,
    isCurrent: true,
    isHover: true,
  },
  // 开启表格溢出  自动为全局的字段开启表格溢出(文字...) 显示方式  关闭则自动换行
  showOverflow: true,
  // 开启表格溢出 列头
  showHeaderOverflow: true,
  // 表格高度 自动
  height: 'auto',
  // 保持原始数据 强制开启
  keepSource: true,
  // 前端导出按钮 暂时保留
  exportConfig: {
    modes: ['current'],
  },
  // 打印配置 打印当前页
  printConfig: {
    modes: ['current'],
  },
  // 工具栏配置 自定义 缩放 导出 刷新
  toolbarConfig: {
    custom: true,
    export: true,
    // 打印 暂时关闭
    // print: true,
    refresh: true,
    // @ts-ignore
    search: true,
    zoom: true,
  },
  // 分页配置 默认页码1 每页20条  如果页面接口查询慢后端无法优化可以调整到10条
  pagerConfig: {
    currentPage: 1,
    pageSize: 20,
  },
  // 正常配置列
  columns: [
    {
      field: 'time',
      title: '月份',
      fixed: 'left',
      minWidth: 200,
    },
    {
      field: 'park_name',
      title: '停车场名称',
      fixed: 'left',
      minWidth: 200,
    },
    { field: 'park_id', title: '车场ID', sortable: true, minWidth: 100 },
    { field: 'location', title: '所在地', minWidth: 200 },
    {
      field: 'should_pay_money_cnt',
      title: '应收（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'payed_money_cnt',
      title: '实收（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'electronic_money_cnt',
      title: '电子支付（次）',
      sortable: true,
      minWidth: 200,
    },
    // {
    //   field: 'ali_money_cnt',
    //   title: '线上交易（次）',
    //   sortable: true,
    //   minWidth: 200,
    // },
    {
      field: 'ali_money',
      title: '线上交易（元）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'etc_money_cnt',
      title: 'ETC支付（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'ali_money_cnt',
      title: '支付宝支付（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'wx_money_cnt',
      title: '微信支付（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'parking_third_party_num',
      title: '第三方会员收入（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'cash_money_cnt',
      title: '现金支付（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'special_money_cnt',
      title: '特殊处理（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'debate_money_cnt',
      title: '优免抵扣（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'special_loss_money_cnt',
      title: '特殊处理损失（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'flush_loss_money_cnt',
      title: '被冲车辆损失（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'manual_money_cnt',
      title: '手动抬杆（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'park_in_num',
      title: '入场临停（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'rent_in_num',
      title: '入场长租（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'park_out_num',
      title: '出场临停（次）',
      sortable: true,
      minWidth: 200,
    },
    {
      field: 'rent_out_num',
      title: '出场长租（次）',
      sortable: true,
      minWidth: 200,
    },
  ],
  // 使用接口方式获取数据，类似Jpaas的rxGrid：url方式
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: getPagingMonthReports,
    },
  },
};

/**
 * 表格事件
 */
// const gridEvents: VxeGridListeners = {
//   checkboxChange({ row }) {
//     console.error(row);
//   },
// };

/**
 * 初始化表格的组件实例与Api实例
 * @description 使用formOptions与gridOptions、gridEvents进行配置
 * 如果没有复选框可以不用配置事件
 */
const [MonthReportByTimeTable, MRBTRef] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  // gridEvents,
});

/**
 *  获取停车缴费金额数目信息
 */
const getPagingMonthTotal = async ({ page }: any, formValues: IFormValues) => {
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
  };
  if (isReset.value) {
    delete params.park_types;
  }
  try {
    const res = await FinanceApi.getPagingMonthTotalApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    const data = [
      res.total_should_pay_money_cnt,
      res.total_payed_money_cnt,
      ...res.vos.map((item) => {
        return item.total_count;
      }),
    ];
    payCountList.value = payCountList.value.map((item, index) => {
      return {
        ...item,
        value: data[index]!,
      };
    });
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
};

/**
 * 获取停车缴费列表数据
 */
async function getPagingMonthReports({ page }: any, formValues: IFormValues) {
  getPagingMonthTotal({ page }, formValues!);
  // 查询参数格式化
  const params = {
    ...formValues,
    park_id: selectParkCheck.value?.park_id || '',
    park_name: selectParkCheck.value?.park_name || '',
  };
  if (isReset.value) {
    delete params.park_types;
    isReset.value = false;
  }
  try {
    const res = await FinanceApi.getPagingMonthReportsApi({
      page: page.currentPage,
      limit: page.pageSize,
      ...params,
    });
    return {
      items: res.rows,
      total: Number(res.total) || 0,
    };
  } catch {
    return {
      items: [],
      total: 0,
    };
  }
}

const loadData = () => {
  MRBTRef.setLoading(true);
  setTimeout(() => {
    MRBTRef.setLoading(false);
    MRBTRef.query();
  }, 200);
};

onMounted(() => {
  const user = userStore.userInfo?.userEntity;
  if (user.park_ids !== undefined && user.park_ids.length === 1) {
    selectParkCheck.value = {
      park_id: user.park_ids[0],
      park_name: user.park_names[0],
    };
    MRBTRef.formApi.setFieldValue('park_id', user.park_names[0]);
    loadData();
  }
});

defineExpose({ parkTypeOptions });
</script>
<template>
  <!--按次月报表 -->
  <div class="h-full">
    <MonthReportByTimeTable>
      <!-- 表格顶部左侧按钮栏 -->
      <template #toolbar-actions>
        <div class="flex flex-wrap gap-4 text-center font-bold">
          <div
            v-for="(item, index) in payCountList"
            :key="index"
            class="flex items-center"
          >
            <ElTag v-if="index === 2" type="danger" size="small" class="mr-1">
              渠道
            </ElTag>
            <span class="pr-1 font-normal">{{ item.label }}:</span>
            <VbenCountToAnimator
              :decimals="0"
              :duration="1000"
              :end-val="item.value"
              :start-val="0"
              color="#F56C6C"
              suffix="次"
            />
          </div>
        </div>
      </template>
    </MonthReportByTimeTable>
    <!-- 车场选择弹窗 -->
    <ParkSelectModal />
  </div>
</template>
