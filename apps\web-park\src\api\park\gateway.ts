import { requestClient } from '#/api/request';

export namespace NSParkGateway {
  export interface IListParkGatewayItem {
    id?: string;
    name?: string;
  }
}

/**
 * 查询车场的入/出场通道
 */
export const getListParkGatewayApi = (parkRegionId: string) => {
  return requestClient
    .get<
      NSParkGateway.IListParkGatewayItem[]
    >(`/console/park/gateway/listParkGateway/${parkRegionId}`)
    .then((res) => {
      return res.map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    });
};
