<script lang="ts" setup>
/**
 * @description 卡片组件
 * @component CommonCard
 *
 * 功能：
 * 1. 包含标题和内容
 * 2. 支持标题图标和标题文本
 * 3. 支持内容插槽
 */
interface Props {
  title: string;
  isShowBorder?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  isShowBorder: false,
});
</script>

<template>
  <div
    :class="{
      'border border-dashed border-[#1570FF]/[0.8]': props.isShowBorder,
    }"
    class="flex h-full flex-col rounded-lg bg-[hsl(var(--background))] p-4"
  >
    <div class="flex items-center">
      <img alt="标题图标" src="/static/card-title-icon.svg" />
      <div class="ml-1 text-base font-bold">{{ props.title }}</div>
      <div class="mx-auto">
        <slot name="title-right"></slot>
      </div>
    </div>
    <!-- 内容容器 -->
    <slot></slot>
  </div>
</template>
