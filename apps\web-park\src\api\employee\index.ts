import { requestClient } from '#/api/request';

export namespace NSEmployee {
  export interface IDeleteEmployeesParams {
    id: number[];
    role_id: number[];
  }

  export interface ICreateEmployeeParams {
    department_id: string;
    department_name: string;
    enabled: '0' | '1';
    id?: string;
    login_name: string;
    mobile: string;
    name: string;
    role_id: string[];
  }

  export interface IEmployeeParkListResult {
    park_id: string;
    park_name: string;
    app_auth_token?: string;
    prk_park_code?: string;
    prk_park_id?: string;
    prk_park_name?: string;
    sub_appid?: string;
    sub_mchid?: string;
  }

  export interface IUpdateEmployeeParkingAuthorityParams {
    employee_id: string;
    park_id: string[];
    park_name: string[];
  }
}

/**
 * 显示消息配置
 * @description 配置请求头
 */
const showMessageConfig = {
  headers: {
    showMessage: true,
  },
};
/**
 * 创建员工
 * @param params 创建员工实体
 */
async function createEmployeeApi(params: NSEmployee.ICreateEmployeeParams) {
  return requestClient.post<any>(
    '/console/employee/createEmployee',
    params,
    showMessageConfig,
  );
}

/**
 * 删除员工
 * @description 批量删除员工/单个 单个删除传单数组
 * @param params 删除员工实体
 */
async function deleteEmployeesApi(params: NSEmployee.IDeleteEmployeesParams) {
  return requestClient.post<null | undefined>(
    '/console/employee/deleteEmployees',
    params,
    showMessageConfig,
  );
}

/**
 * 禁用员工
 * @param id 员工ID
 */
async function disableEmployeeApi(id: number) {
  return requestClient.post<null | undefined>(
    `/console/employee/disableEmployee/${id}`,
    {},
    showMessageConfig,
  );
}

/**
 * 启用员工
 * @param id 员工ID
 */
async function enableEmployeeApi(id: number) {
  return requestClient.post<null | undefined>(
    `/console/employee/enableEmployee/${id}`,
    {},
    showMessageConfig,
  );
}

/**
 * 获取员工详情
 * @param id 员工ID
 */
async function getEmployeeByIdApi(id: number) {
  return requestClient.post<any>(`/console/employee/getEmployeeById/${id}`);
}

/**
 * 根据条件搜索万信人员和信息
 * @param iamToken 万信token
 * @param userNo 万信人员编号
 * @param fullName 万信人员姓名
 */
async function getUserInfoApi(
  iamToken: string,
  userNo: string,
  fullName: string,
) {
  return requestClient.get<Record<string, any>[]>(
    `/console/employee/getUserInfo?token=${iamToken}&userNo=${userNo}&fullName=${fullName}`,
  );
}

/**
 * 获取员工列表
 * @description 分页获取员工列表
 * @param params 分页参数/查询参数
 */
async function pagingEmployeesApi(params: any) {
  return requestClient.post<any>('/console/employee/pagingEmployees', params);
}

/**
 * 重置密码
 * @param id 员工ID
 */
async function resetPasswordApi(id: number) {
  return requestClient.post<null | undefined>(
    `/console/employee/resetPassword/${id}`,
    {},
    showMessageConfig,
  );
}

/**
 * 更新员工信息
 * @param params 更新员工实体
 */
async function updateEmployeeApi(params: NSEmployee.ICreateEmployeeParams) {
  return requestClient.post<null | undefined>(
    '/console/employee/updateEmployee',
    params,
    showMessageConfig,
  );
}

/**
 * 获取员工分管车场列表
 * @param id 员工ID
 */
async function getEmployeeParkListApi(id: number) {
  return requestClient.post<NSEmployee.IEmployeeParkListResult[]>(
    `/console/employee/employeeParkList/${id}`,
  );
}

/**
 * 更新员工的车场授权
 * @param params 更新员工实体
 */
async function updateEmployeeParkingAuthorityApi(
  params: NSEmployee.IUpdateEmployeeParkingAuthorityParams,
) {
  return requestClient.post<null | undefined>(
    `/console/employee/parkingAuthority`,
    params,
    showMessageConfig,
  );
}

export const EmployeeApi = {
  createEmployeeApi,
  deleteEmployeesApi,
  disableEmployeeApi,
  enableEmployeeApi,
  getEmployeeByIdApi,
  getEmployeeParkListApi,
  getUserInfoApi,
  pagingEmployeesApi,
  resetPasswordApi,
  updateEmployeeApi,
  updateEmployeeParkingAuthorityApi,
};
